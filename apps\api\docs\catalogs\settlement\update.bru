meta {
  name: update
  type: http
  seq: 7
}

put {
  url: {{base}}/catalogs/settlement/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 
}

headers {
  authorization: 
}

body:json {
  {
    "key": "test_key_change", // optional
    "name": "Cambio en nombre de prueba", // optional
    "description": "Cambio en descripcion de Nombre de prueba", // optional
    "zipCode": "49000",
    "fkStateId": "uuid Estado",
    "fkMunicipalityId": "uuid Municipio"
  }
}
