import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/exam_result.controller';

const catExamResultRouter = Router();

catExamResultRouter.get('/', getAll);
catExamResultRouter.get('/search', searchPaginated);
catExamResultRouter.get('/:id', getOne);
catExamResultRouter.post('/', create);
catExamResultRouter.put('/:id', update);
catExamResultRouter.delete('/:id', deleteOne);

export default catExamResultRouter;
