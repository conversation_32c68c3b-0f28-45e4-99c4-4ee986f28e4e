meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/control/uniform/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 
}

body:json {
  {
    "serialNumber": "Número de Serie",
    "fkUniformTypeId": "ID Tipo de uniforme",
    "fkSizeId": "ID Tamaño", 
    "fkColorId": "ID Color",
    "status": "Estado del uniforme", // ['new', 'in_use', 'damaged', 'lost']
    "expirationDate": "Fecha de expiración", // Opcional
    "acquisitionDate": "Fecha de compra/adquisición",
    "cost": "Costo",
    "fkSupplierId": "ID Proveedor", // Opcional
    "description": "Descripción" // Opcional
    
  }
}
