meta {
  name: create
  type: http
  seq: 5
}

post {
  url: {{base}}/assignments/fatigue?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

body:json {
  /*{
    "fkUserId": "ID Usuario",
    "fkFatigueId": "ID Fatiga/Despliegue",
    "fkArrayCategoryIds": ["ID Categoria 1", "ID Categoria 2"],
    "fkArrayPlaceIds": ["ID Lugar 1", "ID Lugar 2"],
    "fkArrayQuadrantsIds": ["ID Cuadrante/Sector", "ID Cuadrante/Sector"], // Opcional
    "fkUnitObject": {
      "fkUnitControlId": "ID Unidad Vehicular",
      "isDriver": false,
      "isPassenger": false,
      "isInCharge": false
    }
  }*/
  {
    "id": "new",
    "fkUserId": "01953e99-2dc5-2b69-6889-4ba50bfb074b",
    "fkFatigueId": "019601c9-a916-a9d1-1862-c26ac92f4dbd",
    "fkArrayCategoryIds": ["01963f81-40b5-1146-9ce8-e14e0604c353"],
    "fkArrayPlaceIds": [],
    "fkArrayQuadrantsIds": [],
    "fkUnitObject": null
  }
}
