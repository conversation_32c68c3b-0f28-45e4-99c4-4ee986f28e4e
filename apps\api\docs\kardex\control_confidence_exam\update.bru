meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/kardex/control_confidence_exam/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 
}

body:json {
  {
    "fkUserId": "ID Usuario",
    "evaluationType": "Tipo de Evaluación", // ['physical', 'psychological', 'medical', 'technical']
    "evaluationDate": "Fecha de la Evaluación",
    "qualitativeResult": "Resultado cualitativo", // Opcional - Texto
    "quantitativeResult": "Resultado cuantitativo", // Opcional - Númerico
    "fkEvaluatorUserId": "ID Uusuario que Evalua",
    "isMandatory": false, // Indica si la evaluación es obligatoria
    "estimatedNextEvaluationDate": "Fecha estimada de la próxima evaluación",
    "observations": "Observaciones" // Opcional
  }
}
