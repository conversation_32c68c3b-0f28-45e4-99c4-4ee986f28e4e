import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import {
  assignUniformBodySchema,
  handleUserChangeInUniformAssign,
  validateUniformAssignmentInFatigue,
  validateDates,
  validateUniformActiveAssignment,
} from '../helpers/uniform.helper';
import SystemModuleCache from '@/utils/system_module_cache';
import {
  catUniformType,
  systemModuleAssignmentUniform,
  systemModuleControlFatigue,
  systemModuleControlUniform,
} from '@repo/shared-drizzle/schemas';
import { getLocale } from '@/utils/locale_validator';
import moment from 'moment';

type UniformAssignGrouped = Record<
  string,
  {
    fkUserId: string;
    data: {
      id: string;
      fkUniformId: string;
      nameUniform: string | null;
      fkFatigueId: string;
      assignmentDate: string;
      assignmentEndDate: string | null;
      estimatedReturnDate: string | null;
      periodUse: number | null;
      comments: string | null;
    }[];
  }
>;

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS, grouped: groupedQS } = req.query;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let grouped = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentUniform.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentUniform.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentUniform.id,
        fkUserId: systemModuleAssignmentUniform.fkUserId,
        fkUniformId: systemModuleAssignmentUniform.fkUniformId,
        nameUniform: catUniformType[locale],
        fkFatigueId: systemModuleAssignmentUniform.fkFatigueId,
        assignmentDate: systemModuleAssignmentUniform.assignmentDate,
        assignmentEndDate: systemModuleAssignmentUniform.assignmentEndDate,
        estimatedReturnDate: systemModuleAssignmentUniform.estimatedReturnDate,
        periodUse: systemModuleAssignmentUniform.periodUse,
        comments: systemModuleAssignmentUniform.comments,
      })
      .from(systemModuleAssignmentUniform)
      .leftJoin(
        systemModuleControlUniform,
        eq(systemModuleAssignmentUniform.fkUniformId, systemModuleControlUniform.id),
      )
      .leftJoin(catUniformType, eq(systemModuleControlUniform.fkUniformTypeId, catUniformType.id))
      .where(and(...filters));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkUniformId: rest.fkUniformId,
            nameUniform: rest.nameUniform || null,
            fkFatigueId: rest.fkFatigueId,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            estimatedReturnDate: rest.estimatedReturnDate || null,
            periodUse: rest.periodUse || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as UniformAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIFORM}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const result = await db
      .select({
        id: systemModuleAssignmentUniform.id,
        fkUserId: systemModuleAssignmentUniform.fkUserId,
        fkUniformId: systemModuleAssignmentUniform.fkUniformId,
        nameUniform: catUniformType[locale],
        fkFatigueId: systemModuleAssignmentUniform.fkFatigueId,
        assignmentDate: systemModuleAssignmentUniform.assignmentDate,
        assignmentEndDate: systemModuleAssignmentUniform.assignmentEndDate,
        estimatedReturnDate: systemModuleAssignmentUniform.estimatedReturnDate,
        periodUse: systemModuleAssignmentUniform.periodUse,
        comments: systemModuleAssignmentUniform.comments,
      })
      .from(systemModuleAssignmentUniform)
      .leftJoin(
        systemModuleControlUniform,
        eq(systemModuleAssignmentUniform.fkUniformId, systemModuleControlUniform.id),
      )
      .leftJoin(catUniformType, eq(systemModuleControlUniform.fkUniformTypeId, catUniformType.id))
      .where(eq(systemModuleAssignmentUniform.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIFORM}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar assignmentDate
    const date = new Date(req.body.assignmentDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('El campo assignmentDate no es una fecha valida');
    }

    // Validar estimatedReturnDate
    if (req.body.estimatedReturnDate) {
      const dateEnd = new Date(req.body.estimatedReturnDate.replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(dateEnd.getTime())) {
        throw new Error('El campo estimatedReturnDate no es una fecha valida');
      }
      if (dateEnd < date) {
        throw new Error('La fecha estimada de retorno no puede ser anterior a la fecha de inicio');
      }
    }

    // Quitar del payload el campo assignmentEndDate, no considerar al crear la asignación
    const { assignmentEndDate, ...rest } = req.body;

    // Validar payload
    const insertData = await assignUniformBodySchema.parseAsync({ ...rest });
    const result = await db.transaction(async (trx) => {
      // Validar la existencia de uniforme
      const uniform = await trx
        .select()
        .from(systemModuleControlUniform)
        .where(eq(systemModuleControlUniform.id, insertData.fkUniformId))
        .limit(1);
      if (uniform.length === 0) {
        throw new Error('Uniforme no encontrado');
      }

      // Validar que no exista una asignación con el mismo uniforme en el despliegue indicado
      const existingAssignment = await trx
        .select()
        .from(systemModuleAssignmentUniform)
        .where(
          and(
            eq(systemModuleAssignmentUniform.fkUniformId, insertData.fkUniformId),
            eq(systemModuleAssignmentUniform.fkFatigueId, insertData.fkFatigueId),
          ),
        )
        .limit(1);
      if (existingAssignment.length > 0) {
        throw new Error('Ya existe una asignación con el mismo uniforme en el despliegue indicado');
      }

      const systemModuleCache = SystemModuleCache.getInstance();
      const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_ASIGNACIONES_UNIFORMES);
      if (!systemModule) {
        throw new Error('System module CTRL_ASIGNACIONES_UNIFORMES not found');
      }
      const newAssignment = await trx.insert(systemModuleAssignmentUniform).values({
        ...insertData,
        assignmentEndDate: null,
        fkSystemModuleId: systemModule.id,
      });
      /*
      // Registrar kardex de uniforme (equipo táctico)
      const insertTacticalEquipmentKardex = await kardexTacticalEquipmentBodySchema.parseAsync({
        fkUserId: insertData.fkUserId,
        fkTacticalEquipmentId: uniform[0].id,
        assignmentDate: insertData.assignmentDate,
        assignmentEndDate: insertData.assignmentEndDate,
        expirationDate: insertData.expirationDate,
        estimatedReturnDate: insertData.estimatedReturnDate,
        fkSizeId: uniform[0].fkSizeId,
        tacticalEquipmentStatus: 'in_use',
        fkLastUserId: insertData.fkLastUserId,
        fkLastDependencyId: insertData.fkLastDependencyId,
      });
      await trx.insert(systemModuleUserKardexTacticalEquipment).values({ ...insertTacticalEquipmentKardex });
      // Actualizar estatus de uniforme
      await trx
        .update(systemModuleControlUniform)
        .set({ status: 'in_use' })
        .where(eq(systemModuleControlUniform.id, insertData.fkUniformId));*/
      // Registrar asignación de uniforme
      return newAssignment;
    });

    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIFORM} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar fechas
    validateDates(req.body);

    // Validar payload
    const updateData = await assignUniformBodySchema.parseAsync({ ...req.body });

    const result = await db.transaction(async (trx) => {
      // Obtener registro de asignación
      const assignment = await trx
        .select()
        .from(systemModuleAssignmentUniform)
        .where(eq(systemModuleAssignmentUniform.id, id))
        .limit(1);

      if (assignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar la existencia del uniforme
      const uniform = await trx
        .select()
        .from(systemModuleControlUniform)
        .where(eq(systemModuleControlUniform.id, updateData.fkUniformId))
        .limit(1);

      if (uniform.length === 0) {
        throw new Error('Uniforme no encontrado');
      }

      // Validar si cambio de arma, que no este en el mismo despliegue ya asignado
      if (updateData.fkUniformId !== assignment[0].fkUniformId) {
        await validateUniformAssignmentInFatigue(trx, updateData.fkUniformId, updateData.fkFatigueId, id);
      }

      // Validar si el uniforme tiene una asignación activa
      await validateUniformActiveAssignment(trx, updateData.fkUniformId, id);

      // Verificar si el usuario cambió
      if (updateData.fkUserId !== assignment[0].fkUserId) {
        await handleUserChangeInUniformAssign(
          trx,
          updateData.fkUserId,
          updateData.fkUniformId,
          updateData.fkFatigueId,
          id,
        );
      }

      // Calcular el periodo de uso si se proporciona assignmentEndDate
      let periodUse = assignment[0].periodUse;
      if (updateData.assignmentEndDate) {
        const diffDays = moment(updateData.assignmentEndDate).diff(moment(updateData.assignmentDate), 'days');
        periodUse = diffDays;
      }

      // Actualizar asignación
      const assignUpdate = await trx
        .update(systemModuleAssignmentUniform)
        .set({
          ...updateData,
          periodUse,
        })
        .where(eq(systemModuleAssignmentUniform.id, id));

      //await handleKardexUpdate(trx, updateData, assignment[0], uniform[0]);

      return assignUpdate;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIFORM}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Validar que exista la asignación
      const existingAssignment = await trx
        .select()
        .from(systemModuleAssignmentUniform)
        .where(eq(systemModuleAssignmentUniform.id, id))
        .limit(1);
      if (existingAssignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar que la asignación no este en un despliegue activo
      const activeUniformAssignment = await trx
        .select()
        .from(systemModuleAssignmentUniform)
        .leftJoin(
          systemModuleControlFatigue,
          eq(systemModuleAssignmentUniform.fkFatigueId, systemModuleControlFatigue.id),
        )
        .where(
          and(
            eq(systemModuleControlFatigue.status, 'active'),
            eq(systemModuleAssignmentUniform.fkFatigueId, existingAssignment[0].fkFatigueId),
            eq(systemModuleAssignmentUniform.id, id),
          ),
        )
        .limit(1);

      if (activeUniformAssignment.length > 0) {
        throw new Error('No es posible eliminar la asignación si este ya se encuentra en un despliegue activo');
      }

      const deleteResult = await trx
        .update(systemModuleAssignmentUniform)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleAssignmentUniform.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIFORM}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    locale: localeQS,
    q: qQS,
    grouped: groupedQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let q = null;
  let grouped = false;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentUniform.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentUniform.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(catUniformType[locale], `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentUniform.assignmentDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentUniform.assignmentEndDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentUniform.estimatedReturnDate}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleAssignmentUniform.comments, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleAssignmentUniform.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentUniform.id,
        fkUserId: systemModuleAssignmentUniform.fkUserId,
        fkUniformId: systemModuleAssignmentUniform.fkUniformId,
        nameUniform: catUniformType[locale],
        fkFatigueId: systemModuleAssignmentUniform.fkFatigueId,
        assignmentDate: systemModuleAssignmentUniform.assignmentDate,
        assignmentEndDate: systemModuleAssignmentUniform.assignmentEndDate,
        estimatedReturnDate: systemModuleAssignmentUniform.estimatedReturnDate,
        periodUse: systemModuleAssignmentUniform.periodUse,
        comments: systemModuleAssignmentUniform.comments,
      })
      .from(systemModuleAssignmentUniform)
      .leftJoin(
        systemModuleControlUniform,
        eq(systemModuleAssignmentUniform.fkUniformId, systemModuleControlUniform.id),
      )
      .leftJoin(catUniformType, eq(systemModuleControlUniform.fkUniformTypeId, catUniformType.id))
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleAssignmentUniform.id));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkUniformId: rest.fkUniformId,
            nameUniform: rest.nameUniform || null,
            fkFatigueId: rest.fkFatigueId,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            estimatedReturnDate: rest.estimatedReturnDate || null,
            periodUse: rest.periodUse || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as UniformAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIFORM} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
