{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "launch",
      "name": "dev:backend:api",
      "runtimeExecutable": "pnpm",
      "experimentalNetworking": "off",
      "runtimeArgs": ["run-script", "dev"],
      "skipFiles": ["<node_internals>/**"],
      "cwd": "${workspaceFolder}/apps/api",
      "env": {
        "NODE_ENV": "development"
      }
    },
    {
      "type": "node",
      "request": "launch",
      "name": "dev:backend:auth",
      "runtimeExecutable": "pnpm",
      "experimentalNetworking": "off",
      "runtimeArgs": ["run-script", "dev"],
      "skipFiles": ["<node_internals>/**"],
      "cwd": "${workspaceFolder}/apps/auth",
      "env": {
        "NODE_ENV": "development"
      }
    },
    {
      "type": "node",
      "request": "launch",
      "name": "dev:backend:frontend",
      "runtimeExecutable": "pnpm",
      "experimentalNetworking": "off",
      "runtimeArgs": ["run-script", "dev"],
      "skipFiles": ["<node_internals>/**"],
      "cwd": "${workspaceFolder}/apps/frontend",
      "env": {
        "NODE_ENV": "development"
      }
    },
    {
      "type": "node",
      "request": "launch",
      "name": "dev:backend:cad",
      "runtimeExecutable": "pnpm",
      "experimentalNetworking": "off",
      "runtimeArgs": ["run-script", "dev"],
      "skipFiles": ["<node_internals>/**"],
      "cwd": "${workspaceFolder}/apps/cad",
      "env": {
        "NODE_ENV": "development"
      }
    }
  ]
}
