import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/weapon_caliber.controller';

const catWeaponCaliberRouter = Router();

catWeaponCaliberRouter.get('/', getAll);
catWeaponCaliberRouter.get('/search', searchPaginated);
catWeaponCaliberRouter.get('/:id', getOne);
catWeaponCaliberRouter.post('/', create);
catWeaponCaliberRouter.put('/:id', update);
catWeaponCaliberRouter.delete('/:id', deleteOne);

export default catWeaponCaliberRouter;
