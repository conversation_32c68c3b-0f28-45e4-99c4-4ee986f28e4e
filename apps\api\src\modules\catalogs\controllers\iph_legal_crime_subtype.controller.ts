import type { Request, Response } from 'express';
import { catIphLegalCrimeSubtype } from '@repo/shared-drizzle/schemas';
import db from '@/db';
import { and, asc, eq, gt, like, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { getLocale } from '@/utils/locale_validator';
import { HttpStatus } from '@/utils/http_status';
import { z } from 'zod';

export const getAll = async (req: Request, res: Response) => {
  const { type: typeParam } = req.params;
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS } = req.query;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();

  const typeSchema = z.string().uuid();

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const type = typeSchema.parse(typeParam);
    const filters: SQL[] = [eq(catIphLegalCrimeSubtype.fkIphLegalCrimeTypeId, type)];
    if (!deleted) {
      filters.push(eq(catIphLegalCrimeSubtype.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(catIphLegalCrimeSubtype.isEnabled, true));
    }
    const result = await db
      .select({
        id: catIphLegalCrimeSubtype.id,
        key: catIphLegalCrimeSubtype.key,
        name: catIphLegalCrimeSubtype[locale],
        fkIphLegalCrimeTypeId: catIphLegalCrimeSubtype.fkIphLegalCrimeTypeId,
        description: catIphLegalCrimeSubtype.description,
      })
      .from(catIphLegalCrimeSubtype)
      .where(and(...filters));
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'iph_legal_crime_subtype/all - getAll',
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const result = await db
      .select({
        id: catIphLegalCrimeSubtype.id,
        key: catIphLegalCrimeSubtype.key,
        name: catIphLegalCrimeSubtype[locale],
        fkIphLegalCrimeTypeId: catIphLegalCrimeSubtype.fkIphLegalCrimeTypeId,
        description: catIphLegalCrimeSubtype.description,
      })
      .from(catIphLegalCrimeSubtype)
      .where(eq(catIphLegalCrimeSubtype.id, id));
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `iph_legal_crime_subtype/${id} - getOne`,
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

// TODO: Add in catalog schemas column to defina defauilt value or not deletable value, add an integer column to indicate the popularity of the row
export const create = async (req: Request, res: Response) => {
  const bodySchema = z.object({
    key: z.string(),
    name: z.string(),
    description: z.string().optional(),
    fkIphLegalCrimeTypeId: z.string().uuid(),
    fkLastUserId: z.string(),
    fkLastDependencyId: z.string(),
  });

  const { locale: localeQS } = req.query;

  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const { name, ...insertData } = await bodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const result = await db.insert(catIphLegalCrimeSubtype).values({ [locale]: name, ...insertData });
    res.status(HttpStatus.CREATED).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'iph_legal_crime_subtype - create',
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const update = async (req: Request, res: Response) => {
  const bodySchema = z.object({
    key: z.string().optional(),
    name: z.string().optional(),
    description: z.string().optional(),
    fkIphLegalCrimeTypeId: z.string().uuid().optional(),
    fkLastUserId: z.string().uuid(),
    fkLastDependencyId: z.string().uuid(),
  });

  const { locale: localeQS } = req.query;
  const { id } = req.params;

  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const { name, ...updateData } = await bodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const result = await db
      .update(catIphLegalCrimeSubtype)
      .set({ ...updateData, [locale]: name })
      .where(eq(catIphLegalCrimeSubtype.id, id));
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `iph_legal_crime_subtype/${id} - update`,
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .update(catIphLegalCrimeSubtype)
      .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
      .where(eq(catIphLegalCrimeSubtype.id, id));

    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `iph_legal_crime_subtype/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const { type: typeParam } = req.params;
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    fkIphLegalCrimeTypeId: fkIphLegalCrimeTypeIdQS,
    locale: localeQS,
    q: qQS,
  } = req.query;

  const typeSchema = z.string().uuid();

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let fkIphLegalCrimeTypeId: string | undefined = undefined;
  let locale = getLocale();
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (fkIphLegalCrimeTypeIdQS && typeof fkIphLegalCrimeTypeIdQS === 'string') {
    fkIphLegalCrimeTypeId = fkIphLegalCrimeTypeIdQS;
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const type = typeSchema.parse(typeParam);
    const filters: SQL[] = [eq(catIphLegalCrimeSubtype.fkIphLegalCrimeTypeId, type)];
    if (!deleted) {
      filters.push(eq(catIphLegalCrimeSubtype.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(catIphLegalCrimeSubtype.isEnabled, true));
    }
    if (fkIphLegalCrimeTypeId) {
      filters.push(eq(catIphLegalCrimeSubtype.fkIphLegalCrimeTypeId, fkIphLegalCrimeTypeId));
    }
    if (q) {
      filters.push(like(catIphLegalCrimeSubtype[locale], `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(catIphLegalCrimeSubtype.id, lastId));
    }
    const result = await db
      .select({
        id: catIphLegalCrimeSubtype.id,
        key: catIphLegalCrimeSubtype.key,
        name: catIphLegalCrimeSubtype[locale],
        fkIphLegalCrimeTypeId: catIphLegalCrimeSubtype.fkIphLegalCrimeTypeId,
        description: catIphLegalCrimeSubtype.description,
      })
      .from(catIphLegalCrimeSubtype)
      .where(and(...filters))
      .limit(limit)
      .orderBy(asc(catIphLegalCrimeSubtype.id));

    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'iph_legal_crime_subtype - searchPaginated',
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};
