meta {
  name: update
  type: http
  seq: 4
}

put {
  url: {{cad}}/alert/configs/:id
  body: json
  auth: inherit
}

params:path {
  id: 
}

body:json {
  {
    "type": "Tipo de configuración", // Obligatorio - NOTA: Dependiendo el tipo son las validaciones que se realizaran al campo properties - ['safe_place','headquarters','safe_path']
    "name": "Nombre de configuración", // Obligatorio
    "description": "Descripción", // Opcional
    "properties": {
      "icon": "Nombre del icono", // Opcional
      "color": "Código Hexadecimal del color", // Opcional - Valor por default #000000
      "style": "Tipo de estilo", // Opcional - Valor por default solid - ['dotted','solid','dashed','double','groove','ridge','inset','outset','none']
      "location": [], // Ubicación - Opcional - Formato esperado { "lng": Number, "lat": Number }
      "address": {
        // Datos domiciliares
      } // Campo no elegible para tipo de configuración safe_path
    }
  }
}
