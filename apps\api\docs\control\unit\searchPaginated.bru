meta {
  name: searchPaginated
  type: http
  seq: 3
}

get {
  url: {{base}}/control/unit/search?limit=10&deleted=false&disabled=true&locale=es&q=Prueba&includeVehicle=true&vehicleFields=mileage,terrain,isNational,fkVehicleTypeId,nameVehicleType,fkVehicleBrandId,nameVehicleBrand,fkVehicleModelId,nameVehicleModel,year,fkVehicleColorId,nameVehicleColor,useType,licensePlate,capacity,nivserialNumber,situation,cost
  body: none
  auth: none
}

params:query {
  limit: 10
  deleted: false
  disabled: true
  locale: es
  q: Prueba
  includeVehicle: true
  vehicleFields: mileage,terrain,isNational,fkVehicleTypeId,nameVehicleType,fkVehicleBrandId,nameVehicleBrand,fkVehicleModelId,nameVehicleModel,year,fkVehicleColorId,nameVehicleColor,useType,licensePlate,capacity,nivserialNumber,situation,cost
  ~lastId: 
}
