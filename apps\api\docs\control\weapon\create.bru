meta {
  name: create
  type: http
  seq: 5
}

post {
  url: {{base}}/control/weapon?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

body:json {
  {
    "serialNumber": "N° Serie",
    "nationalRegistryNumber": "N° Registro Nacional", // Opcional
    "fkWeaponTypeId": "ID Tipo de Arma",
    "weaponSize": "Tamaño de Arma", // ['short', 'long']
    "fkAmmunitionTypeId": "ID Tipo de Munición",
    "locId": "ID Licencia Oficial Colectiva", // Opcional
    "weaponStatus": "Estado del Arma", // ['active', 'inactive', 'in_maintenance']
    "ownershipStatus": "Estado de Propiedad" // ['own', 'comodato']
  }
}
