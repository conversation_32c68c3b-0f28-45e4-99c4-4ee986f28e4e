import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/control/controllers/fatigue_place.controller';

const fatiguePlaceRouter = Router();

fatiguePlaceRouter.get('/', getAll);
fatiguePlaceRouter.get('/search', searchPaginated);
fatiguePlaceRouter.get('/:id', getOne);
fatiguePlaceRouter.post('/', create);
fatiguePlaceRouter.put('/:id', update);
fatiguePlaceRouter.delete('/:id', deleteOne);

export default fatiguePlaceRouter;
