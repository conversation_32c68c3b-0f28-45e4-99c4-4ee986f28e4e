meta {
  name: create
  type: http
  seq: 5
}

post {
  url: {{base}}/control/fatigue?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

body:json {
  {
    "fkUserId": "ID Usuario", // Responsable de la fátiga
    "fkShiftId": "Id Turno",
    "fatigueDate": "<PERSON>cha de la Fátiga", // Formato aaaa/mm/dd
    "status": "Estatus Fátiga", // ['draft', 'scheduled', 'active', 'completed']
    "comments": "Comentarios", // Opcional
    "arrayUsersIds": [
      {
        "fkUserId": "ID Usuario" // Asignado a la fátiga
      }
    ] // Opcional, enviar [] si no se asignará usuarios al crear fátiga
  }
}
