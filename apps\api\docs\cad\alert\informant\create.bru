meta {
  name: create
  type: http
  seq: 3
}

post {
  url: {{cad}}/alert/informant
  body: json
  auth: inherit
}

body:json {
  {
    "type": "Tipo de Informante", // Obligatorio - ['person', 'business', 'annonymous', 'organization']
    "name": "Nombre del informante", // Opcional
    "firstSurname": "Primer apellido", // Opcional
    "secondSurname": "Segundo apellido", // Opcional
    "businessName": "Nombre del negocio", // Opcional
    "businessPhone": "Teléfono del negocio", // Opcional
    "businessActivity": "Actividad del negocio", // Opcional
    "fkGenderId": "ID Genero",
    "phone": "Teléfono", // Opcional
    "description": "Descripción breve", // Opcional
    "address": {
      // Datos del domicilio (Objeto)
    }
  }
}
