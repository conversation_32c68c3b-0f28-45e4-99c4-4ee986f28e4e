import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/exam_type.controller';

const catExamTypeRouter = Router();

catExamTypeRouter.get('/', getAll);
catExamTypeRouter.get('/search', searchPaginated);
catExamTypeRouter.get('/:id', getOne);
catExamTypeRouter.post('/', create);
catExamTypeRouter.put('/:id', update);
catExamTypeRouter.delete('/:id', deleteOne);

export default catExamTypeRouter;
