import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import moment from 'moment';
import SystemModuleCache from '@/utils/system_module_cache';
import {
  systemModuleAssignmentAmmunition,
  systemModuleControlAmmunition,
  systemModuleControlFatigue,
} from '@repo/shared-drizzle/schemas';
import { assignAmmunitionBodySchema } from '../helpers/ammunition.helper';

type AmmunitionAssignGrouped = Record<
  string,
  {
    fkUserId: string;
    data: {
      id: string;
      fkControlAmmunitionId: string;
      fkFatigueId: string;
      quantityAssigned: number;
      quantityReturned: number;
      assignmentDate: string;
      assignmentEndDate: string | null;
      periodUse: number | null;
      comments: string | null;
    }[];
  }
>;

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, grouped: groupedQS } = req.query;
  let deleted = false;
  let disabled = false;
  let grouped = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentAmmunition.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentAmmunition.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentAmmunition.id,
        fkUserId: systemModuleAssignmentAmmunition.fkUserId,
        fkControlAmmunitionId: systemModuleAssignmentAmmunition.fkControlAmmunitionId,
        fkFatigueId: systemModuleAssignmentAmmunition.fkFatigueId,
        quantityAssigned: systemModuleAssignmentAmmunition.quantityAssigned,
        quantityReturned: systemModuleAssignmentAmmunition.quantityReturned,
        assignmentDate: systemModuleAssignmentAmmunition.assignmentDate,
        assignmentEndDate: systemModuleAssignmentAmmunition.assignmentEndDate,
        periodUse: systemModuleAssignmentAmmunition.periodUse,
        comments: systemModuleAssignmentAmmunition.comments,
      })
      .from(systemModuleAssignmentAmmunition)
      .where(and(...filters));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkControlAmmunitionId: rest.fkControlAmmunitionId,
            fkFatigueId: rest.fkFatigueId,
            quantityAssigned: rest.quantityAssigned,
            quantityReturned: rest.quantityReturned,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            periodUse: rest.periodUse || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as AmmunitionAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleAssignmentAmmunition.id,
        fkUserId: systemModuleAssignmentAmmunition.fkUserId,
        fkControlAmmunitionId: systemModuleAssignmentAmmunition.fkControlAmmunitionId,
        fkFatigueId: systemModuleAssignmentAmmunition.fkFatigueId,
        quantityAssigned: systemModuleAssignmentAmmunition.quantityAssigned,
        quantityReturned: systemModuleAssignmentAmmunition.quantityReturned,
        assignmentDate: systemModuleAssignmentAmmunition.assignmentDate,
        assignmentEndDate: systemModuleAssignmentAmmunition.assignmentEndDate,
        periodUse: systemModuleAssignmentAmmunition.periodUse,
        comments: systemModuleAssignmentAmmunition.comments,
      })
      .from(systemModuleAssignmentAmmunition)
      .where(eq(systemModuleAssignmentAmmunition.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar assignmentDate
    const date = new Date(req.body.assignmentDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('El campo assignmentDate no es una fecha valida');
    }

    // Quitar del payload el campo assignmentEndDate, no considerar al crear la asignación
    const { assignmentEndDate, ...rest } = req.body;

    // Validar payload
    const insertData = await assignAmmunitionBodySchema.parseAsync({ ...rest });
    const result = await db.transaction(async (trx) => {
      // Validar la existencia de la munición
      const ammunition = await trx
        .select()
        .from(systemModuleControlAmmunition)
        .where(eq(systemModuleControlAmmunition.id, insertData.fkControlAmmunitionId))
        .limit(1);
      if (ammunition.length === 0) {
        throw new Error('Munición no encontrada');
      }

      const systemModuleCache = SystemModuleCache.getInstance();
      const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_ASIGNACIONES_MUNICIONES);
      if (!systemModule) {
        throw new Error('System module CTRL_ASIGNACIONES_MUNICIONES not found');
      }
      const newAssignment = await trx.insert(systemModuleAssignmentAmmunition).values({
        ...insertData,
        assignmentEndDate: null,
        quantityReturned: 0,
        fkSystemModuleId: systemModule.id,
      });

      return newAssignment;
    });

    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar assignmentDate
    const date = new Date(req.body.assignmentDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('El campo assignmentDate no es una fecha valida');
    }

    // Validar unassionEndDate (si existe)
    if (req.body.assignmentEndDate) {
      const dateEnd = new Date(req.body.assignmentEndDate.replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(dateEnd.getTime())) {
        throw new Error('El campo assignmentEndDate no es una fecha valida');
      }
      if (dateEnd < date) {
        throw new Error('La fecha de fin de asignación no puede ser anterior a la fecha de inicio');
      }
    }

    // Validar payload
    const updateData = await assignAmmunitionBodySchema.parseAsync({ ...req.body });
    const result = await db.transaction(async (trx) => {
      // Obtener registro de asignación
      const assignment = await trx
        .select()
        .from(systemModuleAssignmentAmmunition)
        .where(eq(systemModuleAssignmentAmmunition.id, id))
        .limit(1);
      if (assignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }
      // Validar la existencia de la munición
      const weapon = await trx
        .select()
        .from(systemModuleControlAmmunition)
        .where(eq(systemModuleControlAmmunition.id, updateData.fkControlAmmunitionId))
        .limit(1);
      if (weapon.length === 0) {
        throw new Error('Munición no encontrada');
      }

      if (updateData.quantityReturned > updateData.quantityAssigned) {
        throw new Error(
          'La cantidad de municiones retornadas no puede ser mayor a la cantidad de municiones asignadas',
        );
      }

      // Calcular el periodo de uso si se proporciona assignmentEndDate
      let periodUse = assignment[0].periodUse;
      if (updateData.assignmentEndDate) {
        const diffDays = moment(updateData.assignmentEndDate).diff(moment(updateData.assignmentDate), 'days');
        periodUse = diffDays;
      }

      // Actualizar la asignación
      const assignUpdate = await trx
        .update(systemModuleAssignmentAmmunition)
        .set({ ...updateData, periodUse })
        .where(eq(systemModuleAssignmentAmmunition.id, id));

      return assignUpdate;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Validar que exista la asignación
      const existingAssignment = await trx
        .select()
        .from(systemModuleAssignmentAmmunition)
        .where(eq(systemModuleAssignmentAmmunition.id, id))
        .limit(1);
      if (existingAssignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar que la asignación no este en un despliegue activo
      const activeWeaponAssignment = await trx
        .select()
        .from(systemModuleAssignmentAmmunition)
        .leftJoin(
          systemModuleControlFatigue,
          eq(systemModuleAssignmentAmmunition.fkFatigueId, systemModuleControlFatigue.id),
        )
        .where(
          and(
            eq(systemModuleControlFatigue.status, 'active'),
            eq(systemModuleAssignmentAmmunition.fkFatigueId, existingAssignment[0].fkFatigueId),
            eq(systemModuleAssignmentAmmunition.id, id),
          ),
        )
        .limit(1);
      if (activeWeaponAssignment.length > 0) {
        throw new Error('No es posible eliminar la asignación si este ya se encuentra en un despliegue activo');
      }

      const deleteResult = await trx
        .update(systemModuleAssignmentAmmunition)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleAssignmentAmmunition.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    q: qQS,
    grouped: groupedQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;
  let grouped = false;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentAmmunition.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentAmmunition.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(sql`${systemModuleAssignmentAmmunition.quantityAssigned}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentAmmunition.quantityReturned}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentAmmunition.assignmentDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentAmmunition.assignmentEndDate}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleAssignmentAmmunition.comments, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleAssignmentAmmunition.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentAmmunition.id,
        fkUserId: systemModuleAssignmentAmmunition.fkUserId,
        fkControlAmmunitionId: systemModuleAssignmentAmmunition.fkControlAmmunitionId,
        fkFatigueId: systemModuleAssignmentAmmunition.fkFatigueId,
        quantityAssigned: systemModuleAssignmentAmmunition.quantityAssigned,
        quantityReturned: systemModuleAssignmentAmmunition.quantityReturned,
        assignmentDate: systemModuleAssignmentAmmunition.assignmentDate,
        assignmentEndDate: systemModuleAssignmentAmmunition.assignmentEndDate,
        periodUse: systemModuleAssignmentAmmunition.periodUse,
        comments: systemModuleAssignmentAmmunition.comments,
      })
      .from(systemModuleAssignmentAmmunition)
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleAssignmentAmmunition.id));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkControlAmmunitionId: rest.fkControlAmmunitionId,
            fkFatigueId: rest.fkFatigueId,
            quantityAssigned: rest.quantityAssigned,
            quantityReturned: rest.quantityReturned,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            periodUse: rest.periodUse || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as AmmunitionAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
