import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/state.controller';

const catStateRouter = Router();

catStateRouter.get('/', getAll);
catStateRouter.get('/search', searchPaginated);
catStateRouter.get('/:id', getOne);
catStateRouter.post('/', create);
catStateRouter.put('/:id', update);
catStateRouter.delete('/:id', deleteOne);

export default catStateRouter;
