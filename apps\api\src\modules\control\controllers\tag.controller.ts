import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, not, or, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';
import { tagBodySchema, validateNormalizeHexColor } from '../helpers/tag.helper';
import { systemModuleControlTag } from '@repo/shared-drizzle/schemas';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS } = req.query;
  let [deleted, disabled] = [false, false];

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlTag.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlTag.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlTag.id,
        name: systemModuleControlTag.name,
        color: systemModuleControlTag.color,
        icon: systemModuleControlTag.icon,
        description: systemModuleControlTag.description,
      })
      .from(systemModuleControlTag)
      .where(and(...filters));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TAG}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleControlTag.id,
        name: systemModuleControlTag.name,
        color: systemModuleControlTag.color,
        icon: systemModuleControlTag.icon,
        description: systemModuleControlTag.description,
      })
      .from(systemModuleControlTag)
      .where(eq(systemModuleControlTag.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TAG}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Obtener el módulo de etiquetas
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_ETIQUETAS);
    if (!systemModule) {
      throw new Error('System module CTRL_ETIQUETAS not found');
    }

    // Validar payload
    const insertData = await tagBodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });

    const result = await db.transaction(async (trx) => {
      // Validar color
      insertData.color = validateNormalizeHexColor(insertData.color);

      // Verificar que no exista una etiqueta con el mismo nombre
      const existingTag = await trx
        .select()
        .from(systemModuleControlTag)
        .where(eq(systemModuleControlTag.name, insertData.name));
      if (existingTag.length > 0) {
        throw new Error('Ya existe una etiqueta con el mismo nombre');
      }

      // Insertar el nuevo turno
      const resultCreate = await trx.insert(systemModuleControlTag).values({
        ...insertData,
        fkSystemModuleId: systemModule.id,
      });

      return resultCreate;
    });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TAG} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    const updateData = await tagBodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });

    const result = await db.transaction(async (trx) => {
      // Validar color
      updateData.color = validateNormalizeHexColor(updateData.color);

      // Verificar que la etiqueta exista
      const existingTag = await trx.select().from(systemModuleControlTag).where(eq(systemModuleControlTag.id, id));
      if (existingTag.length === 0) {
        throw new Error('Etiqueta no encontrada');
      }

      // Verificar que no exista una etiqueta con el mismo nombre
      const existingTagName = await trx
        .select()
        .from(systemModuleControlTag)
        .where(and(eq(systemModuleControlTag.name, updateData.name), not(eq(systemModuleControlTag.id, id))));
      if (existingTagName.length > 0) {
        throw new Error('Ya existe una etiqueta con el mismo nombre');
      }

      // Actualizar la etiqueta
      const updateResult = await trx
        .update(systemModuleControlTag)
        .set({ ...updateData })
        .where(eq(systemModuleControlTag.id, id));

      return updateResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TAG}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Verificar que la etiqueta exista
      const existingTag = await trx.select().from(systemModuleControlTag).where(eq(systemModuleControlTag.id, id));
      if (existingTag.length === 0) {
        throw new Error('Etiqueta no encontrada');
      }
      const deleteResult = await trx
        .update(systemModuleControlTag)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleControlTag.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TAG}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, deleted: deletedQS, disabled: disabledQS, q: qQS } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlTag.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlTag.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleControlTag.name, `%${q}%`));
      likeFilters.push(like(systemModuleControlTag.description, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleControlTag.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlTag.id,
        name: systemModuleControlTag.name,
        color: systemModuleControlTag.color,
        icon: systemModuleControlTag.icon,
        description: systemModuleControlTag.description,
      })
      .from(systemModuleControlTag)
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlTag.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TAG} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
