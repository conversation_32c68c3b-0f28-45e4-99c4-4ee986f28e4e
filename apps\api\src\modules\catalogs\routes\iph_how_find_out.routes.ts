// import { Router } from 'express';
// import {
//   getAll,
//   getOne,
//   create,
//   update,
//   deleteOne,
//   searchPaginated,
// } from '@/modules/catalogs/controllers/iph_how_find_out.controller';

// const catIphHowFindOutRouter = Router();

// catIphHowFindOutRouter.get('/', getAll);
// catIphHowFindOutRouter.get('/search', searchPaginated);
// catIphHowFindOutRouter.get('/:id', getOne);
// catIphHowFindOutRouter.post('/', create);
// catIphHowFindOutRouter.put('/:id', update);
// catIphHowFindOutRouter.delete('/:id', deleteOne);

// export default catIphHowFindOutRouter;
