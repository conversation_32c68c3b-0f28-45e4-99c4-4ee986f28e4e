import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/vehicle_status.controller';

const catVehicleStatusRouter = Router();

catVehicleStatusRouter.get('/', getAll);
catVehicleStatusRouter.get('/search', searchPaginated);
catVehicleStatusRouter.get('/:id', getOne);
catVehicleStatusRouter.post('/', create);
catVehicleStatusRouter.put('/:id', update);
catVehicleStatusRouter.delete('/:id', deleteOne);

export default catVehicleStatusRouter;
