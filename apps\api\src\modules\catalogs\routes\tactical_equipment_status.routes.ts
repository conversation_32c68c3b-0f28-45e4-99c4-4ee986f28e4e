import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/tactical_equipment_status.controller';

const catTacticalEquipmentStatusRouter = Router();

catTacticalEquipmentStatusRouter.get('/', getAll);
catTacticalEquipmentStatusRouter.get('/search', searchPaginated);
catTacticalEquipmentStatusRouter.get('/:id', getOne);
catTacticalEquipmentStatusRouter.post('/', create);
catTacticalEquipmentStatusRouter.put('/:id', update);
catTacticalEquipmentStatusRouter.delete('/:id', deleteOne);

export default catTacticalEquipmentStatusRouter;
