import { Router } from 'express';
import placeRouter from './place.routes';
import rolRouter from './rol.routes';
import profileRouter from './profile.routes';
import permissionRouter from './permission.routes';
import ammunitionRouter from './ammunition.routes';
import weaponRouter from './weapon.routes';
import operationalQuadrantRouter from './operational_quadrant.routes';
import supplierRouter from './supplier.routes';
import uniformRouter from './uniform.routes';
import tacticalEquipmentRouter from './tactical_equipment.routes';
import unitRouter from './unit.routes';
import radioRouter from './radio.routes';
import quadrantGroupingRouter from './quadrant_grouping.routes';
import shiftRouter from './shift.routes';
import fatigueRouter from './fatigue.routes';
import ammunitionUseRouter from './ammunition_use.routes';
import commissionRouter from './commission.routes';
import fatigueCategoryRouter from './fatigue_category.routes';
import fatiguePlaceRouter from './fatigue_place.routes';
import tagRouter from './tag.routes';

const controlRouter = Router();

controlRouter.use('/place', placeRouter);
controlRouter.use('/rol', rolRouter);
controlRouter.use('/profile', profileRouter);
controlRouter.use('/permission', permissionRouter);
controlRouter.use('/ammunition', ammunitionRouter);
controlRouter.use('/weapon', weaponRouter);
controlRouter.use('/operational_quadrant', operationalQuadrantRouter);
controlRouter.use('/supplier', supplierRouter);
controlRouter.use('/uniform', uniformRouter);
controlRouter.use('/tactical_equipment', tacticalEquipmentRouter);
controlRouter.use('/unit', unitRouter);
controlRouter.use('/radio', radioRouter);
controlRouter.use('/quadrant_grouping', quadrantGroupingRouter);
controlRouter.use('/shift', shiftRouter);
controlRouter.use('/fatigue', fatigueRouter);
controlRouter.use('/ammunition_use', ammunitionUseRouter);
controlRouter.use('/commission', commissionRouter);
controlRouter.use('/fatigue_category', fatigueCategoryRouter);
controlRouter.use('/fatigue_place', fatiguePlaceRouter);
controlRouter.use('/tag', tagRouter);

export default controlRouter;
