meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/assignments/fatigue/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 01965957-9ea8-7947-88c6-a3b98c3fb196
}

body:json {
  /*{
    "fkUserId": "ID Usuario",
    "fkFatigueId": "ID Fatiga/Despliegue",
    "categories": ["ID Categoria 1", "ID Categoria 2"],
    "places": ["ID Lugar 1", "ID Lugar 2"],
    "fkArrayQuadrantsIds": ["ID Cuadrante/Sector", "ID Cuadrante/Sector"], // Opcional
    "fkUnitObject": {
      "fkUnitControlId": "ID Unidad Vehicular",
      "isDriver": false,
      "isPassenger": false,
      "isInCharge": false
    }
  }*/
  {
    "fkUserId": "01953e99-2dc5-2b69-6889-4ba50bfb074b",
    "fkFatigueId": "019601c9-a916-a9d1-1862-c26ac92f4dbd",
    "fkArrayCategoryIds": ["01963f81-40b5-1146-9ce8-e14e0604c353"],
    "fkArrayPlaceIds": [],
    "fkArrayQuadrantsIds": [],
    "fkUnitObject": null
  }
}
