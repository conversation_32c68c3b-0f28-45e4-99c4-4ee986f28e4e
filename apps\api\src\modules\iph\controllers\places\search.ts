import type { Request, Response } from 'express';
import { HttpStatus } from '@/utils/http_status';
import { Logger } from '@/utils/logger';
import db from '@/db';
import { and, asc, eq, gt, like, sql, type SQL } from 'drizzle-orm';
import { systemModuleControlPlaces, catMunicipality, catSettlement, catState } from '@repo/shared-drizzle/schemas';
import { getLocale } from '@/utils/locale_validator';

export const search = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, q: qQS, locale: localeQS } = req.query;

  let locale = getLocale();
  let lastId = null;
  let limit = 10;
  let q = null;

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: (SQL | undefined)[] = [];
    filters.push(eq(systemModuleControlPlaces.isDeleted, false), eq(systemModuleControlPlaces.isEnabled, true));
    if (q) {
      filters.push(
        // Search by full address
        like(
          sql`normalize_text(coalesce(${systemModuleControlPlaces.name}) || ' ' || coalesce(${systemModuleControlPlaces.zipCode}) || ' ' || coalesce(${systemModuleControlPlaces.street}) || ' ' || coalesce(${systemModuleControlPlaces.reference}) || ' ' || coalesce(${systemModuleControlPlaces.betweenStreet1}) || ' ' || coalesce(${systemModuleControlPlaces.betweenStreet2}))`,
          sql`'%' || normalize_text(${q}) || '%'`,
        ),
      );
    }

    if (lastId) {
      filters.push(gt(systemModuleControlPlaces.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlPlaces.id,
        name: systemModuleControlPlaces.name,
        type: systemModuleControlPlaces.type,
        state: {
          id: catState.id,
          name: catState[locale],
        },
        municipality: {
          id: catMunicipality.id,
          name: catMunicipality[locale],
        },
        colony: {
          id: catSettlement.id,
          name: catSettlement[locale],
        },
        zipCode: systemModuleControlPlaces.zipCode,
        street: systemModuleControlPlaces.street,
        number: systemModuleControlPlaces.number,
        interiorNumber: systemModuleControlPlaces.interiorNumber,
        betweenStreet1: systemModuleControlPlaces.betweenStreet1,
        betweenStreet2: systemModuleControlPlaces.betweenStreet2,
        reference: systemModuleControlPlaces.reference,
        location: systemModuleControlPlaces.location,
        sectors: systemModuleControlPlaces.sectors,
      })
      .from(systemModuleControlPlaces)
      .leftJoin(catState, eq(systemModuleControlPlaces.fkStateId, catState.id))
      .leftJoin(catMunicipality, eq(systemModuleControlPlaces.fkMunicipalityId, catMunicipality.id))
      .leftJoin(catSettlement, eq(systemModuleControlPlaces.fkColonyId, catSettlement.id))
      .where(and(...filters))
      .limit(limit)
      .orderBy(asc(systemModuleControlPlaces.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'iph/places/search - search',
      source: req.headers['user-agent'],
      module: 'iph/places',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
