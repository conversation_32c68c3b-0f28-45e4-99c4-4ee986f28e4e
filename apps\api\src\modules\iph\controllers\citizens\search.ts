import type { Request, Response } from 'express';
import { HttpStatus } from '@/utils/http_status';
import { Logger } from '@/utils/logger';
import db from '@/db';
import { and, asc, eq, gt, is, like, type SelectedFields, sql, type SQL } from 'drizzle-orm';
import {
  catDocumentType,
  catGender,
  catMaritalStatus,
  catMunicipality,
  catNationality,
  catSettlement,
  catState,
  systemModuleControlCitizen,
  systemModuleControlPlaces,
} from '@repo/shared-drizzle/schemas';
import { PgTimestampString } from 'drizzle-orm/pg-core';
import type { SelectResultFields } from 'drizzle-orm/query-builders/select.types';
import { getLocale } from '@/utils/locale_validator';

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
function jsonBuildObject<T extends SelectedFields<any, any>>(shape: T) {
  const chunks: SQL[] = [];

  for (const entry of Object.entries(shape)) {
    const [key, value] = entry;
    if (chunks.length > 0) {
      chunks.push(sql.raw(','));
    }

    chunks.push(sql.raw(`'${key}',`));

    // json_build_object formats to ISO 8601 ...
    if (is(value, PgTimestampString)) {
      chunks.push(sql`timezone('UTC', ${value})`);
    } else {
      chunks.push(sql`${value}`);
    }
  }

  return sql<SelectResultFields<T>>`coalesce(json_build_object(${sql.join(chunks)}), '{}')`;
}

export const search = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, q: qQS, locale: localeQS } = req.query;

  let locale = getLocale();
  let lastId = null;
  let limit = 10;
  let q = null;

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: (SQL | undefined)[] = [];
    filters.push(eq(systemModuleControlCitizen.isDeleted, false), eq(systemModuleControlCitizen.isEnabled, true));
    if (q) {
      filters.push(
        // Search by full name
        like(
          sql`normalize_text(coalesce(${systemModuleControlCitizen.name}) || ' ' || coalesce(${systemModuleControlCitizen.firstSurname}) || ' ' || coalesce(${systemModuleControlCitizen.secondSurname}))`,
          sql`'%' || normalize_text(${q}) || '%'`,
        ),
      );
    }

    if (lastId) {
      filters.push(gt(systemModuleControlCitizen.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlCitizen.id,
        name: systemModuleControlCitizen.name,
        firstSurname: systemModuleControlCitizen.firstSurname,
        secondSurname: systemModuleControlCitizen.secondSurname,
        alias: systemModuleControlCitizen.alias,
        gender: {
          id: catGender.id,
          name: catGender[locale],
        },
        bornAt: systemModuleControlCitizen.bornAt,
        phone: systemModuleControlCitizen.phone,
        email: systemModuleControlCitizen.email,
        address: {
          id: systemModuleControlPlaces.id,
          name: systemModuleControlPlaces.name,
          type: systemModuleControlPlaces.type,
          state: jsonBuildObject({
            id: catState.id,
            name: catState[locale],
          }),
          municipality: jsonBuildObject({
            id: catMunicipality.id,
            name: catMunicipality[locale],
          }),
          colony: jsonBuildObject({
            id: catSettlement.id,
            name: catSettlement[locale],
          }),
          zipCode: systemModuleControlPlaces.zipCode,
          street: systemModuleControlPlaces.street,
          number: systemModuleControlPlaces.number,
          interiorNumber: systemModuleControlPlaces.interiorNumber,
          betweenStreet1: systemModuleControlPlaces.betweenStreet1,
          betweenStreet2: systemModuleControlPlaces.betweenStreet2,
          reference: systemModuleControlPlaces.reference,
          location: systemModuleControlPlaces.location,
          sectors: systemModuleControlPlaces.sectors,
        },
        nationality: {
          id: catNationality.id,
          name: catNationality[locale],
        },
        documentType: {
          id: catDocumentType.id,
          name: catDocumentType[locale],
        },
        documentIdentificationNumber: systemModuleControlCitizen.documentIdentificationNumber,
        maritalStatus: {
          id: catMaritalStatus.id,
          name: catMaritalStatus[locale],
        },
        emergencyContactId: systemModuleControlCitizen.fkEmercencyContactCitizenId,
        curp: systemModuleControlCitizen.curp,
        rfc: systemModuleControlCitizen.rfc,
      })
      .from(systemModuleControlCitizen)
      .leftJoin(
        systemModuleControlPlaces,
        eq(systemModuleControlCitizen.fkAddressPlaceId, systemModuleControlPlaces.id),
      )
      .leftJoin(catNationality, eq(systemModuleControlCitizen.fkNationalityId, catNationality.id))
      .leftJoin(catDocumentType, eq(systemModuleControlCitizen.fkDocumentTypeId, catDocumentType.id))
      .leftJoin(catMaritalStatus, eq(systemModuleControlCitizen.fkMaritalStatusId, catMaritalStatus.id))
      .leftJoin(catGender, eq(systemModuleControlCitizen.fkGenderId, catGender.id))
      .leftJoin(catState, eq(systemModuleControlPlaces.fkStateId, catState.id))
      .leftJoin(catMunicipality, eq(systemModuleControlPlaces.fkMunicipalityId, catMunicipality.id))
      .leftJoin(catSettlement, eq(systemModuleControlPlaces.fkColonyId, catSettlement.id))
      .where(and(...filters))
      .limit(limit)
      .orderBy(asc(systemModuleControlCitizen.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'iph/elements/all - search',
      source: req.headers['user-agent'],
      module: 'iph/elements',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
