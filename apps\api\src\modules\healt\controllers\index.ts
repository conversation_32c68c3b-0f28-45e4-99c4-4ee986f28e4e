import db from '@/db';
import logsDb from '@/db/log';
import { sql } from 'drizzle-orm';
import type { Request, Response } from 'express';

async function healthCheck(_: Request, res: Response) {
  const [mainResult, logsResult] = await Promise.all([db.execute(sql`SELECT 1`), logsDb.execute(sql`SELECT 1`)]);
  if (mainResult.fields.length === 0 && logsResult.fields.length === 0) {
    res.status(500).json({ status: 'error' });
    return;
  }
  res.status(200).json({ status: 'ok' });
}

export default healthCheck;
