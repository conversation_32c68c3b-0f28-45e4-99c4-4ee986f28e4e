/* import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/ownership_status.controller';

const catOwnershipStatusRouter = Router();

catOwnershipStatusRouter.get('/', getAll);
catOwnershipStatusRouter.get('/search', searchPaginated);
catOwnershipStatusRouter.get('/:id', getOne);
catOwnershipStatusRouter.post('/', create);
catOwnershipStatusRouter.put('/:id', update);
catOwnershipStatusRouter.delete('/:id', deleteOne);

export default catOwnershipStatusRouter;
 */
