import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, not, or, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import SystemModuleCache from '@/utils/system_module_cache';
import { fatigueCategoryBodySchema } from '../helpers/fatigue_category.helper';
import { systemModuleControlFatigueCategory } from '@repo/shared-drizzle/schemas';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS } = req.query;
  let deleted = false;
  let disabled = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlFatigueCategory.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlFatigueCategory.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlFatigueCategory.id,
        name: systemModuleControlFatigueCategory.name,
        description: systemModuleControlFatigueCategory.description,
      })
      .from(systemModuleControlFatigueCategory)
      .where(and(...filters));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE_CATEGORY}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleControlFatigueCategory.id,
        name: systemModuleControlFatigueCategory.name,
        description: systemModuleControlFatigueCategory.description,
      })
      .from(systemModuleControlFatigueCategory)
      .where(eq(systemModuleControlFatigueCategory.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE_CATEGORY}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const insertData = await fatigueCategoryBodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_FATIGUE_CATEGORY);
    if (!systemModule) {
      throw new Error('System module CTRL_FATIGUE_CATEGORY not found');
    }
    const result = await db.transaction(async (trx) => {
      // Validar que no exista una categoría con el mismo nombre
      const existingPlace = await trx
        .select()
        .from(systemModuleControlFatigueCategory)
        .where(eq(systemModuleControlFatigueCategory.name, insertData.name));
      if (existingPlace.length > 0) {
        throw new Error('Ya existe un categoria con el mismo nombre');
      }
      // Insertar la nueva categoría
      const resultCreate = await trx.insert(systemModuleControlFatigueCategory).values({
        ...insertData,
        fkSystemModuleId: systemModule.id,
      });
      return resultCreate;
    });

    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE_CATEGORY} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { name, description } = req.body;
  try {
    const result = await db.transaction(async (trx) => {
      // Verificar que la categoría exista
      const existingFatigueCategory = await trx
        .select()
        .from(systemModuleControlFatigueCategory)
        .where(eq(systemModuleControlFatigueCategory.id, id));
      if (existingFatigueCategory.length === 0) {
        throw new Error('Categoría de fatiga no encontrada');
      }
      // Verificar que no exista una categoría con el mismo nombre
      const existingFatigueCategoryName = await trx
        .select()
        .from(systemModuleControlFatigueCategory)
        .where(
          and(eq(systemModuleControlFatigueCategory.name, name), not(eq(systemModuleControlFatigueCategory.id, id))),
        );
      if (existingFatigueCategoryName.length > 0) {
        throw new Error('Ya existe una categoría con el mismo nombre');
      }
      // Actualizar la categoría
      const updateResult = await trx
        .update(systemModuleControlFatigueCategory)
        .set({
          name,
          description,
          fkLastUserId: req.userId,
          fkLastDependencyId: req.dependencyId,
        })
        .where(eq(systemModuleControlFatigueCategory.id, id));
      return updateResult;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE_CATEGORY}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Verificar que la categoría exista
      const existingFatigueCategory = await trx
        .select()
        .from(systemModuleControlFatigueCategory)
        .where(eq(systemModuleControlFatigueCategory.id, id));
      if (existingFatigueCategory.length === 0) {
        throw new Error('Categoría de fatiga no encontrada');
      }
      // Actualizar la categoría
      const deleteResult = await trx
        .update(systemModuleControlFatigueCategory)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleControlFatigueCategory.id, id));
      return deleteResult;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE_CATEGORY}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, deleted: deletedQS, disabled: disabledQS, q: qQS } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlFatigueCategory.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlFatigueCategory.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleControlFatigueCategory.name, `%${q}%`));
      likeFilters.push(like(systemModuleControlFatigueCategory.description, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleControlFatigueCategory.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlFatigueCategory.id,
        name: systemModuleControlFatigueCategory.name,
        description: systemModuleControlFatigueCategory.description,
      })
      .from(systemModuleControlFatigueCategory)
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlFatigueCategory.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE_CATEGORY} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
