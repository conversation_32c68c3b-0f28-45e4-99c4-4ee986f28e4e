import db from '@/db';
import { type General, systemModuleControlIphGeneral } from '@repo/shared-drizzle/schemas';
import { HttpStatus } from '@/utils/http_status';
import { generalSchema } from '@repo/types/schemas';
import type { Request, Response } from 'express';
import type z from 'zod';

const saveGeneral = async (req: Request, res: Response) => {
  const userId = req.userId as string;
  const dependencyId = req.dependencyId as string;
  const error: { [key: string]: z.ZodError } = {};
  try {
    const dataToUpdate: General = {
      id: undefined,
      fkLastUserId: userId,
      fkLastDependencyId: dependencyId,
      fkUserId: userId,
      fkDependencyId: dependencyId,
      updatedAt: new Date(),
    };
    const unsafeBody = req.body as z.infer<typeof generalSchema>;
    // validate fields individually
    const status = generalSchema.shape.status.safeParse(unsafeBody.status);
    if (status.success) {
      dataToUpdate.status = status.data;
    } else {
      error.status = status.error;
    }
    const referenceNumber = generalSchema.shape.referenceNumber.safeParse(unsafeBody.referenceNumber);
    if (referenceNumber.success) {
      dataToUpdate.referenceNumber = referenceNumber.data;
    } else {
      error.referenceNumber = referenceNumber.error;
    }
    const fileNumber = generalSchema.shape.fileNumber.safeParse(unsafeBody.fileNumber);
    if (fileNumber.success) {
      dataToUpdate.fileNumber = fileNumber.data;
    } else {
      error.fileNumber = fileNumber.error;
    }
    const id = generalSchema.shape.id.safeParse(unsafeBody.id);
    if (id.success && id.data) {
      dataToUpdate.id = id.data;
    }

    if (Object.keys(dataToUpdate).length < 6) {
      // if there is no data to update more than the 4 required fields return Not Modified status
      res.status(HttpStatus.NOT_MODIFIED).json({ message: 'No data to update', error });
      return;
    }
    const updated = await db.insert(systemModuleControlIphGeneral).values(dataToUpdate).onConflictDoUpdate({
      target: systemModuleControlIphGeneral.id,
      set: dataToUpdate,
    });

    res.status(HttpStatus.OK).json({ error, success: (updated.rowCount || 0) > 0 });
  } catch (error) {
    const err = error as Error;
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message: err.message, error });
  }
};

export default saveGeneral;
