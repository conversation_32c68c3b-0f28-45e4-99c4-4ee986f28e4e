import type { Request, Response } from 'express';
import { HttpStatus } from '@/utils/http_status';
import { Logger } from '@/utils/logger';
import db from '@/db';
import {
  catPoliceJob,
  catSecondment,
  systemModuleAssignmentUnit,
  systemModuleControlCitizen,
  systemModuleControlVehicleUnit,
  systemModuleUser,
  systemModuleUserKardexEmployment,
} from '@repo/shared-drizzle/schemas';
import { and, asc, eq, gt, isNull, like, or, sql, type SQL } from 'drizzle-orm';
import { getLocale } from '@/utils/locale_validator';

export const search = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, locale: localeQS, q: qQS } = req.query;

  let lastId = null;
  let limit = 10;
  let locale = getLocale();
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: (SQL | undefined)[] = [];
    filters.push(
      eq(systemModuleUser.isDeleted, false),
      eq(systemModuleUser.isEnabled, true),
      isNull(systemModuleAssignmentUnit.assignmentEndDate),
    );
    if (q) {
      filters.push(
        // Search by full name
        or(
          like(
            sql`normalize_text(coalesce(${systemModuleControlCitizen.name}) || ' ' || coalesce(${systemModuleControlCitizen.firstSurname}) || ' ' || coalesce(${systemModuleControlCitizen.secondSurname}))`,
            sql`'%' || normalize_text(${q}) || '%'`,
          ),
          // Search by position
          like(sql`normalize_text(${catPoliceJob[locale]})`, sql`'%' || normalize_text(${q}) || '%'`),
        ),
      );
    }

    if (lastId) {
      filters.push(gt(systemModuleUser.id, lastId));
    }
    const resultDB = await db
      .select()
      .from(systemModuleUser)
      .leftJoin(systemModuleControlCitizen, eq(systemModuleUser.fkCitizenId, systemModuleControlCitizen.id))
      .leftJoin(systemModuleAssignmentUnit, eq(systemModuleUser.id, systemModuleAssignmentUnit.fkUserId))
      .leftJoin(
        systemModuleControlVehicleUnit,
        eq(systemModuleAssignmentUnit.fkUnitControlId, systemModuleControlVehicleUnit.id),
      )
      .leftJoin(systemModuleUserKardexEmployment, eq(systemModuleUser.id, systemModuleUserKardexEmployment.fkUserId))
      .leftJoin(catSecondment, eq(systemModuleUserKardexEmployment.fkSecondmentId, catSecondment.id))
      .leftJoin(catPoliceJob, eq(systemModuleUserKardexEmployment.fkCurrentJobPositionId, catPoliceJob.id))
      .where(and(...filters))
      .limit(limit)
      .orderBy(asc(systemModuleUser.id));

    type ElementResult = {
      id: string;
      name: string;
      position: {
        id: string;
        name?: string | null;
      };
      vehicleUnit?: {
        id: string;
        code: string;
      } | null;
      secondment: {
        id: string;
        name?: string | null;
      };
    };

    const result: ElementResult[] = [];

    for (const element of resultDB) {
      if (!element.system_module_control_citizen) {
        return undefined;
      }
      if (!element.system_module_user_kardex_employment) {
        return undefined;
      }

      const vehicleUnit = !element.system_module_control_vehicle_unit
        ? null
        : {
            id: element.system_module_control_vehicle_unit.id,
            code: element.system_module_control_vehicle_unit.code,
          };

      let name = element.system_module_control_citizen.name;
      if (element.system_module_control_citizen.firstSurname) {
        name = `${name} ${element.system_module_control_citizen.firstSurname}`;
      }
      if (element.system_module_control_citizen.secondSurname) {
        name = `${name} ${element.system_module_control_citizen.secondSurname}`;
      }

      result.push({
        id: element.system_module_user.id,
        name: name || '',
        position: {
          id: element.system_module_user_kardex_employment.fkCurrentJobPositionId,
          name: element.cat_police_job?.es,
        },
        secondment: {
          id: element.system_module_user_kardex_employment.fkSecondmentId,
          name: element.cat_secondment?.es,
        },
        vehicleUnit,
      });
    }

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'iph/elements/all - search',
      source: req.headers['user-agent'],
      module: 'iph/elements',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
