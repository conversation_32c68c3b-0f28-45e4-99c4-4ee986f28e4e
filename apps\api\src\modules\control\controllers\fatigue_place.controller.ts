import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, not, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import SystemModuleCache from '@/utils/system_module_cache';
import { fatiguePlaceBodySchema } from '../helpers/fatigue_place.helper';
import { systemModuleControlFatiguePlace } from '@repo/shared-drizzle/schemas';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS } = req.query;
  let deleted = false;
  let disabled = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlFatiguePlace.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlFatiguePlace.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlFatiguePlace.id,
        name: systemModuleControlFatiguePlace.name,
        type: systemModuleControlFatiguePlace.type,
        fkFatigueCategoryId: systemModuleControlFatiguePlace.fkFatigueCategoryId,
        properties: systemModuleControlFatiguePlace.properties,
      })
      .from(systemModuleControlFatiguePlace)
      .where(and(...filters));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE_PLACE}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleControlFatiguePlace.id,
        name: systemModuleControlFatiguePlace.name,
        type: systemModuleControlFatiguePlace.type,
        fkFatigueCategoryId: systemModuleControlFatiguePlace.fkFatigueCategoryId,
        properties: systemModuleControlFatiguePlace.properties,
      })
      .from(systemModuleControlFatiguePlace)
      .where(eq(systemModuleControlFatiguePlace.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE_PLACE}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const insertData = await fatiguePlaceBodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_FATIGUE_PLACE);
    if (!systemModule) {
      throw new Error('System module CTRL_FATIGUE_PLACE not found');
    }
    const result = await db
      .insert(systemModuleControlFatiguePlace)
      .values({
        ...insertData,
        fkSystemModuleId: systemModule.id,
      })
      .returning({ id: systemModuleControlFatiguePlace.id });

    res.status(HttpStatus.CREATED).json((result.length || 0) > 0 ? result[0] : null);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE_PLACE} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const updateData = await fatiguePlaceBodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const result = await db.transaction(async (trx) => {
      // Verificar que la lugar exista
      const existingFatiguePlace = await trx
        .select()
        .from(systemModuleControlFatiguePlace)
        .where(eq(systemModuleControlFatiguePlace.id, id));
      if (existingFatiguePlace.length === 0) {
        throw new Error('Lugar de la fatiga no encontrada');
      }
      // Verificar que no exista una lugar con el mismo nombre
      const existingFatiguePlaceName = await trx
        .select()
        .from(systemModuleControlFatiguePlace)
        .where(
          and(
            eq(systemModuleControlFatiguePlace.name, updateData.name),
            not(eq(systemModuleControlFatiguePlace.id, id)),
          ),
        );
      if (existingFatiguePlaceName.length > 0) {
        throw new Error('Ya existe una lugar con el mismo nombre');
      }
      // Actualizar la lugar
      const updateResult = await trx
        .update(systemModuleControlFatiguePlace)
        .set({
          name: updateData.name,
          type: updateData.type,
          fkFatigueCategoryId: updateData.fkFatigueCategoryId,
          properties: updateData.properties,
          fkLastUserId: req.userId,
          fkLastDependencyId: req.dependencyId,
        })
        .where(eq(systemModuleControlFatiguePlace.id, id));
      return updateResult;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE_PLACE}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Verificar que la lugar exista
      const existingFatiguePlace = await trx
        .select()
        .from(systemModuleControlFatiguePlace)
        .where(eq(systemModuleControlFatiguePlace.id, id));
      if (existingFatiguePlace.length === 0) {
        throw new Error('Lugar de la fatiga no encontrada');
      }
      // Actualizar la lugar
      const deleteResult = await trx
        .update(systemModuleControlFatiguePlace)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleControlFatiguePlace.id, id));
      return deleteResult;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE_PLACE}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, deleted: deletedQS, disabled: disabledQS, q: qQS } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlFatiguePlace.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlFatiguePlace.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleControlFatiguePlace.name, `%${q}%`));
      likeFilters.push(sql`
        ${systemModuleControlFatiguePlace.properties} != {}::jsonb AND 
        EXISTS (
          SELECT 1 FROM jsonb_each_text(${systemModuleControlFatiguePlace.properties})
          WHERE value LIKE '%${q}%'
        )`);
    }

    if (lastId) {
      filters.push(gt(systemModuleControlFatiguePlace.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlFatiguePlace.id,
        name: systemModuleControlFatiguePlace.name,
        type: systemModuleControlFatiguePlace.type,
        fkFatigueCategoryId: systemModuleControlFatiguePlace.fkFatigueCategoryId,
        properties: systemModuleControlFatiguePlace.properties,
      })
      .from(systemModuleControlFatiguePlace)
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlFatiguePlace.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE_PLACE} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
