import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/job_status.controller';

const catJobStatusRouter = Router();

catJobStatusRouter.get('/', getAll);
catJobStatusRouter.get('/search', searchPaginated);
catJobStatusRouter.get('/:id', getOne);
catJobStatusRouter.post('/', create);
catJobStatusRouter.put('/:id', update);
catJobStatusRouter.delete('/:id', deleteOne);

export default catJobStatusRouter;
