import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/disciplinary_offense_type.controller';

const catDisciplinaryOffenseTypeRouter = Router();

catDisciplinaryOffenseTypeRouter.get('/', getAll);
catDisciplinaryOffenseTypeRouter.get('/search', searchPaginated);
catDisciplinaryOffenseTypeRouter.get('/:id', getOne);
catDisciplinaryOffenseTypeRouter.post('/', create);
catDisciplinaryOffenseTypeRouter.put('/:id', update);
catDisciplinaryOffenseTypeRouter.delete('/:id', deleteOne);

export default catDisciplinaryOffenseTypeRouter;
