import { Router } from 'express';
import {
  create,
  deleteOne,
  getAll,
  getOne,
  searchPaginated,
  update,
} from '../controllers/tactical_equipment.controller';

const tacticalEquipmentRouter = Router();

tacticalEquipmentRouter.get('/', getAll);
tacticalEquipmentRouter.get('/search', searchPaginated);
tacticalEquipmentRouter.get('/:id', getOne);
tacticalEquipmentRouter.post('/', create);
tacticalEquipmentRouter.put('/:id', update);
tacticalEquipmentRouter.delete('/:id', deleteOne);

export default tacticalEquipmentRouter;
