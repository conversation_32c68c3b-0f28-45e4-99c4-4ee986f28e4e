// import { Router } from 'express';
// import {
//   getAll,
//   getOne,
//   create,
//   update,
//   deleteOne,
//   searchPaginated,
// } from '@/modules/catalogs/controllers/iph_status.controller';

// const catIphStatusRouter = Router();

// catIphStatusRouter.get('/', getAll);
// catIphStatusRouter.get('/search', searchPaginated);
// catIphStatusRouter.get('/:id', getOne);
// catIphStatusRouter.post('/', create);
// catIphStatusRouter.put('/:id', update);
// catIphStatusRouter.delete('/:id', deleteOne);

// export default catIphStatusRouter;
