import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/supplier.controller';

const supplierRouter = Router();

supplierRouter.get('/', getAll);
supplierRouter.get('/search', searchPaginated);
supplierRouter.get('/:id', getOne);
supplierRouter.post('/', create);
supplierRouter.put('/:id', update);
supplierRouter.delete('/:id', deleteOne);

export default supplierRouter;
