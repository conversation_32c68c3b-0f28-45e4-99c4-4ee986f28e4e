meta {
  name: update
  type: http
  seq: 5
}

put {
  url: {{base}}/kardex/medical
  body: json
  auth: inherit
}

body:json {
  {
    "fkUserId": "ID Usuario", // Requerido
    "bloodGroup": "Grupo sanguíneo", // Requerido - ['A_plus','A_minus','B_plus','B_minus','AB_plus','AB_minus','O_plus','O_minus',]
    "height": 180, // Requerido - En centímetros
    "weight": 70, // Requerido - En kilos
    "allergies": "Alergias", // Opcional
    "ailments": "Padecimientos", // Opcional
    "medicalInsurance": true, // Requerido (Por default false)
    "useWheelchair": false // Requerido (Por default false)
  }
}
