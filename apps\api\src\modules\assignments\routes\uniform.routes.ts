import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/uniform.controller';

const uniformAssignRouter = Router();

uniformAssignRouter.get('/', getAll);
uniformAssignRouter.get('/search', searchPaginated);
uniformAssignRouter.get('/:id', getOne);
uniformAssignRouter.post('/', create);
uniformAssignRouter.put('/:id', update);
uniformAssignRouter.delete('/:id', deleteOne);

export default uniformAssignRouter;
