import type { Request, Response } from 'express';
import { catExternalInstitution, type Detained, systemModuleControlIphDetained } from '@repo/shared-drizzle/schemas';
import { getLocale, type Locale } from '@/utils/locale_validator';
import { detainedSchema } from '@repo/types/schemas';
import z from 'zod';
import { HttpStatus } from '@/utils/http_status';
import db from '@/db';
import genrateKey from '@/utils/generate_key';
import { saveCitizen, savePlace } from '../shared';

const validate = async ({
  unsafeData,
  userId,
  dependencyId,
  locale,
}: { unsafeData: z.infer<typeof detainedSchema>; userId: string; dependencyId: string; locale: Locale }) => {
  const error: { [k: string]: unknown } = {};
  const shape = detainedSchema.shape;
  const data: Detained = {
    fkIphId: unsafeData.iphId,
    fkLastUserId: userId,
    fkLastDependencyId: dependencyId,
    isEnabled: true,
    updatedAt: new Date(),
  };
  // detainedAt
  const dispositionAt = shape.detainedAt.safeParse(unsafeData.detainedAt);
  if (dispositionAt.success) {
    data.detainedAt = dispositionAt.data;
  } else {
    error.dispositionAt = dispositionAt.error;
  }
  // number
  const number = shape.number.safeParse(unsafeData.number);
  if (number.success) {
    data.number = number.data;
  } else {
    error.number = number.error;
  }
  // detentionNumber
  const detentionNumber = shape.detentionNumber.safeParse(unsafeData.detentionNumber);
  if (detentionNumber.success) {
    data.detentionNumber = detentionNumber.data;
  } else {
    error.detentionNumber = detentionNumber.error;
  }
  // description
  const description = shape.description.safeParse(unsafeData.description);
  if (description.success) {
    data.description = description.data;
  } else {
    error.description = description.error;
  }
  // hasVisibleInjuries
  const hasVisibleInjuries = shape.hasVisibleInjuries.safeParse(unsafeData.hasVisibleInjuries);
  if (hasVisibleInjuries.success) {
    data.hasVisibleInjuries = hasVisibleInjuries.data;
  } else {
    error.hasVisibleInjuries = hasVisibleInjuries.error;
  }
  // visibleInjuriesDescription
  const visibleInjuriesDescription = shape.visibleInjuriesDescription.safeParse(unsafeData.visibleInjuriesDescription);
  if (visibleInjuriesDescription.success) {
    data.visibleInjuriesDescription = visibleInjuriesDescription.data;
  } else {
    error.visibleInjuriesDescription = visibleInjuriesDescription.error;
  }
  // hasReportedIllness
  const hasReportedIllness = shape.hasReportedIllness.safeParse(unsafeData.hasReportedIllness);
  if (hasReportedIllness.success) {
    data.hasReportedIllness = hasReportedIllness.data;
  } else {
    error.hasReportedIllness = hasReportedIllness.error;
  }
  // reportedIllnessDescription
  const reportedIllnessDescription = shape.reportedIllnessDescription.safeParse(unsafeData.reportedIllnessDescription);
  if (reportedIllnessDescription.success) {
    data.reportedIllnessDescription = reportedIllnessDescription.data;
  } else {
    error.reportedIllnessDescription = reportedIllnessDescription.error;
  }
  // isInVulnerableGroup
  const isInVulnerableGroup = shape.isInVulnerableGroup.safeParse(unsafeData.isInVulnerableGroup);
  if (isInVulnerableGroup.success) {
    data.isInVulnerableGroup = isInVulnerableGroup.data;
  } else {
    error.isInVulnerableGroup = isInVulnerableGroup.error;
  }
  // vulnerableGroupDescription
  const vulnerableGroupDescription = shape.vulnerableGroupDescription.safeParse(unsafeData.vulnerableGroupDescription);
  if (vulnerableGroupDescription.success) {
    data.vulnerableGroupDescription = vulnerableGroupDescription.data;
  } else {
    error.vulnerableGroupDescription = vulnerableGroupDescription.error;
  }
  // isInDelinquentGroup
  const isInDelinquentGroup = shape.isInDelinquentGroup.safeParse(unsafeData.isInDelinquentGroup);
  if (isInDelinquentGroup.success) {
    data.isInDelinquentGroup = isInDelinquentGroup.data;
  } else {
    error.isInDelinquentGroup = isInDelinquentGroup.error;
  }
  // delinquentGroupDescription
  const delinquentGroupDescription = shape.delinquentGroupDescription.safeParse(unsafeData.delinquentGroupDescription);
  if (delinquentGroupDescription.success) {
    data.delinquentGroupDescription = delinquentGroupDescription.data;
  } else {
    error.delinquentGroupDescription = delinquentGroupDescription.error;
  }
  // hasTrustedPerson
  const hasTrustedPerson = shape.hasTrustedPerson.safeParse(unsafeData.hasTrustedPerson);
  if (hasTrustedPerson.success) {
    data.hasTrustedPerson = hasTrustedPerson.data;
  } else {
    error.hasTrustedPerson = hasTrustedPerson.error;
  }
  // wasInformedOfRights
  const wasInformedOfRights = shape.wasInformedOfRights.safeParse(unsafeData.wasInformedOfRights);
  if (wasInformedOfRights.success) {
    data.wasInformedOfRights = wasInformedOfRights.data;
  } else {
    error.wasInformedOfRights = wasInformedOfRights.error;
  }
  // wasObjectRelatedToIncidentFound
  const wasObjectRelatedToIncidentFound = shape.wasObjectRelatedToIncidentFound.safeParse(
    unsafeData.wasObjectRelatedToIncidentFound,
  );
  if (wasObjectRelatedToIncidentFound.success) {
    data.wasObjectRelatedToIncidentFound = wasObjectRelatedToIncidentFound.data;
  } else {
    error.wasObjectRelatedToIncidentFound = wasObjectRelatedToIncidentFound.error;
  }
  // wasBelongingsCollected
  const wasBelongingsCollected = shape.wasBelongingsCollected.safeParse(unsafeData.wasBelongingsCollected);
  if (wasBelongingsCollected.success) {
    data.wasBelongingsCollected = wasBelongingsCollected.data;
  } else {
    error.wasBelongingsCollected = wasBelongingsCollected.error;
  }
  // belongings
  const belongings = shape.belongings.safeParse(unsafeData.belongings);
  if (belongings.success) {
    data.belongings = belongings.data;
  } else {
    error.belongings = belongings.error;
  }
  // isDetentionPlaceSameAsInterventionPlace
  const isDetentionPlaceSameAsInterventionPlace = shape.isDetentionPlaceSameAsInterventionPlace.safeParse(
    unsafeData.isDetentionPlaceSameAsInterventionPlace,
  );
  if (isDetentionPlaceSameAsInterventionPlace.success) {
    data.isDetentionPlaceSameAsInterventionPlace = isDetentionPlaceSameAsInterventionPlace.data;
  }
  // observations
  const observations = shape.observations.safeParse(unsafeData.observations);
  if (observations.success) {
    data.observations = observations.data;
  } else {
    error.observations = observations.error;
  }
  // firstRespondent
  const firstRespondent = shape.firstRespondent.safeParse(unsafeData.firstRespondent);
  if (firstRespondent.success) {
    data.fkFirstRespondentUserId = firstRespondent.data?.id;
  } else {
    error.firstRespondent = firstRespondent.error;
  }
  // transferExternalInstitution
  const transferExternalInstitution = shape.transferExternalInstitution.safeParse(
    unsafeData.transferExternalInstitution,
  );
  if (transferExternalInstitution.success) {
    data.fkTransferExternalInstitutionId = transferExternalInstitution.data?.id;
    if (transferExternalInstitution.data?.action === 'create') {
      await db
        .insert(catExternalInstitution)
        .values({
          id: transferExternalInstitution.data.id,
          key: genrateKey(transferExternalInstitution.data.name),
          [locale]: transferExternalInstitution.data.name,
          fkLastUserId: userId,
          fkLastDependencyId: dependencyId,
        })
        .catch((err) => {
          error.transferExternalInstitution = z.ZodError.create([
            {
              fatal: true,
              message: err.message || 'Error creating institution',
              code: z.ZodIssueCode.custom,
              path: ['transferExternalInstitution', 'authority'],
            },
          ]);
        });
    }
  }
  // detainedData
  if (unsafeData.detainedData) {
    let id: string | null = unsafeData.detainedData.id;
    if (unsafeData.detainedData.action === 'create' || unsafeData.detainedData.action === 'update') {
      const newCitizenError = await saveCitizen({
        unsafeData: unsafeData.detainedData,
        userId,
        dependencyId,
        locale,
        objectPath: 'detainedData',
      });
      if (newCitizenError) {
        error.detainedData = newCitizenError;
        if (newCitizenError.id) {
          id = null;
        }
      }
    }
    data.fkDetainedCitizenId = id;
  }
  // trustedPerson
  if (unsafeData.trustedPerson) {
    let id: string | null = unsafeData.trustedPerson.id;
    if (unsafeData.trustedPerson.action === 'create' || unsafeData.trustedPerson.action === 'update') {
      const newCitizenError = await saveCitizen({
        unsafeData: unsafeData.trustedPerson,
        userId,
        dependencyId,
        locale,
        objectPath: 'trustedPerson',
      });
      if (newCitizenError) {
        error.trustedPerson = newCitizenError;
        if (newCitizenError.id) {
          id = null;
        }
      }
    }
    data.fkTrustedPersonCitizenId = id;
  }
  // detentionAddress
  if (unsafeData.detentionAddress) {
    let id: string | null = unsafeData.detentionAddress.id;
    if (unsafeData.detentionAddress.action === 'create' || unsafeData.detentionAddress.action === 'update') {
      const placeError = await savePlace({
        unsafeData: unsafeData.detentionAddress,
        userId,
        dependencyId,
        locale,
        objectPath: 'detentionAddress',
      });
      if (placeError) {
        error.address = placeError;
        if (placeError.id) {
          id = null;
        }
      }
    }
    data.fkDetentionAddressPlaceId = id;
  }
  // id
  const id = shape.id.safeParse(unsafeData.id);
  if (id.success && id.data) {
    data.id = id.data;
  }
  const result: {
    data?: Detained;
    error?: { [key: string]: unknown };
  } = {};

  if (Object.keys(data).length > 5) {
    result.data = data;
  }
  if (Object.keys(error).length > 0) {
    result.error = error;
  }
  return result;
};

export const save = async (req: Request, res: Response) => {
  const { locale: localeQS } = req.query;
  let locale = getLocale();
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  const userId = req.userId as string;
  const dependencyId = req.dependencyId as string;

  try {
    const unsafeBody = req.body as z.infer<typeof detainedSchema>;
    const { data, error } = await validate({
      unsafeData: unsafeBody,
      userId,
      dependencyId,
      locale,
    });
    if (!data) {
      res.status(HttpStatus.NOT_MODIFIED).json({ message: 'Not modified', error });
      return;
    }

    const updated = await db.insert(systemModuleControlIphDetained).values(data).onConflictDoUpdate({
      target: systemModuleControlIphDetained.id,
      set: data,
    });

    if ((updated.rowCount ?? 0) === 0) {
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message: 'Error creating detained', error });
      return;
    }
    res.status(HttpStatus.CREATED).json({ message: 'Detained saved', error });
  } catch (error) {
    const err = error as Error;
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message: err.message, error });
  }
};

export const saveAll = async (req: Request, res: Response) => {
  const { locale: localeQS } = req.query;
  let locale = getLocale();
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  const userId = req.userId as string;
  const dependencyId = req.dependencyId as string;

  try {
    const arraySchema = detainedSchema.array();
    const unsafeBody = req.body as z.infer<typeof arraySchema>;

    const promises = unsafeBody.map(async (detained) => {
      const { data, error } = await validate({
        unsafeData: detained,
        userId,
        dependencyId,
        locale,
      });
      if (!data) {
        return { [detained.id]: error };
      }
      const updated = await db.insert(systemModuleControlIphDetained).values(data).onConflictDoUpdate({
        target: systemModuleControlIphDetained.id,
        set: data,
      });
      if ((updated.rowCount ?? 0) === 0) {
        return { [detained.id]: { ...error, id: 'Error saving detained' } };
      }
      return { [detained.id]: error };
    });

    const results = await Promise.allSettled(promises);

    res.status(HttpStatus.CREATED).json({ message: 'Detainees saved', error: results });
  } catch (error) {
    const err = error as Error;
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message: err.message, error });
  }
};
// const remove = () => {};
