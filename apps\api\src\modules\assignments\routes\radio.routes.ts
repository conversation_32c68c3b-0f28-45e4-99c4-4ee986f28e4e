import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/radio.controller';

const radioAssignRouter = Router();

radioAssignRouter.get('/', getAll);
radioAssignRouter.get('/search', searchPaginated);
radioAssignRouter.get('/:id', getOne);
radioAssignRouter.post('/', create);
radioAssignRouter.put('/:id', update);
radioAssignRouter.delete('/:id', deleteOne);

export default radioAssignRouter;
