import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/gender.controller';

const catGenderRouter = Router();

catGenderRouter.get('/', getAll);
catGenderRouter.get('/search', searchPaginated);
catGenderRouter.get('/:id', getOne);
catGenderRouter.post('/', create);
catGenderRouter.put('/:id', update);
catGenderRouter.delete('/:id', deleteOne);

export default catGenderRouter;
