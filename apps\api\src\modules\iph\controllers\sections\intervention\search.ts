import type { Request, Response } from 'express';
import db from '@/db';
import {
  catMunicipality,
  catSettlement,
  catState,
  systemModuleControlIphIntervention,
  systemModuleControlPlaces,
} from '@repo/shared-drizzle/schemas';
import { HttpStatus } from '@/utils/http_status';
import { getLocale, type Locale } from '@/utils/locale_validator';
import { Logger } from '@/utils/logger';
import { ModuleName } from '@/utils/module_names';
import { and, asc, eq, gt, sql, type SQL } from 'drizzle-orm';

export const query = ({
  locale,
  query,
  limit,
  lastId,
}: { locale: Locale; query?: string | null; limit: number; lastId?: string | null }) => {
  const filters: SQL[] = [];
  if (query) {
    // TODO: search by referenceNumber, fileNumber and status
    // filters.push(SQL`reference_number ilike ${query}`);
  }

  if (lastId) {
    filters.push(gt(systemModuleControlIphIntervention.id, lastId));
  }
  return db
    .select({
      id: systemModuleControlIphIntervention.id,
      arrivalAt: systemModuleControlIphIntervention.arrivalAt,
      wasSiteInspected: systemModuleControlIphIntervention.wasSiteInspected,
      wasRelatedObjectFound: systemModuleControlIphIntervention.wasRelatedObjectFound,
      wasSitePreserved: systemModuleControlIphIntervention.wasSitePreserved,
      wasSitePrioritized: systemModuleControlIphIntervention.wasSitePrioritized,
      isSocialRisk: systemModuleControlIphIntervention.isSocialRisk,
      socialRiskDescription: systemModuleControlIphIntervention.socialRiskDescription,
      isNaturalRisk: systemModuleControlIphIntervention.isNaturalRisk,
      naturalRiskDescription: systemModuleControlIphIntervention.naturalRiskDescription,
      interventionAddress: {
        id: systemModuleControlPlaces.id,
        name: systemModuleControlPlaces.name,
        type: systemModuleControlPlaces.type,
        state: sql`json_build_object('id', ${catState.id}, 'name', ${catState[locale]})`,
        municipality: sql`json_build_object('id', ${catMunicipality.id}, 'name', ${catMunicipality[locale]})`,
        colony: sql`json_build_object('id', ${catSettlement.id}, 'name', ${catSettlement[locale]})`,
        zipCode: systemModuleControlPlaces.zipCode,
        street: systemModuleControlPlaces.street,
        number: systemModuleControlPlaces.number,
        interiorNumber: systemModuleControlPlaces.interiorNumber,
        betweenStreet1: systemModuleControlPlaces.betweenStreet1,
        betweenStreet2: systemModuleControlPlaces.betweenStreet2,
        reference: systemModuleControlPlaces.reference,
        location: systemModuleControlPlaces.location,
        sectors: systemModuleControlPlaces.sectors,
      },
    })
    .from(systemModuleControlIphIntervention)
    .leftJoin(
      systemModuleControlPlaces,
      eq(systemModuleControlPlaces.id, systemModuleControlIphIntervention.fkInterventionAddressPlaceId),
    )
    .leftJoin(catState, eq(catState.id, systemModuleControlPlaces.fkStateId))
    .leftJoin(catMunicipality, eq(catMunicipality.id, systemModuleControlPlaces.fkMunicipalityId))
    .leftJoin(catSettlement, eq(catSettlement.id, systemModuleControlPlaces.fkColonyId))
    .where(and(...filters, eq(systemModuleControlIphIntervention.isDeleted, false)))
    .orderBy(asc(systemModuleControlIphIntervention.id))
    .limit(limit);
};

export const search = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, q: qQS, locale: localeQS } = req.query;

  let lastId = null;
  let limit = 10;
  let q = null;
  let locale: Locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  try {
    const queryResult = await query({ locale, limit, lastId, query: q });
    res.status(HttpStatus.OK).json(queryResult);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      module: ModuleName.IPH,
      method: '/iph/section/intervention/search - searchIntervention',
      source: req.headers['user-agent'],
      stack,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};
