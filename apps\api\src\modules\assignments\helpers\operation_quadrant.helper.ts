import {
  systemModuleAssignmentOperationQuadrant,
  systemModuleControlFatigue,
  systemModuleControlOperationQuadrant,
} from '@repo/shared-drizzle/schemas';
import { and, eq, type ExtractTablesWithRelations, inArray, ne, or } from 'drizzle-orm';
import type { NodePgQueryResultHKT } from 'drizzle-orm/node-postgres';
import type { PgTransaction } from 'drizzle-orm/pg-core';
import { z } from 'zod';
import type { OperationQuadrantAssignment } from '../interfaces/operation_quadrant.interface';
import SystemModuleCache from '@/utils/system_module_cache';
import { SystemModuleKeys } from '@/utils/system_module_keys';

export const operationQuadrantAssignmentBodySchema = z.object({
  fkUserId: z.string(),
  fkOperationQuadrantId: z.string(),
  fkFatigueId: z.string(),
  assignmentDate: z.string(),
  assignmentEndDate: z.string().nullable().optional(),
  comments: z.string().nullable().optional(),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});

export const createOperationQuadrantAssignment = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  operationQuadrantData: OperationQuadrantAssignment[],
) => {
  // Validar payload
  if (operationQuadrantData.length === 0) {
    throw new Error('No se encontraron cuadrantes de operación');
  }
  // Validar y parsear todos los datos
  const parsedData = await Promise.all(
    operationQuadrantData.map((data) => operationQuadrantAssignmentBodySchema.parseAsync({ ...data })),
  );

  const systemModuleCache = SystemModuleCache.getInstance();
  const systemModule = systemModuleCache.getSystemModuleByKey(
    SystemModuleKeys.CTRL_ASIGNACIONES_CUADRANTES_OPERACIONALES,
  );
  if (!systemModule) {
    throw new Error('System module CTRL_ASIGNACIONES_CUADRANTES_OPERACIONALES not found');
  }
  // Validar todos los cuadrantes de operación existan
  const quadrantsIds = parsedData.map((data) => data.fkOperationQuadrantId);

  const existingQuadrants = await trx
    .select({ id: systemModuleControlOperationQuadrant.id })
    .from(systemModuleControlOperationQuadrant)
    .where(inArray(systemModuleControlOperationQuadrant.id, quadrantsIds));
  const existingQuadrantsIds = existingQuadrants.map((quadrant) => quadrant.id);
  const missingQuadrants = quadrantsIds.filter((id) => !existingQuadrantsIds.includes(id));
  if (missingQuadrants.length > 0) {
    throw new Error(`Los siguientes cuadrantes de operación no existen: ${missingQuadrants.join(', ')}`);
  }
  // Validar que no existan asignaciones duplicadas
  const existingAssignments = await trx
    .select({
      fkUserId: systemModuleAssignmentOperationQuadrant.fkUserId,
      fkOperationQuadrantId: systemModuleAssignmentOperationQuadrant.fkOperationQuadrantId,
      fkFatigueId: systemModuleAssignmentOperationQuadrant.fkFatigueId,
    })
    .from(systemModuleAssignmentOperationQuadrant)
    .where(
      or(
        ...parsedData.map((data) =>
          and(
            eq(systemModuleAssignmentOperationQuadrant.fkOperationQuadrantId, data.fkOperationQuadrantId),
            eq(systemModuleAssignmentOperationQuadrant.fkUserId, data.fkUserId),
            eq(systemModuleAssignmentOperationQuadrant.fkFatigueId, data.fkFatigueId),
          ),
        ),
      ),
    );
  if (existingAssignments.length > 0) {
    const duplicates = existingAssignments.map(
      (assign) =>
        `Cuadrante ${assign.fkOperationQuadrantId} para usuario/elemento ${assign.fkUserId} en despliegue ${assign.fkFatigueId}`,
    );
    throw new Error(`Ya existen asignaciones para: ${duplicates.join(', ')}`);
  }
  // Insertar todas las asignaciones
  const insertData = parsedData.map((data) => ({
    ...data,
    assignmentEndDate: null,
    fkSystemModuleId: systemModule.id,
  }));
  const newAssignment = await trx.insert(systemModuleAssignmentOperationQuadrant).values(insertData).returning({
    id: systemModuleAssignmentOperationQuadrant.id,
    fkUserId: systemModuleAssignmentOperationQuadrant.fkUserId,
    fkOperationQuadrantId: systemModuleAssignmentOperationQuadrant.fkOperationQuadrantId,
    fkFatigueId: systemModuleAssignmentOperationQuadrant.fkFatigueId,
    assignmentDate: systemModuleAssignmentOperationQuadrant.assignmentDate,
    assignmentEndDate: systemModuleAssignmentOperationQuadrant.assignmentEndDate,
    comments: systemModuleAssignmentOperationQuadrant.comments,
  });
  return newAssignment;
};

export const validateOperationQuadrantAssignmentInFatigue = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkOperationQuadrantId: string,
  fkFatigueId: string,
  fkOperationQuadrantAssignmentId: string,
) => {
  const existingAssignment = await trx
    .select()
    .from(systemModuleAssignmentOperationQuadrant)
    .where(
      and(
        eq(systemModuleAssignmentOperationQuadrant.fkOperationQuadrantId, fkOperationQuadrantId),
        eq(systemModuleAssignmentOperationQuadrant.fkFatigueId, fkFatigueId),
        ne(systemModuleAssignmentOperationQuadrant.id, fkOperationQuadrantAssignmentId),
      ),
    )
    .limit(1);
  if (existingAssignment.length > 0) {
    throw new Error('Ya existe una asignación con el mismo cuadrante de operación en el despliegue indicado');
  }
};

export const validateOperationQuadrantActiveAssignment = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkOperationQuadrantId: string,
  fkOperationQuadrantAssignmentId: string,
) => {
  const activeOperationQuadrantAssignment = await trx
    .select()
    .from(systemModuleAssignmentOperationQuadrant)
    .leftJoin(
      systemModuleControlFatigue,
      eq(systemModuleAssignmentOperationQuadrant.fkFatigueId, systemModuleControlFatigue.id),
    )
    .where(
      and(
        eq(systemModuleControlFatigue.status, 'active'),
        eq(systemModuleAssignmentOperationQuadrant.fkOperationQuadrantId, fkOperationQuadrantId),
        eq(systemModuleAssignmentOperationQuadrant.isDeleted, false),
        ne(systemModuleAssignmentOperationQuadrant.id, fkOperationQuadrantAssignmentId),
      ),
    )
    .limit(1);
  if (activeOperationQuadrantAssignment.length > 0) {
    throw new Error('El cuadrante de operación esta asignada a otro elemento en un despliegue activo');
  }
};

export const handleUserChangeInOperationQuadrantAssign = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkUserId: string,
  fkOperationQuadrantId: string,
  fkFatigueId: string,
  fkOperationQuadrantAssignmentId: string,
) => {
  const existingAssignment = await trx
    .select()
    .from(systemModuleAssignmentOperationQuadrant)
    .where(
      and(
        eq(systemModuleAssignmentOperationQuadrant.fkUserId, fkUserId),
        eq(systemModuleAssignmentOperationQuadrant.fkOperationQuadrantId, fkOperationQuadrantId),
        eq(systemModuleAssignmentOperationQuadrant.fkFatigueId, fkFatigueId),
        ne(systemModuleAssignmentOperationQuadrant.id, fkOperationQuadrantAssignmentId), // Excluir la asignación actual
      ),
    )
    .limit(1);

  if (existingAssignment.length > 0) {
    throw new Error('El usuario ya tiene una asignación con este cuadrante de operación en el mismo despliegue');
  }
};

export const deleteOperationQuadrantAssignment = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  ids: string[],
  fkLastUserId: string,
  fkLastDependencyId: string,
) => {
  for (const id of ids) {
    await trx
      .update(systemModuleAssignmentOperationQuadrant)
      .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId, fkLastDependencyId })
      .where(eq(systemModuleAssignmentOperationQuadrant.id, id));
  }
};
