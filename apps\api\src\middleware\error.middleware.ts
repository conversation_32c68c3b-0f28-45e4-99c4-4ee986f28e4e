import type { Request, Response, NextFunction } from 'express';
import { Logger } from '@/utils/logger';

const errorMiddleware = (err: Error, req: Request, res: Response, next: NextFunction) => {
  Logger.getInstance().error(err.message, {
    method: 'errorMiddleware',
    module: 'middleware',
    source: req.headers['user-agent'],
    stack: err.stack,
  });
  res.status(500).json({ message: err.message });
  next();
};

export default errorMiddleware;
