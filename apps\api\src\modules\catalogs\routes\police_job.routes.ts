import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/police_job.controller';

const catPoliceJobRouter = Router();

catPoliceJobRouter.get('/', getAll);
catPoliceJobRouter.get('/search', searchPaginated);
catPoliceJobRouter.get('/:id', getOne);
catPoliceJobRouter.post('/', create);
catPoliceJobRouter.put('/:id', update);
catPoliceJobRouter.delete('/:id', deleteOne);

export default catPoliceJobRouter;
