import { VITE_BASE_URL } from '@/config/env';
import type { TokenResponse, UserInfo } from '@/types/api.types';

export const fetchUserInfo = async (): Promise<UserInfo> => {
  const response = await fetch('/info');
  if (!response.ok) {
    throw new Error('Error al cargar la información del usuario');
  }
  return response.json();
};

export const generateToken = async (userInfo: UserInfo): Promise<TokenResponse> => {
  const response = await fetch(`${VITE_BASE_URL}/auth/token/generate`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      userId: userInfo.userId,
      dependencyId: userInfo.dependencyId,
    },
  });

  if (!response.ok) {
    throw new Error('Error al generar el token de autenticación');
  }

  return response.json();
};

export interface UserResponse {
  user: {
    id: string;
    alias: string;
    email: string | null;
    lastActivityAt: string;
  };
  citizen: {
    id: string;
    name: string;
    firstSurname: string;
    secondSurname: string;
    rfc: string | null;
    [key: string]: unknown;
  };
  employment: {
    cup: string;
    nameCurrentJobPosition: string;
    nameJobStatus: string;
    nameInstitution: string;
    entryDate: string;
    [key: string]: unknown;
  };
}

export const getUsers = async (): Promise<UserResponse[]> => {
  const response = await fetch(`${VITE_BASE_URL}/user?deleted=false&disabled=false&locale=es`);

  if (!response.ok) {
    throw new Error('Error al obtener la lista de usuarios');
  }

  return response.json();
};
