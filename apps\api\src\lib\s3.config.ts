import { S3Client, type S3ClientConfig } from '@aws-sdk/client-s3';

const s3Config: S3ClientConfig = {
  endpoint: process.env.AWS_S3_ENDPOINT || 'https://files-argus.yimi.dev',
  credentials: {
    accessKeyId: process.env.AWS_S3_ACCESS_KEY || 'nytEpywLMvtvPaCAeeOu',
    secretAccessKey: process.env.AWS_S3_SECRET_KEY || '',
  },
  region: process.env.AWS_S3_REGION,
  forcePathStyle: true,
};

export const s3Client = new S3Client(s3Config);
