import { z } from 'zod';

export const ammunitionUseBodySchema = z.object({
  fkUserId: z.string(),
  fkAmmunitionId: z.string(),
  fkWeaponId: z.string(),
  fkCaliberId: z.string(),
  quantity: z.number().default(0),
  reasonsForUse: z.string(),
  fkFatigueId: z.string().nullable().optional(),
  fkIphId: z.string().nullable().optional(),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});
