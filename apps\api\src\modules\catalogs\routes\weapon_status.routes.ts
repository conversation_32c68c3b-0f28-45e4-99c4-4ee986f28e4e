/* import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/weapon_status.controller';

const catWeaponStatusRouter = Router();

catWeaponStatusRouter.get('/', getAll);
catWeaponStatusRouter.get('/search', searchPaginated);
catWeaponStatusRouter.get('/:id', getOne);
catWeaponStatusRouter.post('/', create);
catWeaponStatusRouter.put('/:id', update);
catWeaponStatusRouter.delete('/:id', deleteOne);

export default catWeaponStatusRouter;
 */
