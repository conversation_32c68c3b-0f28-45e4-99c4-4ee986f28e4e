import type { Request, Response } from 'express';
import db from '@/db';
import { systemModuleControlIphGeneral } from '@repo/shared-drizzle/schemas';
import { and, asc, eq, gt, type SQL } from 'drizzle-orm';
import { HttpStatus } from '@/utils/http_status';
import { Logger } from '@/utils/logger';
import { ModuleName } from '@/utils/module_names';

export const query = ({ query, limit, lastId }: { query?: string | null; limit: number; lastId?: string | null }) => {
  const filters: SQL[] = [];
  if (query) {
    // TODO: search by referenceNumber, fileNumber and status
    // filters.push(SQL`reference_number ilike ${query}`);
  }

  if (lastId) {
    filters.push(gt(systemModuleControlIphGeneral.id, lastId));
  }

  return db
    .select()
    .from(systemModuleControlIphGeneral)
    .where(and(...filters, eq(systemModuleControlIphGeneral.isDeleted, false)))
    .orderBy(asc(systemModuleControlIphGeneral.id))
    .limit(limit);
};

export const search = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, q: qQS } = req.query;

  let lastId = null;
  let limit = 10;
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  try {
    const queryResult = await query({ limit, lastId, query: q });
    res.status(HttpStatus.OK).json(queryResult);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      module: ModuleName.IPH,
      method: '/iph/section/general/search - searchGeneral',
      source: req.headers['user-agent'],
      stack,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};
