import type { Request, Response } from 'express';
import {
  catSize,
  catTacticalEquipmentType,
  systemModuleAssignmentTacticalEquipment,
  systemModuleControlSupplier,
  systemModuleControlTacticalEquipment,
  systemModuleUserKardexTacticalEquipment,
} from '@repo/shared-drizzle/schemas';
import db from '@/db';
import { and, asc, desc, eq, gt, isNull, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { getLocale } from '@/utils/locale_validator';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';
import { tacticalEquipmentBodySchema } from '../helpers/tactical_equipment.helper';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS } = req.query;
  let [deleted, disabled, locale] = [false, false, getLocale()];

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlTacticalEquipment.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlTacticalEquipment.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlTacticalEquipment.id,
        serialNumber: systemModuleControlTacticalEquipment.serialNumber,
        fkTacticalEquipmentTypeId: systemModuleControlTacticalEquipment.fkTacticalEquipmentTypeId,
        nameTacticalEquipmentType: catTacticalEquipmentType[locale],
        expirationDate: systemModuleControlTacticalEquipment.expirationDate,
        cost: systemModuleControlTacticalEquipment.cost,
        description: systemModuleControlTacticalEquipment.description,
        fkSizeId: systemModuleControlTacticalEquipment.fkSizeId,
        nameSize: catSize[locale],
        tacticalEquipmentStatus: systemModuleControlTacticalEquipment.tacticalEquipmentStatus,
        acquisitionDate: systemModuleControlTacticalEquipment.acquisitionDate,
        fkSupplierId: systemModuleControlTacticalEquipment.fkSupplierId,
        nameSupplier: systemModuleControlSupplier.name,
      })
      .from(systemModuleControlTacticalEquipment)
      .leftJoin(
        catTacticalEquipmentType,
        eq(systemModuleControlTacticalEquipment.fkTacticalEquipmentTypeId, catTacticalEquipmentType.id),
      )
      .leftJoin(catSize, eq(systemModuleControlTacticalEquipment.fkSizeId, catSize.id))
      .leftJoin(
        systemModuleControlSupplier,
        eq(systemModuleControlTacticalEquipment.fkSupplierId, systemModuleControlSupplier.id),
      )
      .where(and(...filters));
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TACTICAL_EQUIPMENT}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const result = await db
      .select({
        id: systemModuleControlTacticalEquipment.id,
        serialNumber: systemModuleControlTacticalEquipment.serialNumber,
        fkTacticalEquipmentTypeId: systemModuleControlTacticalEquipment.fkTacticalEquipmentTypeId,
        nameTacticalEquipmentType: catTacticalEquipmentType[locale],
        expirationDate: systemModuleControlTacticalEquipment.expirationDate,
        cost: systemModuleControlTacticalEquipment.cost,
        description: systemModuleControlTacticalEquipment.description,
        fkSizeId: systemModuleControlTacticalEquipment.fkSizeId,
        nameSize: catSize[locale],
        tacticalEquipmentStatus: systemModuleControlTacticalEquipment.tacticalEquipmentStatus,
        acquisitionDate: systemModuleControlTacticalEquipment.acquisitionDate,
        fkSupplierId: systemModuleControlTacticalEquipment.fkSupplierId,
        nameSupplier: systemModuleControlSupplier.name,
      })
      .from(systemModuleControlTacticalEquipment)
      .leftJoin(
        catTacticalEquipmentType,
        eq(systemModuleControlTacticalEquipment.fkTacticalEquipmentTypeId, catTacticalEquipmentType.id),
      )
      .leftJoin(catSize, eq(systemModuleControlTacticalEquipment.fkSizeId, catSize.id))
      .leftJoin(
        systemModuleControlSupplier,
        eq(systemModuleControlTacticalEquipment.fkSupplierId, systemModuleControlSupplier.id),
      )
      .where(eq(systemModuleControlTacticalEquipment.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TACTICAL_EQUIPMENT}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    // Validar assignmentDate
    if (req.body.expirationDate) {
      const date = new Date(req.body.expirationDate.replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(date.getTime())) {
        throw new Error('El campo expirationDate no es una fecha valida');
      }
    }
    // Validar acquisitionDate
    const date = new Date(req.body.acquisitionDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('El campo acquisitionDate no es una fecha valida');
    }
    const insertData = tacticalEquipmentBodySchema.parse({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_EQUIPO_TACTICO);
    if (!systemModule) {
      throw new Error('System module CTRL_EQUIPO_TACTICO not found');
    }
    const result = await db.insert(systemModuleControlTacticalEquipment).values({
      ...insertData,
      fkSystemModuleId: systemModule.id,
    });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TACTICAL_EQUIPMENT} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;
    // Validar assignmentDate
    if (req.body.expirationDate) {
      const date = new Date(req.body.expirationDate.replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(date.getTime())) {
        throw new Error('El campo expirationDate no es una fecha valida');
      }
    }
    // Validar acquisitionDate
    const date = new Date(req.body.acquisitionDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('El campo acquisitionDate no es una fecha valida');
    }
    // Validar payload
    const updateData = await tacticalEquipmentBodySchema.parseAsync({ ...req.body });
    const result = await db.transaction(async (trx) => {
      // Obtener el equipo táctico actual
      const tacticalEquipment = await trx
        .select()
        .from(systemModuleControlTacticalEquipment)
        .where(eq(systemModuleControlTacticalEquipment.id, id))
        .limit(1);
      if (tacticalEquipment.length === 0) {
        throw new Error('Equipo táctico no encontrado');
      }

      // Obtener la asignación activa del equipo táctico
      const activeAssignment = await trx
        .select()
        .from(systemModuleAssignmentTacticalEquipment)
        .where(
          and(
            eq(systemModuleAssignmentTacticalEquipment.fkTacticalEquipmentId, id),
            isNull(systemModuleAssignmentTacticalEquipment.assignmentEndDate), // Solo asignaciones activas
          ),
        )
        .limit(1);

      if (activeAssignment.length > 0) {
        // Definir los campos a verificar como un array de strings literales
        const fieldsToCheck = ['expirationDate', 'fkSizeId', 'tacticalEquipmentStatus'] as const;

        // Extraer el tipo de las claves
        type FieldsToCheck = (typeof fieldsToCheck)[number];

        // Verificar si alguno de los campos relevantes cambió
        const hasChanged = fieldsToCheck.some(
          (field) => updateData[field as FieldsToCheck] !== tacticalEquipment[0][field as FieldsToCheck],
        );

        if (hasChanged) {
          // Obtener el último kardex del usuario asignado
          const lastKardex = await trx
            .select()
            .from(systemModuleUserKardexTacticalEquipment)
            .where(
              and(
                eq(systemModuleUserKardexTacticalEquipment.fkUserId, activeAssignment[0].fkUserId),
                eq(systemModuleUserKardexTacticalEquipment.fkTacticalEquipmentId, id),
                isNull(systemModuleUserKardexTacticalEquipment.assignmentEndDate), // Solo kardex sin fecha de fin
              ),
            )
            .orderBy(desc(systemModuleUserKardexTacticalEquipment.createdAt))
            .limit(1);

          if (lastKardex.length > 0) {
            // Actualizar el último kardex del usuario asignado
            await trx
              .update(systemModuleUserKardexTacticalEquipment)
              .set({
                expirationDate: updateData.expirationDate,
                fkSizeId: updateData.fkSizeId,
                tacticalEquipmentStatus: updateData.tacticalEquipmentStatus,
                fkLastUserId: updateData.fkLastUserId,
                fkLastDependencyId: updateData.fkLastDependencyId,
              })
              .where(eq(systemModuleUserKardexTacticalEquipment.id, lastKardex[0].id));
          }
        }
      }

      // Actualizar el equipo táctico
      const updateResult = await trx
        .update(systemModuleControlTacticalEquipment)
        .set({ ...updateData })
        .where(eq(systemModuleControlTacticalEquipment.id, id));

      return updateResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TACTICAL_EQUIPMENT}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Verificar si el equipo táctico está asignado actualmente a un usuario
      const activeAssignment = await trx
        .select()
        .from(systemModuleAssignmentTacticalEquipment)
        .where(
          and(
            eq(systemModuleAssignmentTacticalEquipment.fkTacticalEquipmentId, id),
            isNull(systemModuleAssignmentTacticalEquipment.assignmentEndDate), // Solo asignaciones activas
          ),
        )
        .limit(1);

      if (activeAssignment.length > 0) {
        throw new Error('No se puede eliminar el equipo táctico porque está asignado actualmente a un elemento');
      }

      // Marcar el equipo táctico como eliminado
      const deleteResult = await trx
        .update(systemModuleControlTacticalEquipment)
        .set({
          isDeleted: true,
          deletedAt: new Date(),
          fkLastUserId: req.userId,
          fkLastDependencyId: req.dependencyId,
        })
        .where(eq(systemModuleControlTacticalEquipment.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TACTICAL_EQUIPMENT}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    locale: localeQS,
    q: qQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlTacticalEquipment.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlTacticalEquipment.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleControlTacticalEquipment.serialNumber, `%${q}%`));
      likeFilters.push(like(catTacticalEquipmentType[locale], `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlTacticalEquipment.expirationDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlTacticalEquipment.cost}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleControlTacticalEquipment.description, `%${q}%`));
      likeFilters.push(like(catSize[locale], `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlTacticalEquipment.acquisitionDate}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleControlSupplier.name, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleControlTacticalEquipment.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlTacticalEquipment.id,
        serialNumber: systemModuleControlTacticalEquipment.serialNumber,
        fkTacticalEquipmentTypeId: systemModuleControlTacticalEquipment.fkTacticalEquipmentTypeId,
        nameTacticalEquipmentType: catTacticalEquipmentType[locale],
        expirationDate: systemModuleControlTacticalEquipment.expirationDate,
        cost: systemModuleControlTacticalEquipment.cost,
        description: systemModuleControlTacticalEquipment.description,
        fkSizeId: systemModuleControlTacticalEquipment.fkSizeId,
        nameSize: catSize[locale],
        tacticalEquipmentStatus: systemModuleControlTacticalEquipment.tacticalEquipmentStatus,
        acquisitionDate: systemModuleControlTacticalEquipment.acquisitionDate,
        fkSupplierId: systemModuleControlTacticalEquipment.fkSupplierId,
        nameSupplier: systemModuleControlSupplier.name,
      })
      .from(systemModuleControlTacticalEquipment)
      .leftJoin(
        catTacticalEquipmentType,
        eq(systemModuleControlTacticalEquipment.fkTacticalEquipmentTypeId, catTacticalEquipmentType.id),
      )
      .leftJoin(catSize, eq(systemModuleControlTacticalEquipment.fkSizeId, catSize.id))
      .leftJoin(
        systemModuleControlSupplier,
        eq(systemModuleControlTacticalEquipment.fkSupplierId, systemModuleControlSupplier.id),
      )
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlTacticalEquipment.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TACTICAL_EQUIPMENT} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
