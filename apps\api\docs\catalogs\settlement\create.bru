meta {
  name: create
  type: http
  seq: 6
}

post {
  url: {{base}}/catalogs/settlement?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

headers {
  authorization: 
}

body:json {
  {
    "key": "test_key",
    "name": "Nombre de prueba",
    "description": "Descripcion de Nombre de prueba", // optional
    "zipCode": "49000",
    "fkStateId": "uuid Estado",
    "fkMunicipalityId": "uuid Municipio"
  }
}
