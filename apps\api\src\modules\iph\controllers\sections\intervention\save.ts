import db from '@/db';
import { type Intervention, systemModuleControlIphIntervention } from '@repo/shared-drizzle/schemas';
import { HttpStatus } from '@/utils/http_status';
import { getLocale } from '@/utils/locale_validator';
import { interventionSchema } from '@repo/types/schemas';
import type { Request, Response } from 'express';
import type z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { savePlace } from '../shared';

const saveIntervention = async (req: Request, res: Response) => {
  const { locale: localeQS } = req.query;
  let locale = getLocale();
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  const systemModule = SystemModuleCache.getInstance().getSystemModuleByKey(SystemModuleKeys.CTRL_IPH);
  if (!systemModule) {
    throw new Error('System module CTRL_IPH not found');
  }
  const userId = req.userId as string;
  const dependencyId = req.dependencyId as string;

  const error: { [key: string]: unknown } = {};
  try {
    const dataToUpdate: Intervention = {
      fkLastUserId: userId,
      fkLastDependencyId: dependencyId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    const unsafeBody = req.body as z.infer<typeof interventionSchema>;
    // validate fields individually
    // arrivalAt
    const arrivalAt = interventionSchema.shape.arrivalAt.safeParse(unsafeBody.arrivalAt);
    if (arrivalAt.success) {
      dataToUpdate.arrivalAt = arrivalAt.data;
    } else {
      error.arrivalAt = arrivalAt.error;
    }
    // wasSiteInspected
    const wasSiteInspected = interventionSchema.shape.wasSiteInspected.safeParse(unsafeBody.wasSiteInspected);
    if (wasSiteInspected.success) {
      dataToUpdate.wasSiteInspected = wasSiteInspected.data;
    } else {
      error.wasSiteInspected = wasSiteInspected.error;
    }
    // wasRelatedObjectFound
    const wasRelatedObjectFound = interventionSchema.shape.wasRelatedObjectFound.safeParse(
      unsafeBody.wasRelatedObjectFound,
    );
    if (wasRelatedObjectFound.success) {
      dataToUpdate.wasRelatedObjectFound = wasRelatedObjectFound.data;
    } else {
      error.wasRelatedObjectFound = wasRelatedObjectFound.error;
    }
    // wasSitePreserved
    const wasSitePreserved = interventionSchema.shape.wasSitePreserved.safeParse(unsafeBody.wasSitePreserved);
    if (wasSitePreserved.success) {
      dataToUpdate.wasSitePreserved = wasSitePreserved.data;
    } else {
      error.wasSitePreserved = wasSitePreserved.error;
    }
    // wasSitePrioritized
    const wasSitePrioritized = interventionSchema.shape.wasSitePrioritized.safeParse(unsafeBody.wasSitePrioritized);
    if (wasSitePrioritized.success) {
      dataToUpdate.wasSitePrioritized = wasSitePrioritized.data;
    } else {
      error.wasSitePrioritized = wasSitePrioritized.error;
    }
    // isSocialRisk
    const isSocialRisk = interventionSchema.shape.isSocialRisk.safeParse(unsafeBody.isSocialRisk);
    if (isSocialRisk.success) {
      dataToUpdate.isSocialRisk = isSocialRisk.data;
    } else {
      error.isSocialRisk = isSocialRisk.error;
    }
    // socialRiskDescription
    const socialRiskDescription = interventionSchema.shape.socialRiskDescription.safeParse(
      unsafeBody.socialRiskDescription,
    );
    if (socialRiskDescription.success) {
      dataToUpdate.socialRiskDescription = socialRiskDescription.data;
    } else {
      error.socialRiskDescription = socialRiskDescription.error;
    }
    // isNaturalRisk
    const isNaturalRisk = interventionSchema.shape.isNaturalRisk.safeParse(unsafeBody.isNaturalRisk);
    if (isNaturalRisk.success) {
      dataToUpdate.isNaturalRisk = isNaturalRisk.data;
    } else {
      error.isNaturalRisk = isNaturalRisk.error;
    }
    // naturalRiskDescription
    const naturalRiskDescription = interventionSchema.shape.naturalRiskDescription.safeParse(
      unsafeBody.naturalRiskDescription,
    );
    if (naturalRiskDescription.success) {
      dataToUpdate.naturalRiskDescription = naturalRiskDescription.data;
    } else {
      error.naturalRiskDescription = naturalRiskDescription.error;
    }
    // interventionAddress
    if (unsafeBody.interventionAddress) {
      let id: string | null = unsafeBody.interventionAddress.id;
      if (unsafeBody.interventionAddress.action === 'create' || unsafeBody.interventionAddress.action === 'update') {
        const placeError = await savePlace({
          unsafeData: unsafeBody.interventionAddress,
          userId,
          dependencyId,
          locale,
          objectPath: 'detentionAddress',
        });
        if (placeError) {
          error.address = placeError;
          if (placeError.id) {
            id = null;
          }
        }
      }
      dataToUpdate.fkInterventionAddressPlaceId = id;
    }
    // id
    const id = interventionSchema.shape.id.safeParse(unsafeBody.id);
    if (id.success && id.data) {
      dataToUpdate.id = id.data;
    }

    if (Object.keys(dataToUpdate).length < 4) {
      // if there is no data to update more than the 4 required fields return Not Modified status
      res.status(HttpStatus.NOT_MODIFIED).json({ message: 'No data to update', error });
      return;
    }
    const updated = await db.insert(systemModuleControlIphIntervention).values(dataToUpdate).onConflictDoUpdate({
      target: systemModuleControlIphIntervention.id,
      set: dataToUpdate,
    });

    res.status(HttpStatus.OK).json({ error, success: (updated.rowCount || 0) > 0 });
  } catch (_error) {
    const err = _error as Error;
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message: err, error });
  }
};

export default saveIntervention;
