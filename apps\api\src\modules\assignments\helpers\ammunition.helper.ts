import { z } from 'zod';

export const assignAmmunitionBodySchema = z.object({
  fkUserId: z.string(),
  fkControlAmmunitionId: z.string(),
  fkFatigueId: z.string(),
  quantityAssigned: z.number().default(0),
  quantityReturned: z.number().default(0),
  assignmentDate: z.string(),
  assignmentEndDate: z.string().nullable().optional(),
  comments: z.string().nullable().optional(),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});
