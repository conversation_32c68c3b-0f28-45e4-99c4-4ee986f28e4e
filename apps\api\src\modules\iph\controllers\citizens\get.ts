import type { Request, Response } from 'express';
import { HttpStatus } from '@/utils/http_status';
import { Logger } from '@/utils/logger';
import db from '@/db';
import { and, eq, is, type SelectedFields, type SQL, sql } from 'drizzle-orm';
import { systemModuleControlCitizen, systemModuleControlPlaces } from '@repo/shared-drizzle/schemas';
import type { SelectResultFields } from 'drizzle-orm/query-builders/select.types';
import { PgTimestampString } from 'drizzle-orm/pg-core';

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
function jsonBuildObject<T extends SelectedFields<any, any>>(shape: T) {
  const chunks: SQL[] = [];

  for (const entry of Object.entries(shape)) {
    const [key, value] = entry;
    if (chunks.length > 0) {
      chunks.push(sql.raw(','));
    }

    chunks.push(sql.raw(`'${key}',`));

    // json_build_object formats to ISO 8601 ...
    if (is(value, PgTimestampString)) {
      chunks.push(sql`timezone('UTC', ${value})`);
    } else {
      chunks.push(sql`${value}`);
    }
  }

  return sql<SelectResultFields<T>>`coalesce(json_build_object(${sql.join(chunks)}), '{}')`;
}

export const getAll = async (req: Request, res: Response) => {
  try {
    const result = await db
      .select({
        id: systemModuleControlCitizen.id,
        name: systemModuleControlCitizen.name,
        firstSurname: systemModuleControlCitizen.firstSurname,
        secondSurname: systemModuleControlCitizen.secondSurname,
        alias: systemModuleControlCitizen.alias,
        gender: {
          id: systemModuleControlCitizen.fkGenderId,
          // name: systemModuleControlCitizen.genderName,
        },
        bornAt: systemModuleControlCitizen.bornAt,
        phone: systemModuleControlCitizen.phone,
        email: systemModuleControlCitizen.email,
        address: {
          id: systemModuleControlPlaces.id,
          name: systemModuleControlPlaces.name,
          type: systemModuleControlPlaces.type,
          state: jsonBuildObject({
            id: systemModuleControlPlaces.fkStateId,
            // name: systemModuleControlPlaces.stateName,
          }),
          municipality: jsonBuildObject({
            id: systemModuleControlPlaces.fkMunicipalityId,
            // name: systemModuleControlPlaces.municipalityName,
          }),
          colony: jsonBuildObject({
            id: systemModuleControlPlaces.fkColonyId,
            // name: systemModuleControlPlaces.colonyName,
          }),
          zipCode: systemModuleControlPlaces.zipCode,
          street: systemModuleControlPlaces.street,
          number: systemModuleControlPlaces.number,
          interiorNumber: systemModuleControlPlaces.interiorNumber,
          betweenStreet1: systemModuleControlPlaces.betweenStreet1,
          betweenStreet2: systemModuleControlPlaces.betweenStreet2,
          reference: systemModuleControlPlaces.reference,
          location: systemModuleControlPlaces.location,
          sectors: systemModuleControlPlaces.sectors,
        },
        nationality: {
          id: systemModuleControlCitizen.fkNationalityId,
          // name: systemModuleControlCitizen.nationalityName,
        },
        documentType: {
          id: systemModuleControlCitizen.fkDocumentTypeId,
          // name: systemModuleControlCitizen.documentTypeName,
        },
        documentIdentificationNumber: systemModuleControlCitizen.documentIdentificationNumber,
        maritalStatus: {
          id: systemModuleControlCitizen.fkMaritalStatusId,
          // name: systemModuleControlCitizen.maritalStatusName,
        },
        emergencyContactId: systemModuleControlCitizen.fkEmercencyContactCitizenId,
        curp: systemModuleControlCitizen.curp,
        rfc: systemModuleControlCitizen.rfc,
      })
      .from(systemModuleControlCitizen)
      .leftJoin(
        systemModuleControlPlaces,
        eq(systemModuleControlCitizen.fkAddressPlaceId, systemModuleControlPlaces.id),
      )
      .where(and(eq(systemModuleControlCitizen.isDeleted, false), eq(systemModuleControlCitizen.isEnabled, true)));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'iph/citizens/all - getAll',
      source: req.headers['user-agent'],
      module: 'iph/citizens',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
