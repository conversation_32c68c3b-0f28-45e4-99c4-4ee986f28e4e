import { Router } from 'express';
import {
  create,
  deleteOne,
  getAll,
  getOne,
  searchPaginated,
  update,
} from '../controllers/tactical_equipment.controller';

const tacticalEquipmentAssignRouter = Router();

tacticalEquipmentAssignRouter.get('/', getAll);
tacticalEquipmentAssignRouter.get('/search', searchPaginated);
tacticalEquipmentAssignRouter.get('/:id', getOne);
tacticalEquipmentAssignRouter.post('/', create);
tacticalEquipmentAssignRouter.put('/:id', update);
tacticalEquipmentAssignRouter.delete('/:id', deleteOne);

export default tacticalEquipmentAssignRouter;
