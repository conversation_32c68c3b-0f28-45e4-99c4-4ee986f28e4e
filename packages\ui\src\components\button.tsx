import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva, type VariantProps } from 'class-variance-authority';

import { cn } from '@repo/ui/lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center  font-semibold whitespace-nowrap rounded-[var(--border-1)] text-sm ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 [&_svg]:pointer-events-none [&_svg]:shrink-0',
  {
    variants: {
      variant: {
        primary:
          'bg-[var(--btn-primary-bg)] text-[var(--fg-inverse)] hover:bg-[var(--btn-primary-hoverbg)] disabled:bg-[var(--bg-disabled)] disabled:text-[--fg-tertiary] ',
        default:
          'bg-[var(--btn-default-bg)] text-[var(--btn-default-text)] border border-[var(--btn-default-border)] hover:bg-[var(--btn-default-hoverbg)] hover:text-[var(--btn-default-hovertext)] hover:border-[var(--btn-default-hoverborder)]',
        ghost:
          'text-[var(--btn-ghost-text)] hover:bg-[var(--btn-ghost-hoverbg)] hover:text-[var(--btn-ghost-hovertext)] disabled:text-[var(--fg-disabled)] disabled:hover:bg-transparent',
        link: 'text-[var(--btn-primary-text)] underline-offset-4 hover:underline hover:text-[var(--btn-primary-hovertext)]',
        destructive:
          'bg-[var(--btn-danger-bg)] text-[var(--fg-inverse)] hover:bg-[var(--btn-danger-hoverbg)] disabled:text-[var(--fg-disabled)] disabled:bg-[var(--bg-disabled)]',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        input:
          'flex h-[38px] w-full items-center justify-between rounded-[var(--border-1)] border border-[var(--border-default)] bg-[var(--input-bg)] font-normal  text-base text-[var(--fg-secondary)] placeholder:text-[var(--fg-placeholder)] ring-offset-background focus:border-[var(--btn-primary-border)] focus:shadow-[0_0_0_2.5px_rgba(0,85,255,0.5)] focus:outline-none focus:ring-0 focus-visible:border-[var(--btn-primary-border)] focus-visible:shadow-[0_0_0_2.5px_rgba(0,85,255,0.5)] focus-visible:outline-none disabled:cursor-not-allowed disabled:bg-[var(--bg-disabled)] disabled:opacity-50 [&>span]:line-clamp-1',
      },
      size: {
        sm: 'h-[30px] text-xs px-[var(--spacing-s-3)] py-2 gap-1.5',
        default: 'h-[38px] px-[var(--spacing-s-4)] py-[11px] gap-2',
        lg: 'h-[46px] py-[var(--spacing-s-3)] px-[var(--spacing-s-5)] gap-[var(--spacing-s-2)]',
        link: 'h-fit p-0 text-[var(--btn-primary-text)] [&_svg]:text-[var(--btn-primary-text)]',
        linkLarge:
          'h-[38px] px-[var(--spacing-s-3)] py-[9px] bg-[var(--brand-1)] text-[var(--btn-primary-text)] [&_svg]:text-[var(--btn-primary-text)] gap-2 hover:no-underline hover:bg-[var(--bg-brand-secondary)] hover:text-[var(--btn-primary-hovertext)] hover:[&_svg]:text-[var(--btn-primary-hovertext)]',
        icon: 'w-[30px] h-[30px] p-[7px]',
        iconMd: 'w-[38px] h-[38px] p-[9px]',
        iconLg: 'w-[46px] h-[46px] p-[var(--spacing-s-3)]',
        input: 'w-full h-[38px] pl-[var(--spacing-s-3)] pr-[var(--spacing-s-2)] py-[var(--spacing-s-2)]',
        date: 'min-w-[174px] max-w-[174px] h-[38px] py-[var(--spacing-s-2)] pr-[var(--spacing-s-2)] pl-[var(--spacing-s-3)]',
        time: 'min-w-[120px] max-w-[120px] h-[38px] py-[var(--spacing-s-2)] pr-[var(--spacing-s-2)] pl-[var(--spacing-s-3)]',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button';
    return <Comp className={cn(buttonVariants({ variant, size, className }))} ref={ref} {...props} />;
  },
);
Button.displayName = 'Button';

export { Button, buttonVariants };
