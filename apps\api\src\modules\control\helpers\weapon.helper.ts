import { ownershipStatusValues, weaponSizeValues, weaponStatusValues } from '@repo/types/values';
import { z } from 'zod';

export const weaponBodySchema = z.object({
  serialNumber: z.string(),
  nationalRegistryNumber: z.string().nullable().optional(),
  fkWeaponTypeId: z.string(),
  weaponSize: z.enum(weaponSizeValues),
  fkAmmunitionTypeId: z.string(),
  locId: z.string().nullable().optional(),
  weaponStatus: z.enum(weaponStatusValues),
  ownershipStatus: z.enum(ownershipStatusValues),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});
