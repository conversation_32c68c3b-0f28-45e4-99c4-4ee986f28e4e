meta {
  name: searchPaginated
  type: http
  seq: 3
}

get {
  url: {{base}}/catalogs/activity_type/search?lastId&limit=10&deleted=false&disabled=false&locale=es&q=Prueba
  body: none
  auth: none
}

params:query {
  lastId: 
  limit: 10
  deleted: false
  disabled: false
  locale: es
  q: Prueba
}

headers {
  Authorization: eyJhbGciOiJIUzI1NiJ9.eyJ1c2VySWQiOiIwMTk1MTU2OC1kYzdjLTUzNzQtMmY1Ni01NzFlZGMzNDlhZTYiLCJkZXBlbmRlbmN5SWQiOiIwMTk1MTU2OC1kYzc0LWNmYTUtNjEzYS1iZDQ4OGRjMGU0OWEiLCJpYXQiOjE3Mzk4MjE2MDUsImV4cCI6MTczOTgyNTIwNX0.qLWP3LmiH5WDUaoKHZUiSRLJ3NOD0RCy1SWZDkCqvdk
}
