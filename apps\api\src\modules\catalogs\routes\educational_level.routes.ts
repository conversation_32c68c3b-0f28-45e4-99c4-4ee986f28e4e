import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/educational_level.controller';

const catEducationalLevelRouter = Router();

catEducationalLevelRouter.get('/', getAll);
catEducationalLevelRouter.get('/search', searchPaginated);
catEducationalLevelRouter.get('/:id', getOne);
catEducationalLevelRouter.post('/', create);
catEducationalLevelRouter.put('/:id', update);
catEducationalLevelRouter.delete('/:id', deleteOne);

export default catEducationalLevelRouter;
