import type { Request, Response } from 'express';
import db from '@/db';
import { and, eq, inArray, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';
import {
  systemModuleAssignmentFatigue,
  systemModuleAssignmentOperationQuadrant,
  systemModuleAssignmentUnit,
  systemModuleControlFatigue,
  systemModuleControlFatigueCategory,
  systemModuleControlFatiguePlace,
  systemModuleUser,
} from '@repo/shared-drizzle/schemas';
import { assignFatigueBodySchema, fatigueBodySchema, groupByFatigue, groupByUser } from '../helpers/fatigue.helper';
import moment from 'moment';
import {
  createOperationQuadrantAssignment,
  deleteOperationQuadrantAssignment,
} from '../helpers/operation_quadrant.helper';
import type { OperationQuadrantAssignment } from '../interfaces/operation_quadrant.interface';
import { processUnitAssignment } from '../helpers/unit.helper';
import type { UnitAssignment } from '../interfaces/unit.interface';
import { parseBooleanQueryParam } from '@/utils/default_values';
import type { ShiftAssignMetadata } from '@/modules/control/interfaces/fatigue.interface';
import { createBaseMetadata, createShiftAssignments } from '@/modules/control/helpers/fatigue.helper';
import { arraysAreEqual } from '@/utils/validate_fields';

export const getAll = async (req: Request, res: Response) => {
  const {
    deleted: deletedQS,
    disabled: disabledQS,
    assigned: assignedQS,
    grouped: groupedQS,
    groupBy: groupByQS,
  } = req.query;
  try {
    // Parsear parámetros de consulta
    const deleted = parseBooleanQueryParam(deletedQS, false);
    const disabled = parseBooleanQueryParam(disabledQS, false);
    const assigned = parseBooleanQueryParam(assignedQS, false);
    const grouped = parseBooleanQueryParam(groupedQS, false);
    const groupBy = typeof groupByQS === 'string' && ['fatigue', 'user'].includes(groupByQS) ? groupByQS : null;

    if (grouped && !groupBy) {
      throw new Error(
        'El parámetro groupedBy es obligatorio cuando se utiliza el parámetro grouped. Si lo esta enviando, el valor debe ser "fatigue" o "user"',
      );
    }
    // Construir consulta
    const filters: SQL[] = [
      !deleted && eq(systemModuleAssignmentFatigue.isDeleted, false),
      !disabled && eq(systemModuleAssignmentFatigue.isEnabled, true),
    ].filter(Boolean) as SQL[];

    const result = await db.transaction(async (trx) => {
      const baseResult = await trx
        .select({
          id: systemModuleAssignmentFatigue.id,
          fkUserId: systemModuleAssignmentFatigue.fkUserId,
          fkFatigueId: systemModuleAssignmentFatigue.fkFatigueId,
          catefories: systemModuleAssignmentFatigue.categories,
          places: systemModuleAssignmentFatigue.places,
          observations: systemModuleAssignmentFatigue.observations,
        })
        .from(systemModuleAssignmentFatigue)
        .where(and(...filters));
      // Manejar asignaciones, si es necesario
      if (assigned) {
        const assignedResult = await Promise.all(
          baseResult.map(async (row) => {
            const unit = await trx
              .select({
                id: systemModuleAssignmentUnit.id,
                fkUnitControlId: systemModuleAssignmentUnit.fkUnitControlId,
                isDriver: systemModuleAssignmentUnit.isDriver,
                isPassenger: systemModuleAssignmentUnit.isPassenger,
                isInCharge: systemModuleAssignmentUnit.isInCharge,
                comments: systemModuleAssignmentUnit.comments,
              })
              .from(systemModuleAssignmentUnit)
              .where(
                and(
                  eq(systemModuleAssignmentUnit.fkUserId, row.fkUserId),
                  eq(systemModuleAssignmentUnit.fkFatigueId, row.fkFatigueId),
                ),
              );
            const quadrants = await trx
              .select({
                id: systemModuleAssignmentOperationQuadrant.id,
                fkOperationQuadrantId: systemModuleAssignmentOperationQuadrant.fkOperationQuadrantId,
                comments: systemModuleAssignmentOperationQuadrant.comments,
              })
              .from(systemModuleAssignmentOperationQuadrant)
              .where(
                and(
                  eq(systemModuleAssignmentOperationQuadrant.fkUserId, row.fkUserId),
                  eq(systemModuleAssignmentOperationQuadrant.fkFatigueId, row.fkFatigueId),
                ),
              );
            const { places, ...rest } = row;
            return {
              ...rest,
              assignments: {
                places,
                unit: unit ? { id: unit[0].id, isDriver: unit[0].isDriver, isPassenger: unit[0].isPassenger } : null,
                quadrants,
              },
            };
          }),
        );
        // Manejar agrupamiento, si es necesario
        if (grouped && groupBy) {
          const groupedResult = groupBy === 'fatigue' ? groupByFatigue(assignedResult) : groupByUser(assignedResult);
          res.status(HttpStatus.OK).json(groupedResult);
          return;
        }
      }
      // Manejar agrupamiento, si es necesario
      if (grouped && groupBy) {
        const groupedResult = groupBy === 'fatigue' ? groupByFatigue(baseResult) : groupByUser(baseResult);
        res.status(HttpStatus.OK).json(groupedResult);
        return;
      }
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { assigned: assignedQS, grouped: groupedQS, groupBy: groupByQS } = req.query;
  try {
    const assigned = parseBooleanQueryParam(assignedQS, false);
    const grouped = parseBooleanQueryParam(groupedQS, false);
    const groupBy = typeof groupByQS === 'string' && ['fatigue', 'user'].includes(groupByQS) ? groupByQS : null;

    if (grouped && !groupBy) {
      throw new Error(
        'El parámetro groupedBy es obligatorio cuando se utiliza el parámetro grouped. Si lo esta enviando, el valor debe ser "fatigue" o "user"',
      );
    }
    // Construir consulta
    const result = await db.transaction(async (trx) => {
      const baseResult = await trx
        .select({
          id: systemModuleAssignmentFatigue.id,
          fkUserId: systemModuleAssignmentFatigue.fkUserId,
          fkFatigueId: systemModuleAssignmentFatigue.fkFatigueId,
          catefories: systemModuleAssignmentFatigue.categories,
          places: systemModuleAssignmentFatigue.places,
          observations: systemModuleAssignmentFatigue.observations,
        })
        .from(systemModuleAssignmentFatigue)
        .where(eq(systemModuleAssignmentFatigue.id, id));
      // Manejar asignaciones, si es necesario
      if (assigned) {
        const assignedResult = await Promise.all(
          baseResult.map(async (row) => {
            const unit = await trx
              .select({
                id: systemModuleAssignmentUnit.id,
                fkUnitControlId: systemModuleAssignmentUnit.fkUnitControlId,
                isDriver: systemModuleAssignmentUnit.isDriver,
                isPassenger: systemModuleAssignmentUnit.isPassenger,
                isInCharge: systemModuleAssignmentUnit.isInCharge,
                comments: systemModuleAssignmentUnit.comments,
              })
              .from(systemModuleAssignmentUnit)
              .where(
                and(
                  eq(systemModuleAssignmentUnit.fkUserId, row.fkUserId),
                  eq(systemModuleAssignmentUnit.fkFatigueId, row.fkFatigueId),
                  eq(systemModuleAssignmentUnit.isDeleted, false),
                ),
              );
            const quadrants = await trx
              .select({
                id: systemModuleAssignmentOperationQuadrant.id,
                fkOperationQuadrantId: systemModuleAssignmentOperationQuadrant.fkOperationQuadrantId,
                comments: systemModuleAssignmentOperationQuadrant.comments,
              })
              .from(systemModuleAssignmentOperationQuadrant)
              .where(
                and(
                  eq(systemModuleAssignmentOperationQuadrant.fkUserId, row.fkUserId),
                  eq(systemModuleAssignmentOperationQuadrant.fkFatigueId, row.fkFatigueId),
                  eq(systemModuleAssignmentOperationQuadrant.isDeleted, false),
                ),
              );
            const placesData = await trx
              .select({
                id: systemModuleControlFatiguePlace.id,
                name: systemModuleControlFatiguePlace.name,
                type: systemModuleControlFatiguePlace.type,
              })
              .from(systemModuleControlFatiguePlace)
              .where(
                and(
                  inArray(systemModuleControlFatiguePlace.id, row.places),
                  eq(systemModuleControlFatiguePlace.isDeleted, false),
                ),
              );
            const placesMap = placesData.map((place) => {
              return { id: place.id, name: place.name, type: place.type };
            });
            const { places, ...rest } = row;
            return {
              ...rest,
              assignments: {
                places: placesMap,
                unit: unit ? { id: unit[0].id, isDriver: unit[0].isDriver, isPassenger: unit[0].isPassenger } : null,
                quadrants,
              },
            };
          }),
        );
        // Manejar agrupamiento, si es necesario
        if (grouped && groupBy) {
          const groupedResult = groupBy === 'fatigue' ? groupByFatigue(assignedResult) : groupByUser(assignedResult);
          res.status(HttpStatus.OK).json(groupedResult);
          return;
        }
      }
      // Manejar agrupamiento, si es necesario
      if (grouped && groupBy) {
        const groupedResult = groupBy === 'fatigue' ? groupByFatigue(baseResult) : groupByUser(baseResult);
        res.status(HttpStatus.OK).json(groupedResult);
        return;
      }
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar payload
    const insertData = await fatigueBodySchema.parseAsync({ ...req.body });
    const result = await db.transaction(async (trx) => {
      // Validar la existencia de la fatiga/despliegue
      const fatigue = await trx
        .select()
        .from(systemModuleControlFatigue)
        .where(eq(systemModuleControlFatigue.id, insertData.fkFatigueId))
        .limit(1);
      if (fatigue.length === 0) {
        throw new Error('Fatiga/despliegue no encontrada');
      }

      // Validar la existencia del usuario/elemento asignado
      const user = await trx
        .select()
        .from(systemModuleUser)
        .where(eq(systemModuleUser.id, insertData.fkUserId))
        .limit(1);
      if (user.length === 0) {
        throw new Error('Usuario no encontrado');
      }

      // Validar que no exista una asignación con el mismo usuario y mismo despliegue
      const existingAssignment = await trx
        .select()
        .from(systemModuleAssignmentFatigue)
        .where(
          and(
            eq(systemModuleAssignmentFatigue.fkUserId, insertData.fkUserId),
            eq(systemModuleAssignmentFatigue.fkFatigueId, insertData.fkFatigueId),
          ),
        )
        .limit(1);
      if (existingAssignment.length > 0) {
        throw new Error('Ya existe una asignación del elemento en la fatiga/despliegue');
      }

      // Validar la existencia de las categorías asignadas
      const existingFatigueCategory = await trx
        .select({ id: systemModuleControlFatigueCategory.id })
        .from(systemModuleControlFatigueCategory)
        .where(inArray(systemModuleControlFatigueCategory.id, insertData.fkArrayCategoryIds));
      const existingFatigueCategoryIds = existingFatigueCategory.map((fatigueCategory) => fatigueCategory.id);
      const missingFatigueCategoryIds = insertData.fkArrayCategoryIds.filter(
        (id) => !existingFatigueCategoryIds.includes(id),
      );
      if (missingFatigueCategoryIds.length > 0) {
        throw new Error(`Las siguientes categorías de fatiga no existen: ${missingFatigueCategoryIds.join(', ')}`);
      }

      if ((insertData.fkArrayPlaceIds.length || 0) > 0) {
        // Validar la existencia de las lugares asignados
        const existingPlace = await trx
          .select({ id: systemModuleControlFatiguePlace.id })
          .from(systemModuleControlFatiguePlace)
          .where(inArray(systemModuleControlFatiguePlace.id, insertData.fkArrayPlaceIds));
        const existingPlaceIds = existingPlace.map((place) => place.id);
        const missingPlaceIds = insertData.fkArrayPlaceIds.filter((id) => !existingPlaceIds.includes(id));
        if (missingPlaceIds.length > 0) {
          throw new Error(`Las siguientes lugares de fatiga no existen: ${missingPlaceIds.join(', ')}`);
        }
      }

      const systemModuleCache = SystemModuleCache.getInstance();
      const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_ASIGNACIONES_FATIGAS);
      if (!systemModule) {
        throw new Error('System module CTRL_ASIGNACIONES_FATIGAS not found');
      }

      const metadata: ShiftAssignMetadata = createBaseMetadata();

      // Crear asignaciones de usuarios a la fatiga,
      // si ya existen fatigas se creara la nueva fatiga a partir de la plantilla
      const { message, submittedUsers, assignedUsers, unassignedUsers, usersByShift } = await createShiftAssignments(
        trx,
        insertData.fkFatigueId,
        fatigue[0].fkShiftId,
        [
          {
            fkUserId: insertData.fkUserId,
            fkFatigueId: insertData.fkFatigueId,
          },
        ],
        metadata,
        systemModuleCache,
        req.userId,
        req.dependencyId,
        false,
      );
      metadata.message = message;
      metadata.submittedUsers = submittedUsers;
      metadata.assignedUsers = assignedUsers;
      metadata.unassignedUsers = unassignedUsers;
      metadata.usersByShift = usersByShift;

      const assignData = await assignFatigueBodySchema.parseAsync({
        fkUserId: insertData.fkUserId,
        fkFatigueId: insertData.fkFatigueId,
        categories: insertData.fkArrayCategoryIds,
        places: insertData.fkArrayPlaceIds,
        fkLastUserId: insertData.fkLastUserId,
        fkLastDependencyId: insertData.fkLastDependencyId,
      });

      if (metadata.assignedUsers.length > 0) {
        // biome-ignore lint/suspicious/noExplicitAny: <explanation>
        const fatigueAssignment: Record<string, any> = {
          id: null,
          fkUserId: insertData.fkUserId,
          quadrantsAssigned: null,
          unitAssigned: null,
        };

        const newAssignment = await trx
          .insert(systemModuleAssignmentFatigue)
          .values({
            ...assignData,
            isEnabled: true,
            fkSystemModuleId: systemModule.id,
          })
          .returning({ id: systemModuleAssignmentFatigue.id });

        metadata.assignedUsers[0].fkFatigueAssignId = newAssignment[0].id;

        if ((insertData.fkArrayQuadrantsIds.length || 0) > 0) {
          const toCreateQuadrantsAssign: OperationQuadrantAssignment[] = insertData.fkArrayQuadrantsIds.map(
            (quadrant) => ({
              fkUserId: insertData.fkUserId,
              fkOperationQuadrantId: quadrant,
              fkFatigueId: newAssignment[0].id,
              assignmentDate: moment().format(),
              assignmentEndDate: null,
              comments: null,
              isEnabled: true,
              fkLastUserId: req.userId || '',
              fkLastDependencyId: req.dependencyId || '',
            }),
          );
          fatigueAssignment.quadrantsAssigned = await createOperationQuadrantAssignment(trx, toCreateQuadrantsAssign);
        }

        if (insertData.fkUnitObject) {
          const toCreateUnitAssign: UnitAssignment = {
            fkUserId: insertData.fkUserId,
            fkUnitControlId: insertData.fkUnitObject.fkUnitControlId,
            fkFatigueId: newAssignment[0].id,
            isDriver: insertData.fkUnitObject.isDriver,
            isPassenger: insertData.fkUnitObject.isPassenger,
            isInCharge: insertData.fkUnitObject.isInCharge,
            assignmentDate: moment().format(),
            assignmentEndDate: null,
            estimatedReturnDate: null,
            periodUse: null,
            comments: null,
            isEnabled: true,
            fkLastUserId: req.userId || '',
            fkLastDependencyId: req.dependencyId || '',
          };
          fatigueAssignment.unitAssigned = await processUnitAssignment(trx, toCreateUnitAssign, true, null);
        }
      } else {
        if (metadata.unassignedUsers.length > 0) {
          metadata.message =
            'Proceso de asignación ejecutado correctamente, pero no se pudieron asignar algunos elementos';
        }
      }

      return metadata;
    });

    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar payload
    const updateData = await fatigueBodySchema.parseAsync({ ...req.body });
    const result = await db.transaction(async (trx) => {
      // Obtener registro de asignación
      const assignment = await trx
        .select()
        .from(systemModuleAssignmentFatigue)
        .where(eq(systemModuleAssignmentFatigue.id, id))
        .limit(1);
      if (assignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar la existencia de la fatiga/despliegue
      const fatigue = await trx
        .select()
        .from(systemModuleControlFatigue)
        .where(eq(systemModuleControlFatigue.id, updateData.fkFatigueId))
        .limit(1);
      if (fatigue.length === 0) {
        throw new Error('Fatiga/despliegue no encontrada');
      }

      // Validar la existencia del usuario/elemento asignado
      const user = await trx
        .select()
        .from(systemModuleUser)
        .where(eq(systemModuleUser.id, updateData.fkUserId))
        .limit(1);
      if (user.length === 0) {
        throw new Error('Usuario no encontrado');
      }

      // Validar la existencia de las categorías asignadas
      const existingFatigueCategory = await trx
        .select({ id: systemModuleControlFatigueCategory.id })
        .from(systemModuleControlFatigueCategory)
        .where(inArray(systemModuleControlFatigueCategory.id, updateData.fkArrayCategoryIds));
      const existingFatigueCategoryIds = existingFatigueCategory.map((fatigueCategory) => fatigueCategory.id);
      const missingFatigueCategoryIds = updateData.fkArrayCategoryIds.filter(
        (id) => !existingFatigueCategoryIds.includes(id),
      );
      if (missingFatigueCategoryIds.length > 0) {
        throw new Error(`Las siguientes categorías de fatiga no existen: ${missingFatigueCategoryIds.join(', ')}`);
      }

      if ((updateData.fkArrayPlaceIds.length || 0) > 0) {
        // Validar la existencia de las lugares asignados
        const existingPlace = await trx
          .select({ id: systemModuleControlFatiguePlace.id })
          .from(systemModuleControlFatiguePlace)
          .where(inArray(systemModuleControlFatiguePlace.id, updateData.fkArrayPlaceIds));
        const existingPlaceIds = existingPlace.map((place) => place.id);
        const missingPlaceIds = updateData.fkArrayPlaceIds.filter((id) => !existingPlaceIds.includes(id));
        if (missingPlaceIds.length > 0) {
          throw new Error(`Las siguientes lugares de fatiga no existen: ${missingPlaceIds.join(', ')}`);
        }
      }

      // Validar si el usuario cambió
      if (updateData.fkUserId !== assignment[0].fkUserId) {
        // Validar que el nuevo usuario no esté asignado a otro despliegue en la misma fecha
        const existingAssignment = await trx
          .select()
          .from(systemModuleAssignmentFatigue)
          .where(
            and(
              eq(systemModuleAssignmentFatigue.fkUserId, updateData.fkUserId),
              eq(systemModuleAssignmentFatigue.fkFatigueId, updateData.fkFatigueId),
            ),
          );
        if (existingAssignment.length > 0) {
          throw new Error('Ya existe una asignación del elemento en la fatiga/despliegue');
        }
        // Si el usuario cambio, actualizar las asignaciones
        // Cuadrantes asignados
        await trx
          .update(systemModuleAssignmentOperationQuadrant)
          .set({ fkUserId: updateData.fkUserId })
          .where(
            and(
              eq(systemModuleAssignmentOperationQuadrant.fkFatigueId, updateData.fkFatigueId),
              eq(systemModuleAssignmentOperationQuadrant.fkUserId, assignment[0].fkUserId),
            ),
          );
        // Unidad vehicular asignada
        await trx
          .update(systemModuleAssignmentUnit)
          .set({ fkUserId: updateData.fkUserId })
          .where(
            and(
              eq(systemModuleAssignmentUnit.fkFatigueId, updateData.fkFatigueId),
              eq(systemModuleAssignmentUnit.fkUserId, assignment[0].fkUserId),
            ),
          );
      }

      const currentQuadrants = await trx
        .select({
          id: systemModuleAssignmentOperationQuadrant.id,
        })
        .from(systemModuleAssignmentOperationQuadrant)
        .where(
          and(
            eq(systemModuleAssignmentOperationQuadrant.fkFatigueId, updateData.fkFatigueId),
            eq(systemModuleAssignmentOperationQuadrant.fkUserId, updateData.fkUserId),
          ),
        );
      const quadrantsIds = currentQuadrants.map((quadrant) => quadrant.id);
      const toCreateQuadrantsAssign: OperationQuadrantAssignment[] = [];

      for (const quadrant of updateData.fkArrayQuadrantsIds) {
        if (!quadrantsIds.includes(quadrant)) {
          toCreateQuadrantsAssign.push({
            fkUserId: updateData.fkUserId,
            fkOperationQuadrantId: quadrant,
            fkFatigueId: updateData.fkFatigueId,
            assignmentDate: moment().format(),
            assignmentEndDate: null,
            comments: null,
            isEnabled: true,
            fkLastUserId: req.userId || '',
            fkLastDependencyId: req.dependencyId || '',
          });
        }
      }
      const idsToDelete = quadrantsIds.filter((id) => !updateData.fkArrayQuadrantsIds.includes(id));

      if (idsToDelete.length > 0) {
        await deleteOperationQuadrantAssignment(
          trx,
          idsToDelete,
          updateData.fkLastUserId,
          updateData.fkLastDependencyId,
        );
      }
      if (toCreateQuadrantsAssign.length > 0) {
        await createOperationQuadrantAssignment(trx, toCreateQuadrantsAssign);
      }

      if (updateData.fkUnitObject) {
        const unit = await trx
          .select()
          .from(systemModuleAssignmentUnit)
          .where(
            and(
              eq(systemModuleAssignmentUnit.fkFatigueId, updateData.fkFatigueId),
              eq(systemModuleAssignmentUnit.fkUserId, updateData.fkUserId),
            ),
          )
          .limit(1);
        if (unit.length === 0) {
          const toCreateUnitAssign: UnitAssignment = {
            fkUserId: updateData.fkUserId,
            fkUnitControlId: updateData.fkUnitObject.fkUnitControlId,
            fkFatigueId: updateData.fkFatigueId,
            isDriver: updateData.fkUnitObject.isDriver,
            isPassenger: updateData.fkUnitObject.isPassenger,
            isInCharge: updateData.fkUnitObject.isInCharge,
            assignmentDate: moment().format(),
            assignmentEndDate: null,
            estimatedReturnDate: null,
            periodUse: null,
            comments: null,
            isEnabled: true,
            fkLastUserId: req.userId || '',
            fkLastDependencyId: req.dependencyId || '',
          };
          await processUnitAssignment(trx, toCreateUnitAssign, true, null);
        } else {
          const toUpdateUnitAssign: UnitAssignment = {
            fkUserId: updateData.fkUserId,
            fkUnitControlId: updateData.fkUnitObject.fkUnitControlId,
            fkFatigueId: updateData.fkFatigueId,
            isDriver: updateData.fkUnitObject.isDriver,
            isPassenger: updateData.fkUnitObject.isPassenger,
            isInCharge: updateData.fkUnitObject.isInCharge,
            assignmentDate: unit[0].assignmentDate,
            assignmentEndDate: unit[0].assignmentEndDate,
            estimatedReturnDate: unit[0].estimatedReturnDate,
            periodUse: unit[0].periodUse,
            comments: unit[0].comments,
            fkLastUserId: updateData.fkLastUserId,
            fkLastDependencyId: updateData.fkLastDependencyId,
          };
          await processUnitAssignment(trx, toUpdateUnitAssign, false, unit[0].id);
        }
      }

      const assignData = await assignFatigueBodySchema.parseAsync({
        fkUserId: updateData.fkUserId,
        fkFatigueId: updateData.fkFatigueId,
        categories: assignment[0].categories,
        places: assignment[0].places,
        fkLastUserId: updateData.fkLastUserId,
        fkLastDependencyId: updateData.fkLastDependencyId,
      });

      if (!arraysAreEqual(assignment[0].categories, updateData.fkArrayCategoryIds)) {
        assignData.categories = updateData.fkArrayCategoryIds;
      }

      if (!arraysAreEqual(assignment[0].places, updateData.fkArrayPlaceIds)) {
        assignData.places = updateData.fkArrayPlaceIds;
      }

      // Actualizar la asignación
      const assignUpdate = await trx
        .update(systemModuleAssignmentFatigue)
        .set(assignData)
        .where(eq(systemModuleAssignmentFatigue.id, id));

      return assignUpdate;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
/*
export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (tx) => {
      // Validar que exista la asignación
      const existingAssignment = await tx
        .select()
        .from(systemModuleAssignmentCommission)
        .where(eq(systemModuleAssignmentCommission.id, id))
        .limit(1);
      if (existingAssignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar que la asignación no este en un despliegue activo
      const activeCommissionAssignment = await tx
        .select()
        .from(systemModuleAssignmentCommission)
        .leftJoin(
          systemModuleControlFatigue,
          eq(systemModuleAssignmentCommission.fkFatigueId, systemModuleControlFatigue.id),
        )
        .where(
          and(
            eq(systemModuleControlFatigue.status, 'active'),
            eq(systemModuleAssignmentCommission.fkFatigueId, existingAssignment[0].fkFatigueId),
            eq(systemModuleAssignmentCommission.id, id),
          ),
        )
        .limit(1);
      if (activeCommissionAssignment.length > 0) {
        throw new Error('No es posible eliminar la asignación si este ya se encuentra en un despliegue activo');
      }

      const deleteResult = await tx
        .update(systemModuleAssignmentCommission)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleAssignmentCommission.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    q: qQS,
    grouped: groupedQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;
  let grouped = false;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentCommission.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentCommission.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(sql`${systemModuleAssignmentCommission.startedAt}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentCommission.endedAt}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleAssignmentCommission.comments, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleAssignmentCommission.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentCommission.id,
        fkUserId: systemModuleAssignmentCommission.fkUserId,
        fkCommissionId: systemModuleAssignmentCommission.fkCommissionId,
        fkFatigueId: systemModuleAssignmentCommission.fkFatigueId,
        isApproved: systemModuleAssignmentCommission.isApproved,
        startedAt: systemModuleAssignmentCommission.startedAt,
        endedAt: systemModuleAssignmentCommission.endedAt,
        comments: systemModuleAssignmentCommission.comments,
      })
      .from(systemModuleAssignmentCommission)
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleAssignmentCommission.id));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkCommissionId: rest.fkCommissionId,
            fkFatigueId: rest.fkFatigueId,
            isApproved: rest.isApproved,
            startedAt: rest.startedAt,
            endedAt: rest.endedAt || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as CommissionAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
*/
