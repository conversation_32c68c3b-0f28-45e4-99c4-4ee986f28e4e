// import { Router } from 'express';
// //import {
// //  getAll,
// //  getOne,
// //  create,
// //  update,
// //  deleteOne,
// //  searchPaginated,
// //} from '@/modules/catalogs/controllers/iph_case_type.controller';

// const catIphCaseTypeRouter = Router();

// //catIphCaseTypeRouter.get('/', getAll);
// //catIphCaseTypeRouter.get('/search', searchPaginated);
// //catIphCaseTypeRouter.get('/:id', getOne);
// //catIphCaseTypeRouter.post('/', create);
// //catIphCaseTypeRouter.put('/:id', update);
// //catIphCaseTypeRouter.delete('/:id', deleteOne);

// export default catIphCaseTypeRouter;
