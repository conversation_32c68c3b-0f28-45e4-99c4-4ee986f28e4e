import type { Request, Response } from 'express';
import db from '@/db';
import { z } from 'zod';
import { and, asc, eq, gt, like, ne, or, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { ModuleName } from '@/utils/module_names';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import type { IPermission, IPermissionUpdate } from '../interfaces/permission.interface';
import { DefaultValues } from '@/utils/default_values';
import { permissionBodySchema } from '../helpers/permission.helper';
import SystemModuleCache from '@/utils/system_module_cache';
import { systemModule, systemModulePermission, systemModuleProfile } from '@repo/shared-drizzle/schemas';

type PermissionGrouped = Record<
  string,
  {
    profileId: string;
    profileName: string;
    permissions: {
      permissionId: string;
      permissionName: string;
      description: string;
      modulePermissionId: string;
      modulePermissionName: string;
      canRead: boolean;
      canCreate: boolean;
      canUpdate: boolean;
      canDelete: boolean;
    }[];
  }
>;

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, grouped: groupedQS } = req.query;
  let deleted = false;
  let disabled = false;
  let grouped = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModulePermission.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModulePermission.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModulePermission.id,
        name: systemModulePermission.name,
        description: systemModulePermission.description,
        canRead: systemModulePermission.canRead,
        canCreate: systemModulePermission.canCreate,
        canUpdate: systemModulePermission.canUpdate,
        canDelete: systemModulePermission.canDelete,
        fkProfileId: systemModulePermission.fkProfileId,
        profileName: systemModuleProfile.name,
        fkModulePermissionId: systemModulePermission.fkModulePermissionId,
        modulePermissionName: systemModule.name,
      })
      .from(systemModulePermission)
      .leftJoin(systemModuleProfile, eq(systemModulePermission.fkProfileId, systemModuleProfile.id))
      .leftJoin(systemModule, eq(systemModulePermission.fkModulePermissionId, systemModule.id))
      .where(and(...filters));
    if (grouped) {
      // Agrupar los resultados por perfil
      const groupedProfiles = result.reduce((acc, row) => {
        const {
          id,
          name,
          description,
          canRead,
          canCreate,
          canUpdate,
          canDelete,
          fkProfileId,
          profileName,
          fkModulePermissionId,
          modulePermissionName,
        } = row;
        if (!acc[fkProfileId]) {
          acc[fkProfileId] = {
            profileId: fkProfileId,
            profileName: profileName || DefaultValues.DEFAULT_CONTENT,
            permissions: [],
          };
        }
        if (fkModulePermissionId) {
          acc[fkProfileId].permissions.push({
            permissionId: id,
            permissionName: name,
            description: description || DefaultValues.DEFAULT_CONTENT,
            modulePermissionId: fkModulePermissionId,
            modulePermissionName: modulePermissionName || DefaultValues.DEFAULT_CONTENT,
            canRead,
            canCreate,
            canUpdate,
            canDelete,
          });
        }
        return acc;
      }, {} as PermissionGrouped);
      const resultGrouped = Object.values(groupedProfiles);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.PERMISSION}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModulePermission.id,
        name: systemModulePermission.name,
        description: systemModulePermission.description,
        canRead: systemModulePermission.canRead,
        canCreate: systemModulePermission.canCreate,
        canUpdate: systemModulePermission.canUpdate,
        canDelete: systemModulePermission.canDelete,
        fkProfileId: systemModulePermission.fkProfileId,
        profileName: systemModuleProfile.name,
        fkModulePermissionId: systemModulePermission.fkModulePermissionId,
        modulePermissionName: systemModule.name,
      })
      .from(systemModulePermission)
      .leftJoin(systemModuleProfile, eq(systemModulePermission.fkProfileId, systemModuleProfile.id))
      .leftJoin(systemModule, eq(systemModulePermission.fkModulePermissionId, systemModule.id))
      .where(eq(systemModulePermission.id, id))
      .limit(1);
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.PERMISSION}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  const { arrPermission }: { arrPermission: IPermission[] } = req.body;
  try {
    const uniqueSet = new Set<string>();
    const duplicates: IPermission[] = [];
    // Verificar que no existan permisos duplicados
    for (const p of arrPermission) {
      const key = `${p.canRead}-${p.canCreate}-${p.canUpdate}-${p.canDelete}-${p.fkProfileId}-${p.fkModulePermissionId}`;
      if (uniqueSet.has(key)) {
        duplicates.push(p);
      } else {
        uniqueSet.add(key);
      }
    }
    if (duplicates.length > 0) {
      res.status(HttpStatus.NOT_ACCEPTABLE).json({ message: 'Duplicate permissions', result: duplicates });
      return;
    }
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_PERMISSIONS);
    if (!systemModule) {
      throw new Error('System module CTRL_PERMISSIONS not found');
    }
    // Obtener el ID del módulo padre del registro
    const systemModuleId = systemModule.id;
    const filters: SQL[] = [];
    const insertValues: IPermission[] = [];
    for (const p of arrPermission) {
      // Almacenar en una lista la relación permisos(booleanos)-perfil-módulo para posteriormente verificar si ya está asignado
      const filter = and(
        eq(systemModulePermission.canRead, p.canRead),
        eq(systemModulePermission.canCreate, p.canCreate),
        eq(systemModulePermission.canUpdate, p.canUpdate),
        eq(systemModulePermission.canDelete, p.canDelete),
        eq(systemModulePermission.fkProfileId, p.fkProfileId),
        eq(systemModulePermission.fkModulePermissionId, p.fkModulePermissionId),
      );
      if (filter !== undefined) {
        filters.push(filter);
      }
      if (req.userId !== undefined && req.dependencyId !== undefined) {
        // Listar los valores que se van a insertar en la tabla
        insertValues.push({
          ...p,
          fkLastUserId: req.userId,
          fkLastDependencyId: req.dependencyId,
        });
      }
    }
    // Verificar que los permisos no estén en uso
    const sqAssignment = db.$with('sq').as(
      db
        .select()
        .from(systemModulePermission)
        .where(or(...filters)),
    );
    const assignmentExist = await db.with(sqAssignment).select().from(sqAssignment);
    if (assignmentExist.length > 0) {
      // Obtener permisos que ya están asignados
      const assigned = arrPermission.filter((item) =>
        assignmentExist.some(
          (assign) =>
            assign.canRead === item.canRead &&
            assign.canCreate === item.canCreate &&
            assign.canUpdate === item.canUpdate &&
            assign.canDelete === item.canDelete &&
            assign.fkProfileId === item.fkProfileId &&
            assign.fkModulePermissionId === item.fkModulePermissionId,
        ),
      );
      res.status(HttpStatus.NOT_ACCEPTABLE).json({ message: 'Permissions already assigned', result: assigned });
      return;
    }
    // Añadir el id del sistema padre al insert
    const values = insertValues.map((item) => {
      return { ...item, fkSystemModuleId: systemModuleId };
    });
    // Insertar los datos en la tabla
    const result = await db.insert(systemModulePermission).values(values);
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.PERMISSION} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const updateData = await permissionBodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const sqAssignment = db.$with('sq').as(
      db
        .select()
        .from(systemModulePermission)
        .where(
          and(
            ne(systemModulePermission.id, id),
            eq(systemModulePermission.canRead, updateData.canRead),
            eq(systemModulePermission.canCreate, updateData.canCreate),
            eq(systemModulePermission.canUpdate, updateData.canUpdate),
            eq(systemModulePermission.canDelete, updateData.canDelete),
            eq(systemModulePermission.fkProfileId, updateData.fkProfileId),
            eq(systemModulePermission.fkModulePermissionId, updateData.fkModulePermissionId),
          ),
        ),
    );
    const assignmentExist = await db.with(sqAssignment).select().from(sqAssignment);
    if (assignmentExist.length > 0) {
      throw new Error('Permission already assigned');
    }
    const result = await db
      .update(systemModulePermission)
      .set({ ...updateData })
      .where(eq(systemModulePermission.id, id));
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.PERMISSION}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const multiUpdate = async (req: Request, res: Response) => {
  const { arrPermission }: { arrPermission: IPermissionUpdate[] } = req.body;
  try {
    const uniqueSet = new Set<string>();
    const duplicates: IPermissionUpdate[] = [];
    // Verificar que no existan permisos duplicados
    for (const p of arrPermission) {
      const key = `${p.canRead}-${p.canCreate}-${p.canUpdate}-${p.canDelete}-${p.fkProfileId}-${p.fkModulePermissionId}`;
      if (uniqueSet.has(key)) {
        duplicates.push(p);
      } else {
        uniqueSet.add(key);
      }
    }
    if (duplicates.length > 0) {
      res.status(HttpStatus.NOT_ACCEPTABLE).json({ message: 'Duplicate permissions', result: duplicates });
      return;
    }
    const filters: SQL[] = [];
    const updateValues: IPermissionUpdate[] = [];
    for (const p of arrPermission) {
      // Almacenar en una lista la relación permisos(booleanos)-perfil-módulo para posteriormente verificar si ya está asignado
      const filter = and(
        ne(systemModulePermission.id, p.id),
        eq(systemModulePermission.canRead, p.canRead),
        eq(systemModulePermission.canCreate, p.canCreate),
        eq(systemModulePermission.canUpdate, p.canUpdate),
        eq(systemModulePermission.canDelete, p.canDelete),
        eq(systemModulePermission.fkProfileId, p.fkProfileId),
        eq(systemModulePermission.fkModulePermissionId, p.fkModulePermissionId),
      );
      if (filter !== undefined) {
        filters.push(filter);
      }
      if (req.userId !== undefined && req.dependencyId !== undefined) {
        // Listar los valores que se van a insertar en la tabla
        updateValues.push({
          ...p,
          fkLastUserId: req.userId,
          fkLastDependencyId: req.dependencyId,
        });
      }
    }
    // Verificar que los permisos no estén en uso
    const sqAssignment = db.$with('sq').as(
      db
        .select()
        .from(systemModulePermission)
        .where(or(...filters)),
    );
    const assignmentExist = await db.with(sqAssignment).select().from(sqAssignment);
    if (assignmentExist.length > 0) {
      // Obtener permisos que ya están asignados
      const assigned = arrPermission.filter((item) =>
        assignmentExist.some(
          (assign) =>
            assign.id !== item.id &&
            assign.canRead === item.canRead &&
            assign.canCreate === item.canCreate &&
            assign.canUpdate === item.canUpdate &&
            assign.canDelete === item.canDelete &&
            assign.fkProfileId === item.fkProfileId &&
            assign.fkModulePermissionId === item.fkModulePermissionId,
        ),
      );
      res.status(HttpStatus.NOT_ACCEPTABLE).json({ message: 'Permissions already assigned', result: assigned });
      return;
    }
    // Actualizar los permisos
    const result = await db.transaction(async (trx) => {
      for (const record of updateValues) {
        const { id, ...updateData } = record;
        await trx.update(systemModulePermission).set(updateData).where(eq(systemModulePermission.id, id));
      }
    });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.PERMISSION} - multiupdate`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .update(systemModulePermission)
      .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
      .where(eq(systemModulePermission.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.PROFILE}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    q: qQS,
    grouped: groupedQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let grouped = false;
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModulePermission.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModulePermission.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModulePermission.name, `%${q}%`));
      likeFilters.push(like(systemModulePermission.description, `%${q}%`));
      likeFilters.push(like(systemModuleProfile.name, `%${q}%`));
      likeFilters.push(like(systemModule.name, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModulePermission.id, lastId));
    }
    const result = await db
      .select({
        id: systemModulePermission.id,
        name: systemModulePermission.name,
        description: systemModulePermission.description,
        canRead: systemModulePermission.canRead,
        canCreate: systemModulePermission.canCreate,
        canUpdate: systemModulePermission.canUpdate,
        canDelete: systemModulePermission.canDelete,
        fkProfileId: systemModulePermission.fkProfileId,
        profileName: systemModuleProfile.name,
        fkModulePermissionId: systemModulePermission.fkModulePermissionId,
        modulePermissionName: systemModule.name,
      })
      .from(systemModulePermission)
      .leftJoin(systemModuleProfile, eq(systemModulePermission.fkProfileId, systemModuleProfile.id))
      .leftJoin(systemModule, eq(systemModulePermission.fkModulePermissionId, systemModule.id))
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModulePermission.id));
    if (grouped) {
      // Agrupar los resultados por perfil
      const groupedProfiles = result.reduce((acc, row) => {
        const {
          id,
          name,
          description,
          canRead,
          canCreate,
          canUpdate,
          canDelete,
          fkProfileId,
          profileName,
          fkModulePermissionId,
          modulePermissionName,
        } = row;
        if (!acc[fkProfileId]) {
          acc[fkProfileId] = {
            profileId: fkProfileId,
            profileName: profileName || DefaultValues.DEFAULT_CONTENT,
            permissions: [],
          };
        }
        if (fkModulePermissionId) {
          acc[fkProfileId].permissions.push({
            permissionId: id,
            permissionName: name,
            description: description || DefaultValues.DEFAULT_CONTENT,
            modulePermissionId: fkModulePermissionId,
            modulePermissionName: modulePermissionName || DefaultValues.DEFAULT_CONTENT,
            canRead,
            canCreate,
            canUpdate,
            canDelete,
          });
        }
        return acc;
      }, {} as PermissionGrouped);
      const resultGrouped = Object.values(groupedProfiles);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.PERMISSION} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
