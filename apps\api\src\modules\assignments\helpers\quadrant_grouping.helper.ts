import { systemModuleAssignmentQuadrantGrouping, systemModuleControlFatigue } from '@repo/shared-drizzle/schemas';
import { and, eq, type ExtractTablesWithRelations, ne } from 'drizzle-orm';
import type { NodePgQueryResultHKT } from 'drizzle-orm/node-postgres';
import type { PgTransaction } from 'drizzle-orm/pg-core';
import { z } from 'zod';

export const quadrantGroupingAssignmentBodySchema = z.object({
  fkUserId: z.string(),
  fkQuadrantGroupingId: z.string(),
  fkFatigueId: z.string(),
  assignmentDate: z.string(),
  assignmentEndDate: z.string().nullable().optional(),
  comments: z.string().nullable().optional(),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});

export const validateQuadrantGroupingAssignmentInFatigue = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkQuadrantGroupingId: string,
  fkFatigueId: string,
  fkQuadrantGroupingAssignmentId: string,
) => {
  const existingAssignment = await trx
    .select()
    .from(systemModuleAssignmentQuadrantGrouping)
    .where(
      and(
        eq(systemModuleAssignmentQuadrantGrouping.fkQuadrantGroupingId, fkQuadrantGroupingId),
        eq(systemModuleAssignmentQuadrantGrouping.fkFatigueId, fkFatigueId),
        ne(systemModuleAssignmentQuadrantGrouping.id, fkQuadrantGroupingAssignmentId),
      ),
    )
    .limit(1);
  if (existingAssignment.length > 0) {
    throw new Error('Ya existe una asignación con la misma agrupación de cuadrantes en el despliegue indicado');
  }
};

export const validateQuadrantGroupingActiveAssignment = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkQuadrantGroupingId: string,
  fkQuadrantGroupingAssignmentId: string,
) => {
  const activeQuadrantGroupingAssignment = await trx
    .select()
    .from(systemModuleAssignmentQuadrantGrouping)
    .leftJoin(
      systemModuleControlFatigue,
      eq(systemModuleAssignmentQuadrantGrouping.fkFatigueId, systemModuleControlFatigue.id),
    )
    .where(
      and(
        eq(systemModuleControlFatigue.status, 'active'),
        eq(systemModuleAssignmentQuadrantGrouping.fkQuadrantGroupingId, fkQuadrantGroupingId),
        eq(systemModuleAssignmentQuadrantGrouping.isDeleted, false),
        ne(systemModuleAssignmentQuadrantGrouping.id, fkQuadrantGroupingAssignmentId),
      ),
    )
    .limit(1);
  if (activeQuadrantGroupingAssignment.length > 0) {
    throw new Error('El agrupador de cuadrantes esta asignada a otro elemento en un despliegue activo');
  }
};

export const handleUserChangeInQuadrantGroupingAssign = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkUserId: string,
  fkQuadrantGroupingId: string,
  fkFatigueId: string,
  fkQuadrantGroupingAssignmentId: string,
) => {
  const existingAssignment = await trx
    .select()
    .from(systemModuleAssignmentQuadrantGrouping)
    .where(
      and(
        eq(systemModuleAssignmentQuadrantGrouping.fkUserId, fkUserId),
        eq(systemModuleAssignmentQuadrantGrouping.fkQuadrantGroupingId, fkQuadrantGroupingId),
        eq(systemModuleAssignmentQuadrantGrouping.fkFatigueId, fkFatigueId),
        ne(systemModuleAssignmentQuadrantGrouping.id, fkQuadrantGroupingAssignmentId), // Excluir la asignación actual
      ),
    )
    .limit(1);

  if (existingAssignment.length > 0) {
    throw new Error('El usuario ya tiene una asignación con este agrupador de cuadrantes en el mismo despliegue');
  }
};
