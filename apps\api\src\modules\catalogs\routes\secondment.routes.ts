import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/secondment.controller';

const catSecondmentRouter = Router();

catSecondmentRouter.get('/', getAll);
catSecondmentRouter.get('/search', searchPaginated);
catSecondmentRouter.get('/:id', getOne);
catSecondmentRouter.post('/', create);
catSecondmentRouter.put('/:id', update);
catSecondmentRouter.delete('/:id', deleteOne);

export default catSecondmentRouter;
