import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { getLocale } from '@/utils/locale_validator';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import { systemModuleControlOperationQuadrant } from '@repo/shared-drizzle/schemas';
import { operationalQuadrantBodySchema } from '../helpers/operational_quadrant.helper';
import SystemModuleCache from '@/utils/system_module_cache';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS } = req.query;
  let [deleted, disabled] = [false, false];

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlOperationQuadrant.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlOperationQuadrant.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlOperationQuadrant.id,
        quadrantName: systemModuleControlOperationQuadrant.quadrantName,
        isOperationalStatus: systemModuleControlOperationQuadrant.isOperationalStatus,
        operationalArea: systemModuleControlOperationQuadrant.operationalArea,
        geometry: systemModuleControlOperationQuadrant.geometry,
        displayColor: systemModuleControlOperationQuadrant.displayColor,
        displayStyle: systemModuleControlOperationQuadrant.displayStyle,
      })
      .from(systemModuleControlOperationQuadrant)
      .where(and(...filters));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.OPERATIONAL_QUADRANT}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleControlOperationQuadrant.id,
        quadrantName: systemModuleControlOperationQuadrant.quadrantName,
        isOperationalStatus: systemModuleControlOperationQuadrant.isOperationalStatus,
        operationalArea: systemModuleControlOperationQuadrant.operationalArea,
        geometry: systemModuleControlOperationQuadrant.geometry,
        displayColor: systemModuleControlOperationQuadrant.displayColor,
        displayStyle: systemModuleControlOperationQuadrant.displayStyle,
      })
      .from(systemModuleControlOperationQuadrant)
      .where(eq(systemModuleControlOperationQuadrant.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.OPERATIONAL_QUADRANT}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const insertData = operationalQuadrantBodySchema.parse({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_CUADRANTES_OPERACIONALES);
    if (!systemModule) {
      throw new Error('System module CTRL_CUADRANTES_OPERACIONALES not found');
    }
    const result = await db.insert(systemModuleControlOperationQuadrant).values({
      ...insertData,
      fkSystemModuleId: systemModule.id,
    });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.OPERATIONAL_QUADRANT} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;
    // Validar payload
    const updateData = operationalQuadrantBodySchema.parse({ ...req.body });
    const result = await db
      .update(systemModuleControlOperationQuadrant)
      .set({ ...updateData })
      .where(eq(systemModuleControlOperationQuadrant.id, id));
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.OPERATIONAL_QUADRANT}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .update(systemModuleControlOperationQuadrant)
      .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
      .where(eq(systemModuleControlOperationQuadrant.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.OPERATIONAL_QUADRANT}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    locale: localeQS,
    q: qQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlOperationQuadrant.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlOperationQuadrant.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleControlOperationQuadrant.quadrantName, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlOperationQuadrant.operationalArea}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleControlOperationQuadrant.displayColor, `%${q}%`));
      likeFilters.push(like(systemModuleControlOperationQuadrant.displayStyle, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleControlOperationQuadrant.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlOperationQuadrant.id,
        quadrantName: systemModuleControlOperationQuadrant.quadrantName,
        isOperationalStatus: systemModuleControlOperationQuadrant.isOperationalStatus,
        operationalArea: systemModuleControlOperationQuadrant.operationalArea,
        geometry: systemModuleControlOperationQuadrant.geometry,
        displayColor: systemModuleControlOperationQuadrant.displayColor,
        displayStyle: systemModuleControlOperationQuadrant.displayStyle,
      })
      .from(systemModuleControlOperationQuadrant)
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlOperationQuadrant.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.OPERATIONAL_QUADRANT} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
