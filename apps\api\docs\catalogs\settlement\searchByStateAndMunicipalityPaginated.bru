meta {
  name: searchByStateAndMunicipalityPaginated
  type: http
  seq: 3
}

get {
  url: {{base}}/catalogs/settlement/:state/:municipality/search?lastId&limit=10&deleted=false&disabled=false&locale=es&q=fat
  body: none
  auth: none
}

params:query {
  lastId: 
  limit: 10
  deleted: false
  disabled: false
  locale: es
  q: fat
}

params:path {
  municipality: 0195839f-3a5c-ba6e-603f-d52402c78087
  state: 0195823a-18f4-b753-2ef9-ae64f38bbd31
}
