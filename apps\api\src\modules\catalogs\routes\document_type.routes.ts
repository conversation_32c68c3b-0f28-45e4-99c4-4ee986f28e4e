import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/document_type.controller';

const catDocumentTypeRouter = Router();

catDocumentTypeRouter.get('/', getAll);
catDocumentTypeRouter.get('/search', searchPaginated);
catDocumentTypeRouter.get('/:id', getOne);
catDocumentTypeRouter.post('/', create);
catDocumentTypeRouter.put('/:id', update);
catDocumentTypeRouter.delete('/:id', deleteOne);

export default catDocumentTypeRouter;
