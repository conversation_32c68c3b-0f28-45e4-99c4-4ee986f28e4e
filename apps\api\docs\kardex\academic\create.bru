meta {
  name: create
  type: http
  seq: 5
}

post {
  url: {{base}}/kardex/academic?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

body:json {
  {
    "fkUserId": "ID Usuario",
    "fkHighestEducationLevelId": "ID Nivel educativo",
    "fkInstitutionId": "ID Institución Externa",
    "careerName": "Nombre Carrera",
    "careerStartDate": "Fecha inicio",
    "graduationDate": "Fecha fin", // Opcional
    "isGraduated": true, 
    "academicFile": {
      "mimeType": "Tipo de contenido",
      "nameFile": "Nombre archivo",
      "value": "Archivo en Base64",
      "fileType": "Tipo de archivo/Extensión"
    }
  }
}
