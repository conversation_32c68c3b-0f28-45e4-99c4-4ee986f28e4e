import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/activity_type.controller';

const catActivityTypeRouter = Router();

catActivityTypeRouter.get('/', getAll);
catActivityTypeRouter.get('/search', searchPaginated);
catActivityTypeRouter.get('/:id', getOne);
catActivityTypeRouter.post('/', create);
catActivityTypeRouter.put('/:id', update);
catActivityTypeRouter.delete('/:id', deleteOne);

export default catActivityTypeRouter;
