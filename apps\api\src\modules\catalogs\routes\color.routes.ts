import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/color.controller';

const catColorRouter = Router();

catColorRouter.get('/', getAll);
catColorRouter.get('/search', searchPaginated);
catColorRouter.get('/:id', getOne);
catColorRouter.post('/', create);
catColorRouter.put('/:id', update);
catColorRouter.delete('/:id', deleteOne);

export default catColorRouter;
