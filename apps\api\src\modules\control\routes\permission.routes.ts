import { Router } from 'express';
import {
  create,
  deleteOne,
  getAll,
  getOne,
  multiUpdate,
  searchPaginated,
  update,
} from '../controllers/permission.controller';

const permissionRouter = Router();

permissionRouter.get('/', getAll);
permissionRouter.get('/search', searchPaginated);
permissionRouter.get('/:id', getOne);
permissionRouter.post('/', create);
permissionRouter.post('/multiupdate', multiUpdate);
permissionRouter.put('/:id', update);
permissionRouter.delete('/:id', deleteOne);

export default permissionRouter;
