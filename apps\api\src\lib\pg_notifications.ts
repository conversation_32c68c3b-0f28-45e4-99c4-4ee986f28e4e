import { drizzle } from 'drizzle-orm/node-postgres';
import { eq } from 'drizzle-orm';
import type { PgTable } from 'drizzle-orm/pg-core';
import db from '@/db';
import { systemModuleRootApiIntegration } from '@repo/shared-drizzle/schemas';

export const sync = async (payload: string) => {
  // create connection to remote database
  const json = JSON.parse(payload);
  const dependencyId = json.fkDependencyId;

  // get data from remote database
  const result = await db
    .select()
    .from(systemModuleRootApiIntegration)
    .where(eq(systemModuleRootApiIntegration.fkLastDependencyId, dependencyId));

  const connectionString = `postgres://${process.env.POSTGRES_USER}:${process.env.POSTGRES_PASSWORD}@${process.env.POSTGRES_HOST}:${process.env.POSTGRES_PORT}/${process.env.POSTGRES_DB}`;
  const remoteDB = drizzle(connectionString);
  // export all tables in a map
  const tablesMap = new Map<string, PgTable>();
  tablesMap.set('system_module_root_api_integration', systemModuleRootApiIntegration);
  const table = tablesMap.get(json.table);
  if (!table) {
    // error
    return;
  }

  if (json.operation === 'INSERT') {
    remoteDB.insert(table).values(result);
  }
  if (json.operation === 'UPDATE') {
    remoteDB.update(table).set(result);
  }
  if (json.operation === 'DELETE') {
    remoteDB.update(table).set({ isDeleted: true, deletedAt: new Date() });
  }

  // TODO: Agregar una columna a las tablas para indicar si esta siendo insertado o actualizado o eliminado por una sincronización remota
};

export const subscribe = async () => {
  db.$client.connect().then((client) => {
    if (!client) return;
    client.on('notification', ({ payload }) => {
      if (!payload) return;
      sync(payload).catch((error) => {
        console.error(error);
      });
    });
  });
};
