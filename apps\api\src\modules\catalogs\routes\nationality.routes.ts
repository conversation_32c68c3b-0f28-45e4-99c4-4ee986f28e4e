import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/nationality.controller';

const catNationalityRouter = Router();

catNationalityRouter.get('/', getAll);
catNationalityRouter.get('/search', searchPaginated);
catNationalityRouter.get('/:id', getOne);
catNationalityRouter.post('/', create);
catNationalityRouter.put('/:id', update);
catNationalityRouter.delete('/:id', deleteOne);

export default catNationalityRouter;
