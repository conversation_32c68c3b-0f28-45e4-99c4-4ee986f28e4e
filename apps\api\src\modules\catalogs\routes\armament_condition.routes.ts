import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/armament_condition.controller';

const catArmamentConditionRouter = Router();

catArmamentConditionRouter.get('/', getAll);
catArmamentConditionRouter.get('/search', searchPaginated);
catArmamentConditionRouter.get('/:id', getOne);
catArmamentConditionRouter.post('/', create);
catArmamentConditionRouter.put('/:id', update);
catArmamentConditionRouter.delete('/:id', deleteOne);

export default catArmamentConditionRouter;
