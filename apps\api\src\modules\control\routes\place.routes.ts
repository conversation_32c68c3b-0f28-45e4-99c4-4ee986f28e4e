import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/control/controllers/place.controller';

const placeRouter = Router();

placeRouter.get('/', getAll);
placeRouter.get('/search', searchPaginated);
placeRouter.get('/:id', getOne);
placeRouter.post('/', create);
placeRouter.put('/:id', update);
placeRouter.delete('/:id', deleteOne);

export default placeRouter;
