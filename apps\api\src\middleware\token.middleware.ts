import db from '@/db';
import {
  catInstitution,
  systemModuleControlCitizen,
  systemModulePivotUserProfile,
  systemModuleProfile,
  systemModuleRootUser,
  systemModuleUser,
  systemModuleUserKardexEmployment,
} from '@repo/shared-drizzle/schemas';
import { and, eq } from 'drizzle-orm';
import { SignJWT } from 'jose';

// Clave secreta para firmar el token
const key = new TextEncoder().encode(process.env.JWT_SECRET);

/**
 * Función para generar un token JWT
 * @param {string} userId - Identificador del usuario responsable.
 * @param {string} dependencyId - Identificador de la dependencia responsable.
 * @returns Token JWT firmado.
 */
const generateAuthToken = async (userId: string, dependencyId: string): Promise<string> => {
  // Verificar si es usuario del sistema
  const user = await db
    .select()
    .from(systemModuleUser)
    .leftJoin(systemModuleUserKardexEmployment, eq(systemModuleUser.id, systemModuleUserKardexEmployment.fkUserId))
    .where(and(eq(systemModuleUser.id, userId), eq(systemModuleUserKardexEmployment.fkInstitutionId, dependencyId)))
    .limit(1);

  // Verificar si el usuario existe
  if (user.length > 0) {
    let role: string | null = null;

    const userProfiles = await db
      .select({ id: systemModuleProfile.id, name: systemModuleProfile.name })
      .from(systemModulePivotUserProfile)
      .leftJoin(systemModuleProfile, eq(systemModulePivotUserProfile.fkProfileId, systemModuleProfile.id))
      .where(eq(systemModulePivotUserProfile.fkUserId, userId));

    if (userProfiles.length > 0) {
      role = userProfiles.some((profile) => profile.name === 'monitor')
        ? 'monitor'
        : userProfiles.some((profile) => profile.name === 'element')
          ? 'unit'
          : null;
    }

    // Generar token para elemento
    const token = await new SignJWT({
      userId: user[0].system_module_user.id,
      dependencyId: user[0].system_module_user_kardex_employment?.fkInstitutionId ?? null,
      role,
    })
      .setProtectedHeader({ alg: 'HS256' }) // Algoritmo de firma
      .setIssuedAt() // Marca de tiempo de emisión
      .setExpirationTime('30d') // Tiempo de expiración
      .sign(key); // Firmar el token
    return token;
  }

  // Verificar si es usuario Root
  const [userRoot, dependencyRoot] = await Promise.all([
    db.select().from(systemModuleRootUser).where(eq(systemModuleRootUser.id, userId)).limit(1),
    db
      .select({ fkInstitutionId: catInstitution.id, key: catInstitution.key })
      .from(catInstitution)
      .where(eq(catInstitution.key, 'ARGUS'))
      .limit(1),
  ]);

  if (userRoot.length > 0) {
    if (dependencyRoot.length === 0) {
      throw new Error('No dependency found');
    }

    // Generar token para Root
    const token = await new SignJWT({
      userId: userRoot[0].id,
      dependencyId: dependencyRoot[0].fkInstitutionId,
      role: 'root',
    })
      .setProtectedHeader({ alg: 'HS256' }) // Algoritmo de firma
      .setIssuedAt() // Marca de tiempo de emisión
      .setExpirationTime('30d') // Tiempo de expiración
      .sign(key); // Firmar el token
    return token;
  }

  // Verificar si es usuario ciudadano
  const citizen = await db
    .select()
    .from(systemModuleControlCitizen)
    .where(eq(systemModuleControlCitizen.id, userId))
    .limit(1);

  if (citizen.length > 0) {
    // Generar token para ciudadano
    const token = await new SignJWT({
      userId: citizen[0].id,
      dependencyId: null,
      role: 'app',
    })
      .setProtectedHeader({ alg: 'HS256' }) // Algoritmo de firma
      .setIssuedAt() // Marca de tiempo de emisión
      .setExpirationTime('30d') // Tiempo de expiración
      .sign(key); // Firmar el token
    return token;
  }
  // Si no es ningun usuario, devolver error
  throw new Error('No user found');
};

export default generateAuthToken;
