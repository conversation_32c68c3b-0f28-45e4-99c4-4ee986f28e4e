import { Router } from 'express';
import policeJobRouter from './police_job.routes';
import policeUnitRouter from './police_unit.routes';
import policeLevelRouter from './police_level.routes';
import jobStatusRouter from './job_status.routes';
import secondmentRouter from './secondment.routes';
import institutionRouter from './institution.routes';
import educationalLevelRouter from './educational_level.routes';
import disciplinaryOffenseTypeRouter from './disciplinary_offense_type.routes';
import sanctionRouter from './sanction.routes';
import armamentConditionRouter from './armament_condition.routes';
import tacticalEquipmentRouter from './tactical_equipment.routes';
import tacticalEquipmentStatusRouter from './tactical_equipment_status.routes';
import sizeRouter from './size.routes';
import examTypeRouter from './exam_type.routes';
import examResultRouter from './exam_result.routes';
import activityTypeRouter from './activity_type.routes';
import radioBrandRouter from './radio_brand.routes';
import radioModelRouter from './radio_model.routes';
import shiftAdjustmentTypeRouter from './shift_adjustment_type.routes';
import weaponCaliberRouter from './weapon_caliber.routes';
import colorRouter from './color.routes';
import documentTypeRouter from './document_type.routes';
import externalInstitutionRouter from './external_institution.routes';
import genderRouter from './gender.routes';
import iphIncidentTypeRouter from './iph_incident_type.routes';
import iphLegalCrimeTypeRouter from './iph_legal_crime_type.routes';
import iphLegalCrimeSubtypeRouter from './iph_legal_crime_subtype.routes';
import iphLegalRightRouter from './iph_legal_right.routes';
import iphLegalCrimeRouter from './iph_legal_crime.routes';
import stateRouter from './state.routes';
import municipalityRouter from './municipality.routes';
import settlementRouter from './settlement.routes';
import nationalityRouter from './nationality.routes';
import vehicleBrandRouter from './vehicle_brand.routes';
import vehicleModelRouter from './vehicle_model.routes';
import vehicleTypeRouter from './vehicle_type.routes';
import vehicleStatusRouter from './vehicle_status.routes';
import operationalPermissionStatusRouter from './operational_permission_status.routes';
import weaponTypeRouter from './weapon_type.routes';
import ammunitionTypeRouter from './ammunition_type.routes';
import permissionTypeRouter from './permission_type.routes';
import objectTypeRouter from './object_type.routes';
import fatigueServiceTypeRouter from './fatigue_service_type.routes';
import directionTypeRouter from './direction_type.routes';
import leadershipTypeRouter from './leadership_type.routes';
import workAreaTypeRouter from './work_area_type.routes';
import catUniformTypeRouter from './uniform_type.routes';
import catTacticalEquipmentTypeRouter from './tactical_equipment_type.routes';
import maritalStatusRouter from './marital_status.routes';

const catalogsRouter = Router();

catalogsRouter.use('/police_job', policeJobRouter);
catalogsRouter.use('/police_unit', policeUnitRouter);
catalogsRouter.use('/police_level', policeLevelRouter);
catalogsRouter.use('/job_status', jobStatusRouter);
catalogsRouter.use('/secondment', secondmentRouter);
catalogsRouter.use('/institution', institutionRouter);
catalogsRouter.use('/educational_level', educationalLevelRouter);
catalogsRouter.use('/disciplinary_offense_type', disciplinaryOffenseTypeRouter);
catalogsRouter.use('/sanction', sanctionRouter);
catalogsRouter.use('/armament_condition', armamentConditionRouter);
catalogsRouter.use('/tactical_equipment', tacticalEquipmentRouter);
catalogsRouter.use('/tactical_equipment_status', tacticalEquipmentStatusRouter);
catalogsRouter.use('/size', sizeRouter);
catalogsRouter.use('/exam_type', examTypeRouter);
catalogsRouter.use('/exam_result', examResultRouter);
catalogsRouter.use('/activity_type', activityTypeRouter);
catalogsRouter.use('/radio_brand', radioBrandRouter);
catalogsRouter.use('/radio_model', radioModelRouter);
catalogsRouter.use('/shift_adjustment_type', shiftAdjustmentTypeRouter);
catalogsRouter.use('/weapon_caliber', weaponCaliberRouter);
catalogsRouter.use('/color', colorRouter);
catalogsRouter.use('/document_type', documentTypeRouter);
catalogsRouter.use('/external_institution', externalInstitutionRouter);
catalogsRouter.use('/gender', genderRouter);
catalogsRouter.use('/iph_incident_type', iphIncidentTypeRouter);
catalogsRouter.use('/iph_legal_crime_type', iphLegalCrimeTypeRouter);
catalogsRouter.use('/iph_legal_crime_subtype', iphLegalCrimeSubtypeRouter);
catalogsRouter.use('/iph_legal_right', iphLegalRightRouter);
catalogsRouter.use('/iph_legal_crime', iphLegalCrimeRouter);
catalogsRouter.use('/state', stateRouter);
catalogsRouter.use('/municipality', municipalityRouter);
catalogsRouter.use('/settlement', settlementRouter);
catalogsRouter.use('/nationality', nationalityRouter);
catalogsRouter.use('/vehicle_brand', vehicleBrandRouter);
catalogsRouter.use('/vehicle_model', vehicleModelRouter);
catalogsRouter.use('/vehicle_type', vehicleTypeRouter);
catalogsRouter.use('/vehicle_status', vehicleStatusRouter);
catalogsRouter.use('/operational_permission_status', operationalPermissionStatusRouter);
catalogsRouter.use('/weapon_type', weaponTypeRouter);
catalogsRouter.use('/ammunition_type', ammunitionTypeRouter);
catalogsRouter.use('/permission_type', permissionTypeRouter);
catalogsRouter.use('/object_type', objectTypeRouter);
catalogsRouter.use('/fatigue_service_type', fatigueServiceTypeRouter);
catalogsRouter.use('/direction_type', directionTypeRouter);
catalogsRouter.use('/leadership_type', leadershipTypeRouter);
catalogsRouter.use('/work_area_type', workAreaTypeRouter);
catalogsRouter.use('/tactical_equipment_type', catTacticalEquipmentTypeRouter);
catalogsRouter.use('/uniform_type', catUniformTypeRouter);
catalogsRouter.use('/marital_status', maritalStatusRouter);

export default catalogsRouter;
