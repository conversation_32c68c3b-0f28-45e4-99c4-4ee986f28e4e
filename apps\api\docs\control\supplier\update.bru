meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/control/supplier/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 
}

body:json {
  {
    "name": "Nombre del Proveedor",
    "contactName": "Nombre del contacto", // Opcional
    "contactPhone": "Teléfono del contacto", // Opcional
    "contactEmail": "Correo electrónico del contacto", // Opcional
    "address": "Dirección del proveedor", // Opcional
    "city": "Ciudad del proveedor", // Opcional
    "state": "Estado del proveedor", // Opcional
    "ZipCode": "Código postal del proveedor", // Opcional
    "country": "País del proveedor", // Opcional
    "rfc": "RFC del proveedor", // Opcional
    "website": "Sitio Web del proveedor", // Opcional
    "comments": "Comentarios" // Opcional
  }
}
