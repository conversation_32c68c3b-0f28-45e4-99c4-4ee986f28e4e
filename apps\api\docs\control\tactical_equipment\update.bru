meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/control/tactical_equipment/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 
}

body:json {
  {
    "serialNumber": "Número de Serie",
    "fkTacticalEquipmentTypeId": "ID Tipo de equipo táctico",
    "fkSizeId": "ID Tamaño", 
    "tacticalEquipmentStatus": "Estado equipo táctico", // ['new', 'in_use', 'damaged', 'lost', 'free', 'disabled', 'ended_use']
    "expirationDate": "Fecha de expiración", // Opcional
    "acquisitionDate": "Fecha de compra/adquisición",
    "cost": "Costo", // Number
    "fkSupplierId": "ID Proveedor", // Opcional
    "description": "Descripción" // Opcional
  }
}
