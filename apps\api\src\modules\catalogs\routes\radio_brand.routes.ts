import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/radio_brand.controller';

const catRadioBrandRouter = Router();

catRadioBrandRouter.get('/', getAll);
catRadioBrandRouter.get('/search', searchPaginated);
catRadioBrandRouter.get('/:id', getOne);
catRadioBrandRouter.post('/', create);
catRadioBrandRouter.put('/:id', update);
catRadioBrandRouter.delete('/:id', deleteOne);

export default catRadioBrandRouter;
