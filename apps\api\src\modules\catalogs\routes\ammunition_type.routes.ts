import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/ammunition_type.controller';

const catAmmunitionTypeRouter = Router();

catAmmunitionTypeRouter.get('/', getAll);
catAmmunitionTypeRouter.get('/search', searchPaginated);
catAmmunitionTypeRouter.get('/:id', getOne);
catAmmunitionTypeRouter.post('/', create);
catAmmunitionTypeRouter.put('/:id', update);
catAmmunitionTypeRouter.delete('/:id', deleteOne);

export default catAmmunitionTypeRouter;
