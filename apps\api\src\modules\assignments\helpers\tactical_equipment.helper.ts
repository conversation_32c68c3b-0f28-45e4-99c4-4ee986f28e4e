import { systemModuleAssignmentTacticalEquipment, systemModuleControlFatigue } from '@repo/shared-drizzle/schemas';
import { and, eq, ne, type ExtractTablesWithRelations } from 'drizzle-orm';
import type { NodePgQueryResultHKT } from 'drizzle-orm/node-postgres';
import type { PgTransaction } from 'drizzle-orm/pg-core';
import { z } from 'zod';

export const assignTacticalEquipmentBodySchema = z.object({
  fkUserId: z.string(),
  fkTacticalEquipmentId: z.string(),
  fkFatigueId: z.string(),
  assignmentDate: z.string(),
  assignmentEndDate: z.string().nullable().optional(),
  expirationDate: z.string().nullable().optional(),
  estimatedReturnDate: z.string().nullable().optional(),
  comments: z.string().nullable().optional(),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
export const validateDates = (body: any) => {
  const dateFields = [
    { field: 'assignmentDate', error: 'El campo assignmentDate no es una fecha valida' },
    { field: 'estimatedReturnDate', error: 'El campo estimatedReturnDate no es una fecha valida' },
    { field: 'expirationDate', error: 'El campo expirationDate no es una fecha valida' },
    { field: 'assignmentEndDate', error: 'El campo assignmentEndDate no es una fecha valida' },
  ];

  for (const df of dateFields) {
    if (body[df.field]) {
      const date = new Date(body[df.field].replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(date.getTime())) {
        throw new Error(df.error);
      }
    }
  }

  if (body.estimatedReturnDate && new Date(body.estimatedReturnDate) < new Date(body.assignmentDate)) {
    throw new Error('La fecha estimada de retorno no puede ser anterior a la fecha de inicio');
  }

  if (body.assignmentEndDate && new Date(body.assignmentEndDate) < new Date(body.assignmentDate)) {
    throw new Error('La fecha de fin de asignación no puede ser anterior a la fecha de inicio');
  }
};

export const validateTacticalEquipmentAssignmentInFatigue = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkTacticalEquipmentId: string,
  fkFatigueId: string,
  fkTacticalEquipmentAssignmentId: string,
) => {
  const existingAssignment = await trx
    .select()
    .from(systemModuleAssignmentTacticalEquipment)
    .where(
      and(
        eq(systemModuleAssignmentTacticalEquipment.fkTacticalEquipmentId, fkTacticalEquipmentId),
        eq(systemModuleAssignmentTacticalEquipment.fkFatigueId, fkFatigueId),
        ne(systemModuleAssignmentTacticalEquipment.id, fkTacticalEquipmentAssignmentId),
      ),
    )
    .limit(1);
  if (existingAssignment.length > 0) {
    throw new Error('Ya existe una asignación con el mismo equipo táctico en el despliegue indicado');
  }
};

export const validateTacticalEquipmentActiveAssignment = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkTacticalEquipmentId: string,
  fkTacticalEquipmentAssignmentId: string,
) => {
  const activeTactiticalEquipmentAssignment = await trx
    .select()
    .from(systemModuleAssignmentTacticalEquipment)
    .leftJoin(
      systemModuleControlFatigue,
      eq(systemModuleAssignmentTacticalEquipment.fkFatigueId, systemModuleControlFatigue.id),
    )
    .where(
      and(
        eq(systemModuleControlFatigue.status, 'active'),
        eq(systemModuleAssignmentTacticalEquipment.fkTacticalEquipmentId, fkTacticalEquipmentId),
        eq(systemModuleAssignmentTacticalEquipment.isDeleted, false),
        ne(systemModuleAssignmentTacticalEquipment.id, fkTacticalEquipmentAssignmentId),
      ),
    )
    .limit(1);
  if (activeTactiticalEquipmentAssignment.length > 0) {
    throw new Error('El equipo táctico esta asignada a otro elemento en un despliegue activo');
  }
};

export const handleUserChangeInTacticalEquipmentAssig = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkUserId: string,
  fkTacticalEquipmentId: string,
  fkFatigueId: string,
  fkTacticalEquipmentAssignmentId: string,
) => {
  const existingAssignment = await trx
    .select()
    .from(systemModuleAssignmentTacticalEquipment)
    .where(
      and(
        eq(systemModuleAssignmentTacticalEquipment.fkUserId, fkUserId),
        eq(systemModuleAssignmentTacticalEquipment.fkTacticalEquipmentId, fkTacticalEquipmentId),
        eq(systemModuleAssignmentTacticalEquipment.fkFatigueId, fkFatigueId),
        ne(systemModuleAssignmentTacticalEquipment.id, fkTacticalEquipmentAssignmentId), // Excluir la asignación actual
      ),
    )
    .limit(1);
  if (existingAssignment.length > 0) {
    throw new Error('El usuario ya tiene una asignación con ese equipo táctico en el mismo despliegue');
  }
};
