meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/catalogs/state/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 
}

headers {
  authorization: 
}

body:json {
  {
    "key": "test_key_change", // optional
    "name": "Cambio en nombre de prueba", // optional
    "description": "Cambio en descripcion de Nombre de prueba" // optional
  }
}
