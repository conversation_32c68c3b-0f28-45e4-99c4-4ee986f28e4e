meta {
  name: create
  type: http
  seq: 3
}

post {
  url: {{cad}}/alert/event
  body: json
  auth: none
}

body:json {
  {
    "fkIncidentTypeId": "ID Tipo de incidente",
    "incidentDescription": "Descripción del incidente",
    "fkReceptorUserId": "ID del Usuario",
    "fkAlertInformantUserId": "ID del Informante", // Opcional
    "alertType": "Tipo de alerta", // ['manual_alert', 'citizen_alert']
    "priorityType": "Tipo de prioridad", // Opcional - ['no_priority', 'low', 'medium', 'high', 'critical']
    "status": "Estatus", // ['canceled', 'in_attention']
    "alertSource": "Origen", // ['radio', 'municipal_telephone', '911', 'direct_complaint', 'app']
    "location": "Ubicación", // Opcional | Formato esperado { "lng": -121, "lat": 37 }
    "address": "Datos de domicilio", // Opcional | Todas las propiedades de domicilio
    "alertDate": "Fecha", // Formato YYYY-MM-DD
    "alertTime": "Hora", // Formato hh:mm
    "commentsOnRejection": "Comentarios sobre rechazo" // Opcional | Requerido si el estatus es 'canceled'
  }
}
