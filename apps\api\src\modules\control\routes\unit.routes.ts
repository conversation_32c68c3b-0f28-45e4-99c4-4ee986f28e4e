import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/unit.controller';

const unitRouter = Router();

unitRouter.get('/', getAll);
unitRouter.get('/search', searchPaginated);
unitRouter.get('/:id', getOne);
unitRouter.post('/', create);
unitRouter.put('/:id', update);
unitRouter.delete('/:id', deleteOne);

export default unitRouter;
