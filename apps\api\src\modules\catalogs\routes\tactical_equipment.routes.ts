import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/tactical_equipment.controller';

const catTacticalEquipmentRouter = Router();

catTacticalEquipmentRouter.get('/', getAll);
catTacticalEquipmentRouter.get('/search', searchPaginated);
catTacticalEquipmentRouter.get('/:id', getOne);
catTacticalEquipmentRouter.post('/', create);
catTacticalEquipmentRouter.put('/:id', update);
catTacticalEquipmentRouter.delete('/:id', deleteOne);

export default catTacticalEquipmentRouter;
