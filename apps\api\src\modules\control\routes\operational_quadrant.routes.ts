import { Router } from 'express';
import {
  create,
  deleteOne,
  getAll,
  getOne,
  searchPaginated,
  update,
} from '../controllers/operational_quadrant.controller';

const operationalQuadrantRouter = Router();

operationalQuadrantRouter.get('/', getAll);
operationalQuadrantRouter.get('/search', searchPaginated);
operationalQuadrantRouter.get('/:id', getOne);
operationalQuadrantRouter.post('/', create);
operationalQuadrantRouter.put('/:id', update);
operationalQuadrantRouter.delete('/:id', deleteOne);

export default operationalQuadrantRouter;
