meta {
  name: status
  type: http
  seq: 6
}

put {
  url: {{cad}}/alert/event/status/:id
  body: json
  auth: inherit
}

params:path {
  id: 0196c5c7-a4fa-c847-446f-cdc396a00b87
}

body:json {
  {
    "status": "no_resolved", // ['new','duplicated','canceled','in_attention','dispatched','resolved','no_resolved','paused','transferred']
    "source": "Origen", // ['site', 'citizen_app', 'police_app']
    "alertFolio": "ID Alerta/Evento", // Opcional
    "tags": ["ID Etiqueta 1", "ID Etiqueta 2"], // Array de identificadores de etiquetas
    "receivingDependency": "Nombre dependencia", // Opcional, solo si el estatus es 'transferred' es requerido
    "message": "El sospechoso ya no se encuentra en el lugar indicado" // Opcional, solo si el estatus es algunos de la sigueinte lista: ['resolved','no_resolved','canceled','transferred'], es requerido
  }
}
