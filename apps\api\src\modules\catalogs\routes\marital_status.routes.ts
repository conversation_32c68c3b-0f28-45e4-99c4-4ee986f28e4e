import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/marital_status.controller';

const maritalStatusRouter = Router();

maritalStatusRouter.get('/', getAll);
maritalStatusRouter.get('/search', searchPaginated);
maritalStatusRouter.get('/:id', getOne);
maritalStatusRouter.post('/', create);
maritalStatusRouter.put('/:id', update);
maritalStatusRouter.delete('/:id', deleteOne);

export default maritalStatusRouter;
