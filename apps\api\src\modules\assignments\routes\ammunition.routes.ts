import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/ammunition.controller';

const ammunitionAssignRouter = Router();

ammunitionAssignRouter.get('/', getAll);
ammunitionAssignRouter.get('/search', searchPaginated);
ammunitionAssignRouter.get('/:id', getOne);
ammunitionAssignRouter.post('/', create);
ammunitionAssignRouter.put('/:id', update);
ammunitionAssignRouter.delete('/:id', deleteOne);

export default ammunitionAssignRouter;
