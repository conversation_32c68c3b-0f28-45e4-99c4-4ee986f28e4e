import type { Request, Response } from 'express';
import { HttpStatus } from '@/utils/http_status';
import { getLocale, type Locale } from '@/utils/locale_validator';
import { Logger } from '@/utils/logger';
import { ModuleName } from '@/utils/module_names';
import db from '@/db';
import {
  systemModuleControlIphInterview,
  systemModuleControlCitizen,
  systemModuleUser,
  catDocumentType,
  catExternalInstitution,
  catGender,
  catMaritalStatus,
  catMunicipality,
  catNationality,
  catPoliceJob,
  catSecondment,
  catSettlement,
  catState,
  systemModuleControlPlaces,
  systemModuleControlIphFiles,
} from '@repo/shared-drizzle/schemas';
import { aliasedTable, and, eq, type SQL } from 'drizzle-orm';
import { sql } from 'drizzle-orm';
import { jsonbBuildObject } from '@/utils/json_build_object';

function query(options: { locale: Locale; id: string; all: true }): Promise<unknown[]>;
function query(options: { locale: Locale; id: string; all?: false }): Promise<unknown>;

async function query({ locale, id, all }: { locale: Locale; id: string; all?: boolean }): Promise<unknown | unknown[]> {
  const intervieweeCitizen = aliasedTable(systemModuleControlCitizen, 'intervieweeCitizen');
  const interviewsFilter: SQL[] = [eq(systemModuleControlIphInterview.isDeleted, false)];
  const filesFilter: SQL[] = [eq(systemModuleControlIphFiles.isDeleted, false)];
  if (all) {
    interviewsFilter.push(eq(systemModuleControlIphInterview.fkIphId, id));
    filesFilter.push(eq(systemModuleControlIphFiles.fkIphId, id));
  } else {
    interviewsFilter.push(eq(systemModuleControlIphInterview.id, id));
    filesFilter.push(eq(systemModuleControlIphFiles.annexId, id));
  }
  const s3Url = sql.raw(`${[process.env.AWS_S3_ENDPOINT, process.env.AWS_S3_BUCKET].join('/')}/`);
  const filesSubquery = db
    .select({
      fkAnnexId: systemModuleControlIphFiles.annexId,
      files: sql`json_agg(${jsonbBuildObject({
        id: systemModuleControlIphFiles.id,
        url: sql`concat('${s3Url}',${systemModuleControlIphFiles.path})`,
        name: systemModuleControlIphFiles.name,
        contentType: systemModuleControlIphFiles.contentType,
        type: systemModuleControlIphFiles.type,
        number: systemModuleControlIphFiles.number,
      })})`.as('files'),
    })
    .from(systemModuleControlIphFiles)
    .where(and(...filesFilter))
    .groupBy(systemModuleControlIphFiles.annexId)
    .as('files_subquery');

  const res = db
    .select({
      id: systemModuleControlIphInterview.id,
      iphId: systemModuleControlIphInterview.fkIphId,
      interviewAt: systemModuleControlIphInterview.interviewAt,
      intervieweeType: systemModuleControlIphInterview.intervieweeType,
      interviewee: {
        id: systemModuleControlCitizen.id,
        name: systemModuleControlCitizen.name,
        firstSurname: systemModuleControlCitizen.firstSurname,
        secondSurname: systemModuleControlCitizen.secondSurname,
        alias: systemModuleControlCitizen.alias,
        gender: sql`json_build_object('id', ${catGender.id}, 'name', ${catGender[locale]})`,
        bornAt: systemModuleControlCitizen.bornAt,
        phone: systemModuleControlCitizen.phone,
        email: systemModuleControlCitizen.email,
        address: sql`json_build_object(
          'id', ${systemModuleControlPlaces.id},
          'state', json_build_object('id', ${catState.id}, 'name', ${catState[locale]}),
          'municipality', json_build_object('id', ${catMunicipality.id}, 'name', ${catMunicipality[locale]}),
          'colony', json_build_object('id', ${catSettlement.id}, 'name', ${catSettlement[locale]}),
          'name', ${systemModuleControlPlaces.name},
          'type', ${systemModuleControlPlaces.type},
          'zipCode', ${systemModuleControlPlaces.zipCode},
          'street', ${systemModuleControlPlaces.street},
          'number', ${systemModuleControlPlaces.number},
          'interiorNumber', ${systemModuleControlPlaces.interiorNumber},
          'betweenStreet1', ${systemModuleControlPlaces.betweenStreet1},
          'betweenStreet2', ${systemModuleControlPlaces.betweenStreet2},
          'reference', ${systemModuleControlPlaces.reference},
          'location', ${systemModuleControlPlaces.location},
          'sectors', ${systemModuleControlPlaces.sectors}
        )`,
        documentType: sql`json_build_object('id', ${catDocumentType.id}, 'name', ${catDocumentType[locale]})`,
        nationality: sql`json_build_object('id', ${catNationality.id}, 'name', ${catNationality[locale]})`,
        documentIdentificationNumber: systemModuleControlCitizen.documentIdentificationNumber,
        maritalStatus: sql`json_build_object('id', ${catMaritalStatus.id}, 'name', ${catMaritalStatus[locale]})`,
        emergencyContactId: systemModuleControlCitizen.fkEmercencyContactCitizenId,
        curp: systemModuleControlCitizen.curp,
        rfc: systemModuleControlCitizen.rfc,
        occupation: systemModuleControlCitizen.occupation,
      },
      story: systemModuleControlIphInterview.story,
      notes: systemModuleControlIphInterview.notes,
      wasIntervieweeTransferred: systemModuleControlIphInterview.wasIntervieweeTransferred,
      transferExternalInstitution: sql`json_build_object('id', ${catExternalInstitution.id}, 'name', ${catExternalInstitution[locale]})`,
      wasInformedOfRights: systemModuleControlIphInterview.wasInformedOfRights,
      firstRespondent: {
        id: systemModuleUser.id,
        name: sql`concat(${systemModuleControlCitizen.name}, ' ', ${systemModuleControlCitizen.firstSurname},' ', ${systemModuleControlCitizen.secondSurname})`,
        position: sql`json_build_object('id', ${catPoliceJob.id}, 'name', ${catPoliceJob[locale]})`,
        secondment: sql`json_build_object('id', ${catSecondment.id}, 'name', ${catSecondment[locale]})`,
      },
      files: filesSubquery.files,
      updatedAt: systemModuleControlIphInterview.updatedAt,
      createdAt: systemModuleControlIphInterview.createdAt,
    })
    .from(systemModuleControlIphInterview)
    .leftJoin(
      catExternalInstitution,
      eq(catExternalInstitution.id, systemModuleControlIphInterview.fkTransferExternalInstitutionId),
    )
    // first responder
    .leftJoin(systemModuleUser, eq(systemModuleUser.id, systemModuleControlIphInterview.fkFirstRespondentUserId))
    .leftJoin(systemModuleControlCitizen, eq(systemModuleControlCitizen.id, systemModuleUser.fkCitizenId))
    .leftJoin(catPoliceJob, eq(catPoliceJob.id, systemModuleControlIphInterview.fkFirstRespondentPositionId))
    .leftJoin(catSecondment, eq(catSecondment.id, systemModuleControlIphInterview.fkFirstRespondentSecondmentId))
    // interviewee
    .leftJoin(intervieweeCitizen, eq(intervieweeCitizen.id, systemModuleControlIphInterview.fkIntervieweeCitizenId))
    .leftJoin(catNationality, eq(catNationality.id, intervieweeCitizen.fkNationalityId))
    .leftJoin(catDocumentType, eq(catDocumentType.id, intervieweeCitizen.fkDocumentTypeId))
    .leftJoin(catMaritalStatus, eq(catMaritalStatus.id, intervieweeCitizen.fkMaritalStatusId))
    .leftJoin(catGender, eq(catGender.id, intervieweeCitizen.fkGenderId))
    .leftJoin(systemModuleControlPlaces, eq(systemModuleControlPlaces.id, intervieweeCitizen.fkAddressPlaceId))
    .leftJoin(catState, eq(catState.id, systemModuleControlPlaces.fkStateId))
    .leftJoin(catMunicipality, eq(catMunicipality.id, systemModuleControlPlaces.fkMunicipalityId))
    .leftJoin(catSettlement, eq(catSettlement.id, systemModuleControlPlaces.fkColonyId))
    .leftJoin(filesSubquery, eq(filesSubquery.fkAnnexId, systemModuleControlIphInterview.id))
    .where(and(...interviewsFilter));

  const data = await res;
  if (data.length === 0) null;
  if (all) return data;
  return data[0];
}

export const get = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale: Locale = getLocale();
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const queryResult = await query({ locale, id });
    if (queryResult === null) {
      res.status(HttpStatus.NOT_FOUND).json({ message: 'Not found' });
      return;
    }
    res.status(HttpStatus.OK).json(queryResult);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      module: ModuleName.IPH,
      source: req.headers['user-agent'],
      method: '/iph/section/interviews/get/:id - get',
    });
  }
};

export const getAll = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale: Locale = getLocale();
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const queryResult = await query({ locale, id, all: true });
    if (queryResult === null) {
      res.status(HttpStatus.NOT_FOUND).json({ message: 'Not found' });
      return;
    }
    res.status(HttpStatus.OK).json(queryResult);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      module: ModuleName.IPH,
      source: req.headers['user-agent'],
      method: '/iph/annex/interviews/get/all/:id - getAll',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};
