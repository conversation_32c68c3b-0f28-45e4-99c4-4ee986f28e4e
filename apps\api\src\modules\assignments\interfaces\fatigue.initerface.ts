interface FatigueAssignments {
  unit: FatigueAssignUnit | null;
  places: string[];
  quadrants: FatigueAssignQuadrants[];
}

interface FatigueAssignUnit {
  id: string;
  fkUnitControlId: string;
  isDriver: boolean;
  isPassenger: boolean;
  isInCharge: boolean;
  comments: string | null;
}

interface FatigueAssignQuadrants {
  id: string;
  fkOperationQuadrantId: string;
  comments: string | null;
}

interface FatigueAssignBaseData {
  id: string;
  assignments: FatigueAssignments;
  observations: string | null;
}

export interface FatigueByFatigueData extends FatigueAssignBaseData {
  fkUserId: string;
  aliasUser: string | null;
}

export interface FatigueByUserData extends FatigueAssignBaseData {
  fkFatigueId: string;
}

export interface FatiguePlaceAssignment {
  fkUserId: string;
  fkFatigueId: string;
  fkFatiguePlaceId: string;
  assignmentDate: string;
  assignmentEndDate: string | null;
  comments: string | null;
  fkLastUserId: string;
  fkLastDependencyId: string;
  isEnabled?: boolean;
  isDeleted?: boolean;
  deletedAt?: string | null;
  createdAt?: string;
  updatedAt?: string;
}
