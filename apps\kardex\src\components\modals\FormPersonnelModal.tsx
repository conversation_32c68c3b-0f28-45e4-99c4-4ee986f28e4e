import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@repo/ui/components/button';
import { DialogContent, DialogFooter, DialogHeader, DialogTitle } from '@repo/ui/components/dialog';
import { Input } from '@repo/ui/components/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@repo/ui/components/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@repo/ui/components/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/ui/components/select';
import { ScrollArea } from '@repo/ui/components/scroll-area';
import { Checkbox } from '@repo/ui/components/checkbox';
import { PhoneInput } from '@repo/ui/components/phone-input';
import { DatePicker } from '@repo/ui/components/DatePicker';
import { ComboboxComplex } from '@repo/ui/components/ComboboxComplex';
import { useModalStore } from '@/components/stores/modal-store';
import { generateUlid } from '@/lib/utils';

// Definición del esquema
export const personnelSchema = z.object({
  // Datos básicos
  id: z.string().optional(),
  profilePhoto: z.string().optional(),
  firstName: z.string().min(1, 'El nombre es requerido'),
  firstLastName: z.string().min(1, 'El primer apellido es requerido'),
  secondLastName: z.string().optional(),
  gender: z.enum(['male', 'female', 'other'], {
    required_error: 'El sexo es requerido',
  }),
  phoneNumber: z.string().min(10, 'Se requiere un número de teléfono válido'),
  birthDate: z.date({
    required_error: 'La fecha de nacimiento es requerida',
  }),
  address: z.string().min(1, 'La dirección es requerida'),
  curp: z.string().min(18, 'La CURP debe tener 18 caracteres').max(18),
  rfc: z.string().min(10, 'El RFC debe tener al menos 10 caracteres'),
  nationality: z.string().min(1, 'La nacionalidad es requerida'),
  maritalStatus: z.enum(['single', 'married', 'divorced', 'widowed', 'other'], {
    required_error: 'El estado civil es requerido',
  }),
  educationLevel: z.string().min(1, 'El nivel educativo es requerido'),
  email: z.string().email('Dirección de correo electrónico inválida'),

  // Datos laborales
  employeeNumber: z.number({
    required_error: 'El número de empleado es requerido',
    invalid_type_error: 'El número de empleado debe ser un número',
  }),
  cup: z.string().min(1, 'El CUP es requerido'),
  grade: z.string().min(1, 'El grado es requerido'),
  assignment: z.object({
    id: z.string(),
    name: z.string(),
  }),
  institution: z.string().min(1, 'La institución es requerida'),
  entity: z.object({
    id: z.string(),
    name: z.string(),
  }),
  directSupervisor: z.object({
    id: z.string(),
    name: z.string(),
  }),
  department: z.object({
    id: z.string(),
    name: z.string(),
  }),
  assignedUnit: z.object({
    id: z.string(),
    name: z.string(),
  }),
  sector: z.object({
    id: z.string(),
    name: z.string(),
  }),

  // Datos médicos
  bloodType: z.string().min(1, 'El tipo de sangre es requerido'),
  height: z.number({
    required_error: 'La estatura es requerida',
    invalid_type_error: 'La estatura debe ser un número',
  }),
  weight: z.number({
    required_error: 'El peso es requerido',
    invalid_type_error: 'El peso debe ser un número',
  }),
  allergies: z.string().optional(),
  conditions: z.string().optional(),
  medicalInsurance: z.enum(['public', 'private', 'none'], {
    required_error: 'El tipo de seguro médico es requerido',
  }),
  wheelchairUser: z.boolean().default(false),
});

type PersonnelFormData = z.infer<typeof personnelSchema>;

interface FormPersonnelModalProps {
  onCancel: () => void;
  onSave: (data: PersonnelFormData) => void;
  title?: string;
}

// Datos de ejemplo para los combobox
const mockAssignments = [
  { id: '1', name: 'Asignación 1' },
  { id: '2', name: 'Asignación 2' },
];

const mockEntities = [
  { id: '1', name: 'Entidad 1' },
  { id: '2', name: 'Entidad 2' },
];

const mockSupervisors = [
  { id: '1', name: 'Supervisor 1' },
  { id: '2', name: 'Supervisor 2' },
];

const mockDepartments = [
  { id: '1', name: 'Departamento 1' },
  { id: '2', name: 'Departamento 2' },
];

const mockUnits = [
  { id: '1', name: 'Unidad 1' },
  { id: '2', name: 'Unidad 2' },
];

const mockSectors = [
  { id: '1', name: 'Sector 1' },
  { id: '2', name: 'Sector 2' },
];

export default function FormPersonnelModal({ onSave, title = 'Agregar personal' }: FormPersonnelModalProps) {
  const { closeModal } = useModalStore();

  // Inicialización del formulario
  const form = useForm<PersonnelFormData>({
    resolver: zodResolver(personnelSchema),
    mode: 'onBlur',
    defaultValues: {
      id: generateUlid(),
      profilePhoto: '',
      firstName: '',
      firstLastName: '',
      secondLastName: '',
      phoneNumber: '',
      address: '',
      curp: '',
      rfc: '',
      nationality: '',
      educationLevel: '',
      email: '',
      cup: '',
      grade: '',
      institution: '',
      allergies: '',
      conditions: '',
      wheelchairUser: false,
    },
  });

  const isFormValid = form.formState.isValid;

  return (
    <DialogContent className="max-w-[600px] bg-[var(--bg-0-light)]">
      <DialogHeader>
        <DialogTitle>{title}</DialogTitle>
      </DialogHeader>
      <ScrollArea className="max-h-[680px]">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit((data) => onSave(data))}
            className="flex flex-col py-[var(--spacing-s-2)] px-0"
          >
            <Tabs defaultValue="basic" className="w-full px-[var(--spacing-s-5)] pt-[var(--spacing-s-4)]">
              <TabsList className="w-full flex justify-center">
                <TabsTrigger value="basic" className="flex-1">
                  Datos básicos
                </TabsTrigger>
                <TabsTrigger value="job" className="flex-1">
                  Laboral
                </TabsTrigger>
                <TabsTrigger value="medical" className="flex-1">
                  Datos médicos
                </TabsTrigger>
              </TabsList>

              {/* Tab: Datos Básicos */}
              <TabsContent value="basic">
                <div className="flex flex-col py-[var(--spacing-s-5)] gap-5">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Nombre(s)</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Ingrese nombre(s)" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="firstLastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Primer apellido</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Ingrese primer apellido" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="secondLastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Segundo apellido</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Ingrese segundo apellido" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="gender"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Sexo</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Seleccione sexo" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="male">Masculino</SelectItem>
                              <SelectItem value="female">Femenino</SelectItem>
                              <SelectItem value="other">Otro</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="phoneNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Teléfono</FormLabel>
                          <FormControl>
                            <PhoneInput value={field.value} onChange={field.onChange} defaultCountry="MX" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="birthDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Fecha de nacimiento</FormLabel>
                          <FormControl>
                            <DatePicker
                              value={field.value}
                              onDateChange={field.onChange}
                              placeholder="Seleccione fecha"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
                    <FormField
                      control={form.control}
                      name="address"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Dirección</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Ingrese dirección" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  {/* Subtitle */}
                  <div className="pl-[var(--spacing-s-1)] pt-[var(--spacing-s-2)] font-semibold text-sm text-[var(--btn-default-text)] ">
                    Identificación
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
                    <FormField
                      control={form.control}
                      name="curp"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>CURP</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Ingrese CURP" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="rfc"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>RFC</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Ingrese RFC" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
                    <FormField
                      control={form.control}
                      name="nationality"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Nacionalidad</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Ingrese nacionalidad" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="maritalStatus"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Estado civil</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Seleccione estado civil" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="single">Soltero(a)</SelectItem>
                              <SelectItem value="married">Casado(a)</SelectItem>
                              <SelectItem value="divorced">Divorciado(a)</SelectItem>
                              <SelectItem value="widowed">Viudo(a)</SelectItem>
                              <SelectItem value="other">Otro</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="educationLevel"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Escolaridad</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Ingrese nivel educativo" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Correo</FormLabel>
                          <FormControl>
                            <Input {...field} type="email" placeholder="Ingrese correo electrónico" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </TabsContent>

              {/* Tab: Laboral */}
              <TabsContent value="job" className="mt-0">
                <div className="flex flex-col px-[var(--spacing-s-5)] py-[var(--spacing-s-4)] gap-5">
                  <div className="grid grid-cols-2 gap-5">
                    <FormField
                      control={form.control}
                      name="employeeNumber"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>No. empleado</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Ingrese número de empleado"
                              {...field}
                              onChange={(e) =>
                                field.onChange(e.target.value === '' ? undefined : Number(e.target.value))
                              }
                              value={field.value === undefined ? '' : field.value}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="cup"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>CUP</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Ingrese CUP" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="grade"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Grado</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Ingrese grado" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="assignment"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Adscripción</FormLabel>
                          <FormControl>
                            <ComboboxComplex
                              data={mockAssignments}
                              valueKey="id"
                              labelKey="name"
                              value={field.value?.id || ''}
                              onValueChange={(value) => {
                                const selected = mockAssignments.find((item) => item.id === value);
                                field.onChange(selected || { id: '', name: '' });
                              }}
                              placeholder="Seleccione adscripción"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="institution"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Institución</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Ingrese institución" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="entity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Entidad</FormLabel>
                          <FormControl>
                            <ComboboxComplex
                              data={mockEntities}
                              valueKey="id"
                              labelKey="name"
                              value={field.value?.id || ''}
                              onValueChange={(value) => {
                                const selected = mockEntities.find((item) => item.id === value);
                                field.onChange(selected || { id: '', name: '' });
                              }}
                              placeholder="Seleccione entidad"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="directSupervisor"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Jefe directo</FormLabel>
                          <FormControl>
                            <ComboboxComplex
                              data={mockSupervisors}
                              valueKey="id"
                              labelKey="name"
                              value={field.value?.id || ''}
                              onValueChange={(value) => {
                                const selected = mockSupervisors.find((item) => item.id === value);
                                field.onChange(selected || { id: '', name: '' });
                              }}
                              placeholder="Seleccione jefe directo"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="department"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Departamento</FormLabel>
                          <FormControl>
                            <ComboboxComplex
                              data={mockDepartments}
                              valueKey="id"
                              labelKey="name"
                              value={field.value?.id || ''}
                              onValueChange={(value) => {
                                const selected = mockDepartments.find((item) => item.id === value);
                                field.onChange(selected || { id: '', name: '' });
                              }}
                              placeholder="Seleccione departamento"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="assignedUnit"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Unidad asignada</FormLabel>
                          <FormControl>
                            <ComboboxComplex
                              data={mockUnits}
                              valueKey="id"
                              labelKey="name"
                              value={field.value?.id || ''}
                              onValueChange={(value) => {
                                const selected = mockUnits.find((item) => item.id === value);
                                field.onChange(selected || { id: '', name: '' });
                              }}
                              placeholder="Seleccione unidad"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="sector"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Sector</FormLabel>
                          <FormControl>
                            <ComboboxComplex
                              data={mockSectors}
                              valueKey="id"
                              labelKey="name"
                              value={field.value?.id || ''}
                              onValueChange={(value) => {
                                const selected = mockSectors.find((item) => item.id === value);
                                field.onChange(selected || { id: '', name: '' });
                              }}
                              placeholder="Seleccione sector"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </TabsContent>

              {/* Tab: Datos médicos */}
              <TabsContent value="medical" className="mt-0">
                <div className="flex flex-col px-[var(--spacing-s-5)] py-[var(--spacing-s-4)] gap-5">
                  <div className="grid grid-cols-2 gap-5">
                    <FormField
                      control={form.control}
                      name="bloodType"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Grupo sanguíneo</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Ingrese grupo sanguíneo" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="height"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Estatura</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Ingrese estatura en cm"
                              {...field}
                              onChange={(e) =>
                                field.onChange(e.target.value === '' ? undefined : Number(e.target.value))
                              }
                              value={field.value === undefined ? '' : field.value}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="weight"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Peso</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Ingrese peso en kg"
                              {...field}
                              onChange={(e) =>
                                field.onChange(e.target.value === '' ? undefined : Number(e.target.value))
                              }
                              value={field.value === undefined ? '' : field.value}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="allergies"
                      render={({ field }) => (
                        <FormItem className="col-span-2">
                          <FormLabel>Alergias</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Ingrese alergias" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="conditions"
                      render={({ field }) => (
                        <FormItem className="col-span-2">
                          <FormLabel>Padecimientos</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Ingrese padecimientos" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="medicalInsurance"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Seguro médico</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Seleccione tipo de seguro" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="public">Público</SelectItem>
                              <SelectItem value="private">Privado</SelectItem>
                              <SelectItem value="none">Ninguno</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="wheelchairUser"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md p-4">
                          <FormControl>
                            <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                          </FormControl>
                          <div className="space-y-1 leading-none">
                            <FormLabel>Usa silla de ruedas</FormLabel>
                          </div>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </form>
        </Form>
      </ScrollArea>
      <DialogFooter>
        <div className="flex gap-3">
          <Button type="button" onClick={closeModal}>
            Cancelar
          </Button>
          <Button
            variant={'primary'}
            disabled={!isFormValid}
            onClick={form.handleSubmit((data) => {
              onSave(data);
              closeModal();
            })}
          >
            Crear personal
          </Button>
        </div>
      </DialogFooter>
    </DialogContent>
  );
}
