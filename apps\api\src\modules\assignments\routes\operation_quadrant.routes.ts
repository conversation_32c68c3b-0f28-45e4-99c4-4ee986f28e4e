import { Router } from 'express';
import {
  create,
  deleteOne,
  getAll,
  getOne,
  searchPaginated,
  update,
} from '../controllers/operation_quadrant.controller';

const operationQuadrantRouter = Router();

operationQuadrantRouter.get('/', getAll);
operationQuadrantRouter.get('/search', searchPaginated);
operationQuadrantRouter.get('/:id', getOne);
operationQuadrantRouter.post('/', create);
operationQuadrantRouter.put('/:id', update);
operationQuadrantRouter.delete('/:id', deleteOne);

export default operationQuadrantRouter;
