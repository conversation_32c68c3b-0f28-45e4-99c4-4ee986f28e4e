meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/control/permission/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 
}

body:json {
  {
    "id": "ID Permiso",
    "name": "Nombre de prueba",
    "description": "Descripción de prueba", // Opcional
    "canRead": true,
    "canCreate": true,
    "canUpdate": true,
    "canDelete": true,
    "fkProfileId": "ID Perfil",
    "fkModulePermissionId": "ID Módulo (Donde se aplicaran los permisos)"
  }
}
