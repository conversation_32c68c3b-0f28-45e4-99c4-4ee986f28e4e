import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/police_unit.controller';

const catPoliceUnitRouter = Router();

catPoliceUnitRouter.get('/', getAll);
catPoliceUnitRouter.get('/search', searchPaginated);
catPoliceUnitRouter.get('/:id', getOne);
catPoliceUnitRouter.post('/', create);
catPoliceUnitRouter.put('/:id', update);
catPoliceUnitRouter.delete('/:id', deleteOne);

export default catPoliceUnitRouter;
