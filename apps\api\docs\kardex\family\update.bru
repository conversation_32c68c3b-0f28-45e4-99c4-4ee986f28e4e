meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/kardex/family/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 
}

body:json {
  {
    "fkUserId": "ID Usuario",
    "fkFatherCitizenId": "ID Ciudadano", // Opcional
    "fkMotherCitizenId": "ID Ciudadano", // Opcional
    "fkSpouseCitizenId": "ID Ciudadano", // Opcional
    "numberOfChildren": 0
  }
}
