import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/permission_type.controller';

const catPermissionTypeRouter = Router();

catPermissionTypeRouter.get('/', getAll);
catPermissionTypeRouter.get('/search', searchPaginated);
catPermissionTypeRouter.get('/:id', getOne);
catPermissionTypeRouter.post('/', create);
catPermissionTypeRouter.put('/:id', update);
catPermissionTypeRouter.delete('/:id', deleteOne);

export default catPermissionTypeRouter;
