meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/assignments/radio/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 
}

body:json {
  {
    "fkUserId": "ID Usuario",
    "fkControlRadioId": "ID Radio",
    "fkFatigueId": "ID Fatiga/Despliegue",
    "assignmentDate": "Fecha asignación",
    "assignmentEndDate": "Fecha entrega o desasignación", // Opcional
    "estimatedReturnDate": "Fecha estimada de entrega", // Opcional
    "comments": "Comentarios o Notas" // Opcional
  }
}
