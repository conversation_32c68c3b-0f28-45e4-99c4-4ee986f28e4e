import type { Request, Response } from 'express';
import { catAmmunitionType, catWeaponType, systemModuleControlWeapon } from '@repo/shared-drizzle/schemas';
import db from '@/db';
import { and, asc, eq, gt, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { getLocale } from '@/utils/locale_validator';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import { weaponBodySchema } from '../helpers/weapon.helper';
import z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS } = req.query;
  let [deleted, disabled, locale] = [false, false, getLocale()];

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlWeapon.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlWeapon.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlWeapon.id,
        serialNumber: systemModuleControlWeapon.serialNumber,
        nationalRegistryNumber: systemModuleControlWeapon.nationalRegistryNumber,
        fkWeaponTypeId: systemModuleControlWeapon.fkWeaponTypeId,
        nameWeaponType: catWeaponType[locale],
        weaponSize: systemModuleControlWeapon.weaponSize,
        fkAmmunitionTypeId: systemModuleControlWeapon.fkAmmunitionTypeId,
        nameAmmunitionType: catAmmunitionType[locale],
        locId: systemModuleControlWeapon.locId,
        weaponStatus: systemModuleControlWeapon.weaponStatus,
        ownershipStatus: systemModuleControlWeapon.ownershipStatus,
      })
      .from(systemModuleControlWeapon)
      .leftJoin(catWeaponType, eq(systemModuleControlWeapon.fkWeaponTypeId, catWeaponType.id))
      .leftJoin(catAmmunitionType, eq(systemModuleControlWeapon.fkAmmunitionTypeId, catAmmunitionType.id))
      .where(and(...filters));
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const result = await db
      .select({
        id: systemModuleControlWeapon.id,
        serialNumber: systemModuleControlWeapon.serialNumber,
        nationalRegistryNumber: systemModuleControlWeapon.nationalRegistryNumber,
        fkWeaponTypeId: systemModuleControlWeapon.fkWeaponTypeId,
        nameWeaponType: catWeaponType[locale],
        weaponSize: systemModuleControlWeapon.weaponSize,
        fkAmmunitionTypeId: systemModuleControlWeapon.fkAmmunitionTypeId,
        nameAmmunitionType: catAmmunitionType[locale],
        locId: systemModuleControlWeapon.locId,
        weaponStatus: systemModuleControlWeapon.weaponStatus,
        ownershipStatus: systemModuleControlWeapon.ownershipStatus,
      })
      .from(systemModuleControlWeapon)
      .leftJoin(catWeaponType, eq(systemModuleControlWeapon.fkWeaponTypeId, catWeaponType.id))
      .leftJoin(catAmmunitionType, eq(systemModuleControlWeapon.fkAmmunitionTypeId, catAmmunitionType.id))
      .where(eq(systemModuleControlWeapon.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const insertData = weaponBodySchema.parse({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_ARMAS);
    if (!systemModule) {
      throw new Error('System module CTRL_ARMAS not found');
    }
    const result = await db.insert(systemModuleControlWeapon).values({
      ...insertData,
      fkSystemModuleId: systemModule.id,
    });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;
    // Validar payload
    const updateData = weaponBodySchema.parse({ ...req.body });
    const result = await db
      .update(systemModuleControlWeapon)
      .set({ ...updateData })
      .where(eq(systemModuleControlWeapon.id, id));
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .update(systemModuleControlWeapon)
      .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
      .where(eq(systemModuleControlWeapon.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    locale: localeQS,
    q: qQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlWeapon.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlWeapon.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleControlWeapon.serialNumber, `%${q}%`));
      likeFilters.push(like(systemModuleControlWeapon.nationalRegistryNumber, `%${q}%`));
      likeFilters.push(like(catWeaponType[locale], `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlWeapon.weaponSize}::text`, `%${q}%`));
      likeFilters.push(like(catAmmunitionType[locale], `%${q}%`));
      likeFilters.push(like(systemModuleControlWeapon.locId, `%${q}%`));
      likeFilters.push(like(systemModuleControlWeapon.weaponStatus, `%${q}%`));
      likeFilters.push(like(systemModuleControlWeapon.ownershipStatus, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleControlWeapon.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlWeapon.id,
        serialNumber: systemModuleControlWeapon.serialNumber,
        nationalRegistryNumber: systemModuleControlWeapon.nationalRegistryNumber,
        fkWeaponTypeId: systemModuleControlWeapon.fkWeaponTypeId,
        nameWeaponType: catWeaponType[locale],
        weaponSize: systemModuleControlWeapon.weaponSize,
        fkAmmunitionTypeId: systemModuleControlWeapon.fkAmmunitionTypeId,
        nameAmmunitionType: catAmmunitionType[locale],
        locId: systemModuleControlWeapon.locId,
        weaponStatus: systemModuleControlWeapon.weaponStatus,
        ownershipStatus: systemModuleControlWeapon.ownershipStatus,
      })
      .from(systemModuleControlWeapon)
      .leftJoin(catWeaponType, eq(systemModuleControlWeapon.fkWeaponTypeId, catWeaponType.id))
      .leftJoin(catAmmunitionType, eq(systemModuleControlWeapon.fkAmmunitionTypeId, catAmmunitionType.id))
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlWeapon.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
