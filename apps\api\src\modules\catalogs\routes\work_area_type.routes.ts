import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/work_area_type.controller';

const workAreaTypeRouter = Router();

workAreaTypeRouter.get('/', getAll);
workAreaTypeRouter.get('/search', searchPaginated);
workAreaTypeRouter.get('/:id', getOne);
workAreaTypeRouter.post('/', create);
workAreaTypeRouter.put('/:id', update);
workAreaTypeRouter.delete('/:id', deleteOne);

export default workAreaTypeRouter;
