import type { Request, Response } from 'express';
import db from '@/db';
import { z } from 'zod';
import { and, asc, eq, gt, inArray, like, or, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { ModuleName } from '@/utils/module_names';
import { systemModuleProfile, systemModuleRol, systemModulePivotRolProfile } from '@repo/shared-drizzle/schemas';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { profileBodySchema } from '../helpers/profile.helper';
import type { IPivotRoleProfile } from '../interfaces/profile.interface';
import SystemModuleCache from '@/utils/system_module_cache';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS } = req.query;
  let deleted = false;
  let disabled = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleProfile.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleProfile.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleProfile.id,
        name: systemModuleProfile.name,
        description: systemModuleProfile.description,
      })
      .from(systemModuleProfile)
      .where(and(...filters));
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.PROFILE}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleProfile.id,
        name: systemModuleProfile.name,
        description: systemModuleProfile.description,
      })
      .from(systemModuleProfile)
      .where(eq(systemModuleProfile.id, id))
      .limit(1);
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.PROFILE}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const insertData = await profileBodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_PERFILES);
    if (!systemModule) {
      throw new Error('System module CTRL_PERFILES not found');
    }
    const result = await db.insert(systemModuleProfile).values({
      ...insertData,
      fkSystemModuleId: systemModule.id,
    });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.PROFILE} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const updateData = await profileBodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const result = await db
      .update(systemModuleProfile)
      .set({ ...updateData })
      .where(eq(systemModuleProfile.id, id));
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.PROFILE}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .update(systemModuleProfile)
      .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
      .where(eq(systemModuleProfile.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.PROFILE}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, deleted: deletedQS, disabled: disabledQS, q: qQS } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleProfile.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleProfile.isEnabled, true));
    }
    if (q) {
      filters.push(like(systemModuleProfile.name, `%${q}%`));
      filters.push(like(systemModuleProfile.description, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleProfile.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleProfile.id,
        name: systemModuleProfile.name,
        description: systemModuleProfile.description,
      })
      .from(systemModuleProfile)
      .where(and(...filters))
      .limit(limit)
      .orderBy(asc(systemModuleProfile.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.PROFILE} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const assignRoleToProfile = async (req: Request, res: Response) => {
  const { fkRolId, arrFkProfileId }: { fkRolId: string; arrFkProfileId: string[] } = req.body;
  try {
    // Obtener los perfiles almacenados en el array
    const sqProfile = db
      .$with('sq')
      .as(db.select().from(systemModuleProfile).where(inArray(systemModuleProfile.id, arrFkProfileId)));
    const profile = await db.with(sqProfile).select().from(sqProfile);
    if (profile.length === 0) {
      throw new Error('Profile not found');
    }
    // Verificar que todos los perfiles almacenados en el array existan en el sistema
    if (profile.length !== arrFkProfileId.length) {
      // Obtener los perfiles que no existen en el sistema
      const missing = arrFkProfileId.filter((item) => !profile.some((profile) => profile.id === item));
      res.status(HttpStatus.NOT_FOUND).json({ message: 'Profiles not found', result: missing });
      return;
    }
    // Obtener el rol
    const sqRol = db.$with('sq').as(db.select().from(systemModuleRol).where(eq(systemModuleRol.id, fkRolId)));
    const rol = await db.with(sqRol).select().from(sqRol);
    if (rol.length === 0) {
      throw new Error('Role not found');
    }
    const filters: SQL[] = [];
    const insertValues: IPivotRoleProfile[] = [];
    for (const p of arrFkProfileId) {
      // Almacenar en una lista la relación rol-perfil para posteriormente verificar si ya está asignado
      const filter = and(
        eq(systemModulePivotRolProfile.fkRolId, fkRolId),
        eq(systemModulePivotRolProfile.fkProfileId, p),
      );
      if (filter !== undefined) {
        filters.push(filter);
      }
      if (req.userId !== undefined && req.dependencyId !== undefined) {
        // Listar los valores que se van a insertar en la tabla
        insertValues.push({ fkRolId, fkProfileId: p, fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId });
      }
    }
    // Verificar si ya está asignado
    const sqAssignment = db.$with('sq').as(
      db
        .select()
        .from(systemModulePivotRolProfile)
        .where(or(...filters)),
    );
    const assignmentExist = await db.with(sqAssignment).select().from(sqAssignment);
    if (assignmentExist.length > 0) {
      // Obtener los perfiles que ya están asignados
      const assigned = arrFkProfileId.filter((item) =>
        assignmentExist.some((assign) => assign.fkProfileId === item && assign.fkRolId === fkRolId),
      );
      res.status(HttpStatus.NOT_FOUND).json({ message: 'Profiles already assigned', result: assigned });
      return;
    }
    // Insertar los valores en la tabla
    const result = await db.insert(systemModulePivotRolProfile).values(insertValues);
    res.status(HttpStatus.OK).json({ message: 'Profile assigned', result });
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack: stack,
      method: `${ModuleName.PROFILE} - assignRoleToProfile`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
