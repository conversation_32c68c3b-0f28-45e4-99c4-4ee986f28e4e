meta {
  name: create
  type: http
  seq: 5
}

post {
  url: {{base}}/kardex/employment?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

body:json {
  {
    "fkUserId": "ID Usuario",
    "cup": "ID CUP",
    "entryDate": "Fecha Ingreso",
    "departureDate": "Fecha finalización", // Opcional
    "fkCurrentJobPositionId": "ID Puesto Actual",
    "fkDirectionId": "ID Dirección Policial", // Opcional
    "fkLeadershipId": "ID Jefatura Policial", // Opcional
    "fkWorkAreaId": "ID Area de trabajo Policial", // Opcional
    "fkJobStatusId": "ID Estatus del trabajo",
    "fkSecondmentId": "ID Adscripción",
    "fkInstitutionId": "ID Institución",
    "federalEntity": "Entidad federativa", // Requerido - Revisar listado "federalEntitiesValue"
    "inmediateBoss": "Identificador de usuario",
    "certifications": [
      {
        "name": "Nombre certificado",
        "startDate": "Fecha inicio certificado",
        "endDate": "Fecha fin certificado"
      }
    ],
    "fkShiftId": "ID Turno", // Opcional
    "jobDescription": "Descripción del trabajo realizado"
  }
}
