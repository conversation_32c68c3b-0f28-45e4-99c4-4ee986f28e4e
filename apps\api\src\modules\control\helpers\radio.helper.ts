import { radioStatusValues } from '@repo/types/values';
import { z } from 'zod';

export const radioBodySchema = z.object({
  serialNumber: z.string(),
  nationalRegistryNumber: z.string(),
  fkRadioBrandId: z.string(),
  fkRadioModelId: z.string(),
  status: z.enum(radioStatusValues),
  expirationDate: z.string().nullable().optional(),
  acquisitionDate: z.string(),
  cost: z.number(),
  description: z.string().nullable().optional(),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});
