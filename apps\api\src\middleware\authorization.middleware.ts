import db from '@/db';
import { systemModuleRootUser, systemModuleUser, systemModuleUserKardexEmployment } from '@repo/shared-drizzle/schemas';
import { HttpStatus } from '@/utils/http_status';
import { Logger } from '@/utils/logger';
import { and, count, eq } from 'drizzle-orm';
import type { Request, Response, NextFunction } from 'express';
import { decodeJwt, jwtVerify } from 'jose';
import moment from 'moment';
import { updateRootLastActivityAt } from '@/modules/root/helpers/user_root.helper';
import { updateLastActivityAt } from '@/modules/user/helpers/user.helper';

// Clave secreta con el que se firma el token
const key = new TextEncoder().encode(process.env.JWT_SECRET);

/**
 * Middleware para verificar la autenticación mediante un token JWT.
 * @param req - Objeto de solicitud de Express.
 * @param res - Objeto de respuesta de Express.
 * @param next - Función para continuar con el siguiente middleware.
 */
const authorizationMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  // Ignorar el middleware si la ruta es /token/generate
  if (req.path === '/auth/token/generate') {
    next(); // Saltar la autenticación
    return;
  }

  // Permitir accesos GET a catálogos - Solo en modo desarrollo
  if (process.env.NODE_ENV === 'development' && req.method === 'GET' && req.path.startsWith('/catalogs/')) {
    next();
    return;
  }

  // Obtener el token de autorización
  const jwt = req.headers.authorization;
  if (!jwt) {
    res.status(HttpStatus.UNAUTHORIZED).json({ message: 'Unauthorized' });
    next(new Error('Missing authorization header'));
    return;
  }

  try {
    // Verificar expiración del token
    const expires = decodeJwt(jwt);
    if (!expires.exp) {
      res.status(HttpStatus.UNAUTHORIZED).json({ success: false, message: 'Unauthorized' });
      next(new Error('Unable to verify token expiration'));
      return;
    }
    if (moment().unix() > expires.exp) {
      res.status(HttpStatus.UNAUTHORIZED).json({ success: false, message: 'Unauthorized' });
      next(new Error('Token has expired'));
      return;
    }

    // Verificar y decodificar el token
    const { payload } = await jwtVerify(jwt, key);

    // Obtener la informarción del token JWT
    const userId = payload.userId as string;
    const dependencyId = payload.dependencyId as string;
    const role = payload.role as string;

    // Verificar si los headers están presentes
    if (!userId || !dependencyId) {
      res.status(HttpStatus.BAD_REQUEST).json({ success: false, message: 'Missing responsibility headers in token' });
      next(new Error('Missing responsibility headers in token'));
      return;
    }

    // Validar que el usuario exista
    const user = await db
      .select({ value: count() })
      .from(systemModuleUser)
      .leftJoin(systemModuleUserKardexEmployment, eq(systemModuleUser.id, systemModuleUserKardexEmployment.fkUserId))
      .where(and(eq(systemModuleUser.id, userId), eq(systemModuleUserKardexEmployment.fkInstitutionId, dependencyId)))
      .limit(1);

    // Verificar si el usuario existe
    if (user[0].value === 0) {
      // Obtener la información del usuario y la dependencia root
      const userRoot = await db
        .select({ value: count() })
        .from(systemModuleRootUser)
        .where(and(eq(systemModuleRootUser.id, userId), eq(systemModuleRootUser.fkInstitutionId, dependencyId)))
        .limit(1);

      // Verificar si el usuario existe
      if (userRoot[0].value === 0) {
        res.status(HttpStatus.BAD_REQUEST).json({ success: false, message: 'Unauthorized user' });
        next(new Error('Unauthorized user'));
        return;
      }
      // Actualizar la última actividad del usuario root
      updateRootLastActivityAt(userId, dependencyId).catch((err) => {
        const { message, stack } = err as Error;
        Logger.getInstance().error(message, {
          method: 'authentcationMiddleware - updateRootLastActivityAt',
          module: 'middleware',
          source: req.headers['user-agent'],
          stack: stack,
        });
      });
    } else {
      // Actualizar la última actividad del usuario
      updateLastActivityAt(userId, dependencyId).catch((err) => {
        const { message, stack } = err as Error;
        Logger.getInstance().error(message, {
          method: 'authentcationMiddleware - updateLastActivityAt',
          module: 'middleware',
          source: req.headers['user-agent'],
          stack: stack,
        });
      });
    }

    // Adjuntar la información al objeto 'req' para ser usado en las rutas
    req.userId = userId;
    req.dependencyId = dependencyId;
    req.role = role;

    // Pasar al siguiente middleware
    next();
  } catch (err) {
    const { message, stack } = err as Error;
    Logger.getInstance().error(message, {
      method: 'authentcationMiddleware',
      module: 'middleware',
      source: req.headers['user-agent'],
      stack: stack,
    });
    res.status(HttpStatus.UNAUTHORIZED).json({ message: 'Unauthorized' });
    next(err);
    return;
  }
};

export default authorizationMiddleware;
