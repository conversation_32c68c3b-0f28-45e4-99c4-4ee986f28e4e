meta {
  name: getAll
  type: http
  seq: 1
}

get {
  url: {{base}}/kardex/employment?deleted=false&disabled=true&locale=es&grouped=true&groupedBy=job_position | direction | leadership | work_area | job_status | secondment | institution | shift | federal_entity | boss
  body: none
  auth: none
}

params:query {
  deleted: false
  disabled: true
  locale: es
  grouped: true
  groupedBy: job_position | direction | leadership | work_area | job_status | secondment | institution | shift | federal_entity | boss
}
