meta {
  name: getAll
  type: http
  seq: 1
}

get {
  url: {{cad}}/alert/event?deleted=false&disabled=true&withActivities=true&withStatus=new&withStatus=in_attention&grouped=false&groupedBy=incident_type | report_user | informant_user | alert_type | priority_type | status
  body: none
  auth: none
}

params:query {
  deleted: false
  disabled: true
  withActivities: true
  withStatus: new
  withStatus: in_attention
  grouped: false
  groupedBy: incident_type | report_user | informant_user | alert_type | priority_type | status
  ~locale: es
}
