import type { Request, Response } from 'express';
import { HttpStatus } from '@/utils/http_status';
import { getLocale, type Locale } from '@/utils/locale_validator';
import { Logger } from '@/utils/logger';
import { ModuleName } from '@/utils/module_names';
import db from '@/db';
import {
  systemModuleControlIphDetained,
  systemModuleControlCitizen,
  systemModuleUser,
  catDocumentType,
  catExternalInstitution,
  catGender,
  catMaritalStatus,
  catMunicipality,
  catNationality,
  catPoliceJob,
  catSecondment,
  catSettlement,
  catState,
  systemModuleControlPlaces,
  systemModuleControlIphFiles,
} from '@repo/shared-drizzle/schemas';
import { aliasedTable, and, eq, type SQL } from 'drizzle-orm';
import { sql } from 'drizzle-orm';
import { jsonbBuildObject } from '@/utils/json_build_object';

function query(options: { locale: Locale; id: string; all: true }): Promise<unknown[]>;
function query(options: { locale: Locale; id: string; all?: false }): Promise<unknown>;

async function query({ locale, id, all }: { locale: Locale; id: string; all?: boolean }): Promise<unknown | unknown[]> {
  const firstRespondentCitizen = aliasedTable(systemModuleControlCitizen, 'firstRespondentCitizen');
  // detained data
  const detainedCitizen = aliasedTable(systemModuleControlCitizen, 'detainedCitizen');
  const detainedNationality = aliasedTable(catNationality, 'detainedNationality');
  const detainedDocumentType = aliasedTable(catDocumentType, 'detainedDocumentType');
  const detainedMaritalStatus = aliasedTable(catMaritalStatus, 'detainedMaritalStatus');
  const detainedGender = aliasedTable(catGender, 'detainedGender');
  const detentionPlace = aliasedTable(systemModuleControlPlaces, 'detentionPlace');
  const detentionPlaceState = aliasedTable(catState, 'detentionPlaceState');
  const detentionPlaceMunicipality = aliasedTable(catMunicipality, 'detentionPlaceMunicipality');
  const detentionPlaceSettlement = aliasedTable(catSettlement, 'detentionPlaceSettlement');
  // trusted person data
  const trustedPersonCitizen = aliasedTable(systemModuleControlCitizen, 'trustedPersonCitizen');
  const trustedPersonNationality = aliasedTable(catNationality, 'trustedPersonNationality');
  const trustedPersonDocumentType = aliasedTable(catDocumentType, 'trustedPersonDocumentType');
  const trustedPersonMaritalStatus = aliasedTable(catMaritalStatus, 'trustedPersonMaritalStatus');
  const trustedPersonGender = aliasedTable(catGender, 'trustedPersonGender');
  const trustedPlace = aliasedTable(systemModuleControlPlaces, 'trustedPlace');
  const trustedPlaceState = aliasedTable(catState, 'trustedPlaceState');
  const trustedPlaceMunicipality = aliasedTable(catMunicipality, 'trustedPlaceMunicipality');
  const trustedPlaceSettlement = aliasedTable(catSettlement, 'trustedPlaceSettlement');

  const detaineesFilter: SQL[] = [eq(systemModuleControlIphDetained.isDeleted, false)];
  const filesFilter: SQL[] = [eq(systemModuleControlIphFiles.isDeleted, false)];
  if (all) {
    detaineesFilter.push(eq(systemModuleControlIphDetained.fkIphId, id));
    filesFilter.push(eq(systemModuleControlIphFiles.fkIphId, id));
  } else if (id) {
    detaineesFilter.push(eq(systemModuleControlIphDetained.id, id));
    filesFilter.push(eq(systemModuleControlIphFiles.annexId, id));
  }
  const s3Url = sql.raw(`${[process.env.AWS_S3_ENDPOINT, process.env.AWS_S3_BUCKET].join('/')}/`);
  const filesSubquery = db
    .select({
      fkAnnexId: systemModuleControlIphFiles.annexId,
      files: sql`json_agg(${jsonbBuildObject({
        id: systemModuleControlIphFiles.id,
        url: sql`concat('${s3Url}',${systemModuleControlIphFiles.path})`,
        name: systemModuleControlIphFiles.name,
        contentType: systemModuleControlIphFiles.contentType,
        type: systemModuleControlIphFiles.type,
        number: systemModuleControlIphFiles.number,
      })})`.as('files'),
    })
    .from(systemModuleControlIphFiles)
    .where(and(...filesFilter))
    .groupBy(systemModuleControlIphFiles.annexId)
    .as('files_subquery');

  const res = db
    .select({
      id: systemModuleControlIphDetained.id,
      iphId: systemModuleControlIphDetained.fkIphId,
      detainedAt: systemModuleControlIphDetained.detainedAt,
      number: systemModuleControlIphDetained.number,
      detentionNumber: systemModuleControlIphDetained.detentionNumber,
      description: systemModuleControlIphDetained.description,
      hasVisibleInjuries: systemModuleControlIphDetained.hasVisibleInjuries,
      visibleInjuriesDescription: systemModuleControlIphDetained.visibleInjuriesDescription,
      hasReportedIllness: systemModuleControlIphDetained.hasReportedIllness,
      reportedIllnessDescription: systemModuleControlIphDetained.reportedIllnessDescription,
      isInVulnerableGroup: systemModuleControlIphDetained.isInVulnerableGroup,
      vulnerableGroupDescription: systemModuleControlIphDetained.vulnerableGroupDescription,
      isInDelinquentGroup: systemModuleControlIphDetained.isInDelinquentGroup,
      delinquentGroupDescription: systemModuleControlIphDetained.delinquentGroupDescription,
      hasTrustedPerson: systemModuleControlIphDetained.hasTrustedPerson,
      wasInformedOfRights: systemModuleControlIphDetained.wasInformedOfRights,
      wasObjectRelatedToIncidentFound: systemModuleControlIphDetained.wasObjectRelatedToIncidentFound,
      wasBelongingsCollected: systemModuleControlIphDetained.wasBelongingsCollected,
      belongings: systemModuleControlIphDetained.belongings,
      isDetentionPlaceSameAsInterventionPlace: systemModuleControlIphDetained.isDetentionPlaceSameAsInterventionPlace,
      observations: systemModuleControlIphDetained.observations,
      transferExternalInstitution: sql`json_build_object('id', ${catExternalInstitution.id}, 'name', ${catExternalInstitution[locale]})`,
      detentionAddress: {
        id: systemModuleControlPlaces.id,
        state: sql`json_build_object('id', ${catState.id}, 'name', ${catState[locale]})`,
        municipality: sql`json_build_object('id', ${catMunicipality.id}, 'name', ${catMunicipality[locale]})`,
        colony: sql`json_build_object('id', ${catSettlement.id}, 'name', ${catSettlement[locale]})`,
        name: systemModuleControlPlaces.name,
        type: systemModuleControlPlaces.type,
        zipCode: systemModuleControlPlaces.zipCode,
        street: systemModuleControlPlaces.street,
        number: systemModuleControlPlaces.number,
        interiorNumber: systemModuleControlPlaces.interiorNumber,
        betweenStreet1: systemModuleControlPlaces.betweenStreet1,
        betweenStreet2: systemModuleControlPlaces.betweenStreet2,
        reference: systemModuleControlPlaces.reference,
        location: systemModuleControlPlaces.location,
        sectors: systemModuleControlPlaces.sectors,
      },
      firstRespondent: {
        id: systemModuleUser.id,
        name: sql`concat(${firstRespondentCitizen.name}, ' ', ${firstRespondentCitizen.firstSurname}, ' ', ${firstRespondentCitizen.secondSurname})`,
        position: sql`json_build_object('id', ${catPoliceJob.id}, 'name', ${catPoliceJob[locale]})`,
        secondment: sql`json_build_object('id', ${catSecondment.id}, 'name', ${catSecondment[locale]})`,
      },
      detainedData: {
        id: detainedCitizen.id,
        name: detainedCitizen.name,
        firstSurname: detainedCitizen.firstSurname,
        secondSurname: detainedCitizen.secondSurname,
        alias: detainedCitizen.alias,
        gender: sql`json_build_object('id', ${detainedGender.id}, 'name', ${detainedGender[locale]})`,
        bornAt: detainedCitizen.bornAt,
        phone: detainedCitizen.phone,
        email: detainedCitizen.email,
        documentIdentificationNumber: detainedCitizen.documentIdentificationNumber,
        emergencyContactId: detainedCitizen.fkEmercencyContactCitizenId,
        nationality: sql`json_build_object('id', ${detainedNationality.id}, 'name', ${detainedNationality[locale]})`,
        documentType: sql`json_build_object('id', ${detainedDocumentType.id}, 'name', ${detainedDocumentType[locale]})`,
        maritalStatus: sql`json_build_object('id', ${detainedMaritalStatus.id}, 'name', ${detainedMaritalStatus[locale]})`,
        curp: detainedCitizen.curp,
        rfc: detainedCitizen.rfc,
        occupation: detainedCitizen.occupation,
        address: sql`json_build_object(
          'id', ${detentionPlace.id},
          'state', json_build_object('id', ${detentionPlaceState.id}, 'name', ${detentionPlaceState[locale]}),
          'municipality', json_build_object('id', ${detentionPlaceMunicipality.id}, 'name', ${detentionPlaceMunicipality[locale]}),
          'colony', json_build_object('id', ${detentionPlaceSettlement.id}, 'name', ${detentionPlaceSettlement[locale]}),
          'name', ${detentionPlace.name},
          'type', ${detentionPlace.type},
          'zipCode', ${detentionPlace.zipCode},
          'street', ${detentionPlace.street},
          'number', ${detentionPlace.number},
          'interiorNumber', ${detentionPlace.interiorNumber},
          'betweenStreet1', ${detentionPlace.betweenStreet1},
          'betweenStreet2', ${detentionPlace.betweenStreet2},
          'reference', ${detentionPlace.reference},
          'location', ${detentionPlace.location},
          'sectors', ${detentionPlace.sectors}
        )`,
      },
      trustedPerson: {
        id: trustedPersonCitizen.id,
        name: trustedPersonCitizen.name,
        firstSurname: trustedPersonCitizen.firstSurname,
        secondSurname: trustedPersonCitizen.secondSurname,
        alias: trustedPersonCitizen.alias,
        gender: sql`json_build_object('id', ${trustedPersonGender.id}, 'name', ${trustedPersonGender[locale]})`,
        bornAt: trustedPersonCitizen.bornAt,
        phone: trustedPersonCitizen.phone,
        email: trustedPersonCitizen.email,
        documentIdentificationNumber: trustedPersonCitizen.documentIdentificationNumber,
        emergencyContactId: trustedPersonCitizen.fkEmercencyContactCitizenId,
        nationality: sql`json_build_object('id', ${trustedPersonNationality.id}, 'name', ${trustedPersonNationality[locale]})`,
        documentType: sql`json_build_object('id', ${trustedPersonDocumentType.id}, 'name', ${trustedPersonDocumentType[locale]})`,
        maritalStatus: sql`json_build_object('id', ${trustedPersonMaritalStatus.id}, 'name', ${trustedPersonMaritalStatus[locale]})`,
        curp: trustedPersonCitizen.curp,
        rfc: trustedPersonCitizen.rfc,
        occupation: trustedPersonCitizen.occupation,
        address: sql`json_build_object(
          'id', ${trustedPlace.id},
          'state', json_build_object('id', ${trustedPlaceState.id}, 'name', ${trustedPlaceState[locale]}),
          'municipality', json_build_object('id', ${trustedPlaceMunicipality.id}, 'name', ${trustedPlaceMunicipality[locale]}),
          'colony', json_build_object('id', ${trustedPlaceSettlement.id}, 'name', ${trustedPlaceSettlement[locale]}),
          'name', ${trustedPlace.name},
          'type', ${trustedPlace.type},
          'zipCode', ${trustedPlace.zipCode},
          'street', ${trustedPlace.street},
          'number', ${trustedPlace.number},
          'interiorNumber', ${trustedPlace.interiorNumber},
          'betweenStreet1', ${trustedPlace.betweenStreet1},
          'betweenStreet2', ${trustedPlace.betweenStreet2},
          'reference', ${trustedPlace.reference},
          'location', ${trustedPlace.location},
          'sectors', ${trustedPlace.sectors}
        )`,
      },
      files: filesSubquery.files,
      updatedAt: systemModuleControlIphDetained.updatedAt,
      createdAt: systemModuleControlIphDetained.createdAt,
    })
    .from(systemModuleControlIphDetained)
    .leftJoin(
      catExternalInstitution,
      eq(catExternalInstitution.id, systemModuleControlIphDetained.fkTransferExternalInstitutionId),
    )
    // first responder
    .leftJoin(systemModuleUser, eq(systemModuleUser.id, systemModuleControlIphDetained.fkFirstRespondentUserId))
    .leftJoin(firstRespondentCitizen, eq(firstRespondentCitizen.id, systemModuleUser.fkCitizenId))
    .leftJoin(catPoliceJob, eq(catPoliceJob.id, systemModuleControlIphDetained.fkFirstRespondentPositionId))
    .leftJoin(catSecondment, eq(catSecondment.id, systemModuleControlIphDetained.fkFirstRespondentSecondmentId))
    // detention place
    .leftJoin(
      systemModuleControlPlaces,
      eq(systemModuleControlPlaces.id, systemModuleControlIphDetained.fkDetentionAddressPlaceId),
    )
    .leftJoin(catState, eq(catState.id, systemModuleControlPlaces.fkStateId))
    .leftJoin(catMunicipality, eq(catMunicipality.id, systemModuleControlPlaces.fkMunicipalityId))
    .leftJoin(catSettlement, eq(catSettlement.id, systemModuleControlPlaces.fkColonyId))
    // detained data
    .leftJoin(detainedCitizen, eq(detainedCitizen.id, systemModuleControlIphDetained.fkDetainedCitizenId))
    .leftJoin(detainedNationality, eq(detainedNationality.id, detainedCitizen.fkNationalityId))
    .leftJoin(detainedDocumentType, eq(detainedDocumentType.id, detainedCitizen.fkDocumentTypeId))
    .leftJoin(detainedMaritalStatus, eq(detainedMaritalStatus.id, detainedCitizen.fkMaritalStatusId))
    .leftJoin(detainedGender, eq(detainedGender.id, detainedCitizen.fkGenderId))
    .leftJoin(detentionPlace, eq(detentionPlace.id, detainedCitizen.fkAddressPlaceId))
    .leftJoin(detentionPlaceState, eq(detentionPlaceState.id, detentionPlace.fkStateId))
    .leftJoin(detentionPlaceMunicipality, eq(detentionPlaceMunicipality.id, detentionPlace.fkMunicipalityId))
    .leftJoin(detentionPlaceSettlement, eq(detentionPlaceSettlement.id, detentionPlace.fkColonyId))
    // trusted person
    .leftJoin(trustedPersonCitizen, eq(trustedPersonCitizen.id, systemModuleControlIphDetained.fkDetainedCitizenId))
    .leftJoin(trustedPersonNationality, eq(trustedPersonNationality.id, trustedPersonCitizen.fkNationalityId))
    .leftJoin(trustedPersonDocumentType, eq(trustedPersonDocumentType.id, trustedPersonCitizen.fkDocumentTypeId))
    .leftJoin(trustedPersonMaritalStatus, eq(trustedPersonMaritalStatus.id, trustedPersonCitizen.fkMaritalStatusId))
    .leftJoin(trustedPersonGender, eq(trustedPersonGender.id, trustedPersonCitizen.fkGenderId))
    .leftJoin(trustedPlace, eq(trustedPlace.id, trustedPersonCitizen.fkAddressPlaceId))
    .leftJoin(trustedPlaceState, eq(trustedPlaceState.id, trustedPlace.fkStateId))
    .leftJoin(trustedPlaceMunicipality, eq(trustedPlaceMunicipality.id, trustedPlace.fkMunicipalityId))
    .leftJoin(trustedPlaceSettlement, eq(trustedPlaceSettlement.id, trustedPlace.fkColonyId))
    .leftJoin(filesSubquery, eq(filesSubquery.fkAnnexId, systemModuleControlIphDetained.id))
    .where(and(...detaineesFilter));

  const data = await res;
  if (data.length === 0) null;
  if (all) return data;
  return data[0];
}

export const get = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale: Locale = getLocale();
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const queryResult = await query({ locale, id });
    if (queryResult === null) {
      res.status(HttpStatus.NOT_FOUND).json({ message: 'Not found' });
      return;
    }
    res.status(HttpStatus.OK).json(queryResult);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      module: ModuleName.IPH,
      source: req.headers['user-agent'],
      method: '/iph/section/detainees/get/:id - get',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message: 'Internal server error' });
  }
};

export const getAll = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale: Locale = getLocale();
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const queryResult = await query({ locale, id, all: true });
    if (queryResult === null) {
      res.status(HttpStatus.NOT_FOUND).json({ message: 'Not found' });
      return;
    }
    res.status(HttpStatus.OK).json(queryResult);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      module: ModuleName.IPH,
      source: req.headers['user-agent'],
      method: '/iph/annex/detainees/get/all/:id - getAll',
    });
  }
};
