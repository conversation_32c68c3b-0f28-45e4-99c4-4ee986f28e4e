import type { Request, Response } from 'express';
import {
  catWeaponCaliber,
  systemModuleControlAmmunition,
  systemModuleControlAmmunitionUse,
  systemModuleControlWeapon,
  systemModuleUser,
} from '@repo/shared-drizzle/schemas';
import db from '@/db';
import { and, asc, eq, gt, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { getLocale } from '@/utils/locale_validator';
import { HttpStatus } from '@/utils/http_status';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import { DefaultValues } from '@/utils/default_values';
import { ammunitionUseBodySchema } from '../helpers/ammunition_use.helper';

type AmmunitionUseGrouped = Record<
  string,
  {
    fkUserId: string;
    aliasUser: string;
    data: {
      id: string;
      fkAmmunitionId: string;
      fkWeaponId: string;
      serialNumber: string;
      fkCaliberId: string;
      nameCaliber: string;
      quantity: number;
      reasonsForUse: string;
      fkFatigueId: string | null;
      fkIphId: string | null;
    }[];
  }
>;

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS, grouped: groupedQS } = req.query;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let grouped = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlAmmunitionUse.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlAmmunitionUse.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlAmmunitionUse.id,
        fkUserId: systemModuleControlAmmunitionUse.fkUserId,
        aliasUser: systemModuleUser.alias,
        fkAmmunitionId: systemModuleControlAmmunitionUse.fkAmmunitionId,
        fkWeaponId: systemModuleControlAmmunitionUse.fkWeaponId,
        serialNumber: systemModuleControlWeapon.serialNumber,
        fkCaliberId: systemModuleControlAmmunitionUse.fkCaliberId,
        nameCaliber: catWeaponCaliber[locale],
        quantity: systemModuleControlAmmunitionUse.quantity,
        reasonsForUse: systemModuleControlAmmunitionUse.reasonsForUse,
        fkFatigueId: systemModuleControlAmmunitionUse.fkFatigueId,
        fkIphId: systemModuleControlAmmunitionUse.fkIphId,
      })
      .from(systemModuleControlAmmunitionUse)
      .leftJoin(systemModuleUser, eq(systemModuleControlAmmunitionUse.fkUserId, systemModuleUser.id))
      .leftJoin(
        systemModuleControlWeapon,
        eq(systemModuleControlAmmunitionUse.fkWeaponId, systemModuleControlWeapon.id),
      )
      .leftJoin(catWeaponCaliber, eq(systemModuleControlAmmunitionUse.fkCaliberId, catWeaponCaliber.id))
      .where(and(...filters));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, aliasUser, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            aliasUser: aliasUser || DefaultValues.DEFAULT_CONTENT,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkAmmunitionId: rest.fkAmmunitionId,
            fkWeaponId: rest.fkWeaponId,
            serialNumber: rest.serialNumber || DefaultValues.DEFAULT_CONTENT,
            fkCaliberId: rest.fkCaliberId,
            nameCaliber: rest.nameCaliber || DefaultValues.DEFAULT_CONTENT,
            quantity: rest.quantity,
            reasonsForUse: rest.reasonsForUse,
            fkFatigueId: rest.fkFatigueId,
            fkIphId: rest.fkIphId,
          });
        }
        return acc;
      }, {} as AmmunitionUseGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.AMMUNITION_USE}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const result = await db
      .select({
        id: systemModuleControlAmmunitionUse.id,
        fkUserId: systemModuleControlAmmunitionUse.fkUserId,
        aliasUser: systemModuleUser.alias,
        fkAmmunitionId: systemModuleControlAmmunitionUse.fkAmmunitionId,
        fkWeaponId: systemModuleControlAmmunitionUse.fkWeaponId,
        serialNumber: systemModuleControlWeapon.serialNumber,
        fkCaliberId: systemModuleControlAmmunitionUse.fkCaliberId,
        nameCaliber: catWeaponCaliber[locale],
        quantity: systemModuleControlAmmunitionUse.quantity,
        reasonsForUse: systemModuleControlAmmunitionUse.reasonsForUse,
        fkFatigueId: systemModuleControlAmmunitionUse.fkFatigueId,
        fkIphId: systemModuleControlAmmunitionUse.fkIphId,
      })
      .from(systemModuleControlAmmunitionUse)
      .leftJoin(systemModuleUser, eq(systemModuleControlAmmunitionUse.fkUserId, systemModuleUser.id))
      .leftJoin(
        systemModuleControlWeapon,
        eq(systemModuleControlAmmunitionUse.fkWeaponId, systemModuleControlWeapon.id),
      )
      .leftJoin(catWeaponCaliber, eq(systemModuleControlAmmunitionUse.fkCaliberId, catWeaponCaliber.id))
      .where(eq(systemModuleControlAmmunitionUse.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.AMMUNITION_USE}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const insertData = ammunitionUseBodySchema.parse({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const result = await db.insert(systemModuleControlAmmunitionUse).values({ ...insertData });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.AMMUNITION_USE} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;
    // Validar payload
    const updateData = ammunitionUseBodySchema.parse({ ...req.body });
    const result = await db.transaction(async (trx) => {
      // Validaciones en paralelo
      const [existingAmmunitionUse, existingAmmunition, existingWeapon, existingCaliber] = await Promise.all([
        trx.select().from(systemModuleControlAmmunitionUse).where(eq(systemModuleControlAmmunitionUse.id, id)).limit(1),
        trx
          .select()
          .from(systemModuleControlAmmunition)
          .where(eq(systemModuleControlAmmunition.id, updateData.fkAmmunitionId))
          .limit(1),
        trx
          .select()
          .from(systemModuleControlWeapon)
          .where(eq(systemModuleControlWeapon.id, updateData.fkWeaponId))
          .limit(1),
        trx.select().from(catWeaponCaliber).where(eq(catWeaponCaliber.id, updateData.fkCaliberId)).limit(1),
      ]);

      if (existingAmmunitionUse.length === 0) throw new Error('El registro de uso de munición no existe');
      if (existingAmmunition.length === 0) throw new Error('La munición no existe');
      if (existingWeapon.length === 0) throw new Error('La arma no existe');
      if (existingCaliber.length === 0) throw new Error('El calibre de la arma no existe');

      const updateResult = await trx
        .update(systemModuleControlAmmunitionUse)
        .set({ ...updateData })
        .where(eq(systemModuleControlAmmunitionUse.id, id));

      return updateResult;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.AMMUNITION_USE}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      const existingAmmunitionUse = await trx
        .select()
        .from(systemModuleControlAmmunitionUse)
        .where(eq(systemModuleControlAmmunitionUse.id, id))
        .limit(1);

      if (existingAmmunitionUse.length === 0) throw new Error('El registro de uso de munición no existe');

      const deleteResult = await trx
        .update(systemModuleControlAmmunitionUse)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleControlAmmunitionUse.id, id));

      return deleteResult;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.AMMUNITION_USE}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    locale: localeQS,
    q: qQS,
    grouped: groupedQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let q = null;
  let grouped = false;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlAmmunitionUse.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlAmmunitionUse.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleUser.alias, `%${q}%`));
      likeFilters.push(like(systemModuleControlWeapon.serialNumber, `%${q}%`));
      likeFilters.push(like(catWeaponCaliber[locale], `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlAmmunitionUse.quantity}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleControlAmmunitionUse.reasonsForUse, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleControlAmmunitionUse.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlAmmunitionUse.id,
        fkUserId: systemModuleControlAmmunitionUse.fkUserId,
        aliasUser: systemModuleUser.alias,
        fkAmmunitionId: systemModuleControlAmmunitionUse.fkAmmunitionId,
        fkWeaponId: systemModuleControlAmmunitionUse.fkWeaponId,
        serialNumber: systemModuleControlWeapon.serialNumber,
        fkCaliberId: systemModuleControlAmmunitionUse.fkCaliberId,
        nameCaliber: catWeaponCaliber[locale],
        quantity: systemModuleControlAmmunitionUse.quantity,
        reasonsForUse: systemModuleControlAmmunitionUse.reasonsForUse,
        fkFatigueId: systemModuleControlAmmunitionUse.fkFatigueId,
        fkIphId: systemModuleControlAmmunitionUse.fkIphId,
      })
      .from(systemModuleControlAmmunitionUse)
      .leftJoin(systemModuleUser, eq(systemModuleControlAmmunitionUse.fkUserId, systemModuleUser.id))
      .leftJoin(
        systemModuleControlWeapon,
        eq(systemModuleControlAmmunitionUse.fkWeaponId, systemModuleControlWeapon.id),
      )
      .leftJoin(catWeaponCaliber, eq(systemModuleControlAmmunitionUse.fkCaliberId, catWeaponCaliber.id))
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlAmmunitionUse.id));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, aliasUser, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            aliasUser: aliasUser || DefaultValues.DEFAULT_CONTENT,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkAmmunitionId: rest.fkAmmunitionId,
            fkWeaponId: rest.fkWeaponId,
            serialNumber: rest.serialNumber || DefaultValues.DEFAULT_CONTENT,
            fkCaliberId: rest.fkCaliberId,
            nameCaliber: rest.nameCaliber || DefaultValues.DEFAULT_CONTENT,
            quantity: rest.quantity,
            reasonsForUse: rest.reasonsForUse,
            fkFatigueId: rest.fkFatigueId,
            fkIphId: rest.fkIphId,
          });
        }
        return acc;
      }, {} as AmmunitionUseGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.AMMUNITION_USE} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
