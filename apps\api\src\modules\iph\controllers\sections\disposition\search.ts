import type { Request, Response } from 'express';
import { getLocale } from '@/utils/locale_validator';
import { Logger } from '@/utils/logger';
import { ModuleName } from '@/utils/module_names';
import { HttpStatus } from '@/utils/http_status';
import db from '@/db';
import {
  catExternalInstitution,
  catPoliceJob,
  catSecondment,
  systemModuleControlCitizen,
  systemModuleControlIphDisposition,
  systemModuleUser,
} from '@repo/shared-drizzle/schemas';
import type { Locale } from '@/utils/locale_validator';
import { and, asc, eq, gt, sql, type SQL } from 'drizzle-orm';

export const query = ({
  locale,
  query,
  limit,
  lastId,
}: { locale: Locale; query?: string | null; limit: number; lastId?: string | null }) => {
  const filters: SQL[] = [];
  if (query) {
    // TODO: search by referenceNumber, fileNumber and status
    // filters.push(SQL`reference_number ilike ${query}`);
  }

  if (lastId) {
    filters.push(gt(systemModuleControlIphDisposition.id, lastId));
  }

  return db
    .select({
      id: systemModuleControlIphDisposition.id,
      dispositionAt: systemModuleControlIphDisposition.dispositionAt,
      fileNumber: systemModuleControlIphDisposition.fileNumber,
      firstRespondent: {
        id: systemModuleUser.id,
        name: sql`concat(${systemModuleControlCitizen.name}, ' ', ${systemModuleControlCitizen.firstSurname}, ' ', ${systemModuleControlCitizen.secondSurname})`,
        position: sql`json_build_object('id', ${catPoliceJob.id}, 'name', ${catPoliceJob[locale]})`,
        secondment: sql`json_build_object('id', ${catSecondment.id}, 'name', ${catSecondment[locale]})`,
      },
      dispositionAuthority: {
        name: systemModuleControlIphDisposition.authorityName,
        firstSurname: systemModuleControlIphDisposition.authorityFirstSurname,
        secondSurname: systemModuleControlIphDisposition.authoritySecondSurname,
        position: systemModuleControlIphDisposition.authorityPosition,
        secondment: systemModuleControlIphDisposition.authoritySecondment,
        authority: sql`json_build_object('id', ${systemModuleControlIphDisposition.fkAuthorityInstitutionId}, 'name', ${catExternalInstitution[locale]})`,
      },
    })
    .from(systemModuleControlIphDisposition)
    .leftJoin(
      catExternalInstitution,
      eq(catExternalInstitution.id, systemModuleControlIphDisposition.fkAuthorityInstitutionId),
    )
    .leftJoin(systemModuleUser, eq(systemModuleUser.id, systemModuleControlIphDisposition.fkFirstRespondentUserId))
    .leftJoin(systemModuleControlCitizen, eq(systemModuleControlCitizen.id, systemModuleUser.fkCitizenId))
    .leftJoin(catPoliceJob, eq(catPoliceJob.id, systemModuleControlIphDisposition.fkFirstRespondentPositionId))
    .leftJoin(catSecondment, eq(catSecondment.id, systemModuleControlIphDisposition.fkFirstRespondentSecondmentId))
    .where(and(...filters, eq(systemModuleControlIphDisposition.isDeleted, false)))
    .orderBy(asc(systemModuleControlIphDisposition.id))
    .limit(limit);
};

export const search = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, q: qQS, locale: localeQS } = req.query;

  let lastId = null;
  let limit = 10;
  let q = null;
  let locale: Locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  try {
    const queryResult = await query({ locale, limit, lastId, query: q });
    res.status(HttpStatus.OK).json(queryResult);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      module: ModuleName.IPH,
      method: '/iph/section/disposition/search - searchDisposition',
      source: req.headers['user-agent'],
      stack,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};
