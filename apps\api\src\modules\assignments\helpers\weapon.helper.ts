import {
  systemModuleAssignmentWeapon,
  systemModuleControlFatigue,
  systemModuleControlWeapon,
  systemModuleUser,
  systemModuleUserKardexArmament,
} from '@repo/shared-drizzle/schemas';
import { kardexArmamentBodySchema } from '@/modules/kardex/helpers/kardex_armament.helper';
import type { KardexArmament } from '@/modules/kardex/interfaces/kardex_armament.interface';
import { and, desc, eq, inArray, isNull, ne, type ExtractTablesWithRelations } from 'drizzle-orm';
import type { NodePgQueryResultHKT } from 'drizzle-orm/node-postgres';
import type { PgTransaction } from 'drizzle-orm/pg-core';
import moment from 'moment';
import { z } from 'zod';

export const assignWeaponBodySchema = z.object({
  fkUserId: z.string(),
  fkControlWeaponId: z.string(),
  fkFatigueId: z.string(),
  assignmentDate: z.string(),
  assignmentEndDate: z.string().nullable().optional(),
  comments: z.string().nullable().optional(),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});

export const registerWeaponInKardex = async (
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, never>, ExtractTablesWithRelations<Record<string, never>>>,
  records: KardexArmament[],
  fkLastUserId: string,
  fkLastDependencyId: string,
) => {
  // Validar que haya registros para procesar
  if (!records || records.length === 0) {
    throw new Error('No se proporcionaron registros para procesar');
  }

  // Obtener todos los IDs de usuarios y tipos de armamento para validación
  const fkUserIds = [...new Set(records.map((r) => r.fkUserId))];
  const serialNumbers = [...new Set(records.map((r) => r.serialNumber))];

  // Validar existencia de usuarios y tipos de armamento
  const [existingUsers, existingWeapons] = await Promise.all([
    trx.select({ id: systemModuleUser.id }).from(systemModuleUser).where(inArray(systemModuleUser.id, fkUserIds)),
    trx
      .select({
        id: systemModuleControlWeapon.id,
        serialNumber: systemModuleControlWeapon.serialNumber,
      })
      .from(systemModuleControlWeapon)
      .where(inArray(systemModuleControlWeapon.serialNumber, serialNumbers)),
  ]);

  // Verificar que todos existen
  const missingUsers = fkUserIds.filter((id) => !existingUsers.some((u) => u.id === id));
  const missingWeapons = serialNumbers.filter((id) => !existingWeapons.some((w) => w.id === id));

  if (missingUsers.length > 0 || missingWeapons.length > 0) {
    const errors = [];
    if (missingUsers.length > 0) {
      errors.push(`Usuarios no encontrados: ${missingUsers.join(', ')}`);
    }
    if (missingWeapons.length > 0) {
      errors.push(`Armas no encontradas: ${missingWeapons.join(', ')}`);
    }
    throw new Error(errors.join('\n'));
  }

  // Procesar registros
  const result = [];
  const today = moment().format();
  for (const record of records) {
    // Buscar asignación activa actual (si existe)
    const activeAssignment = await trx
      .select()
      .from(systemModuleUserKardexArmament)
      .where(
        and(
          eq(systemModuleUserKardexArmament.fkUserId, record.fkUserId),
          isNull(systemModuleUserKardexArmament.unassignmentDate),
        ),
      )
      .orderBy(desc(systemModuleUserKardexArmament.assignmentDate))
      .limit(1);

    // Si existe una asignación activa actual con diferente número de serie, cerramos esa asignación
    if (activeAssignment.length > 0 && activeAssignment[0].serialNumber !== record.serialNumber) {
      await trx
        .update(systemModuleUserKardexArmament)
        .set({ unassignmentDate: today, fkLastUserId, fkLastDependencyId })
        .where(
          and(
            eq(systemModuleUserKardexArmament.fkUserId, record.fkUserId),
            eq(systemModuleUserKardexArmament.id, activeAssignment[0].id),
          ),
        );
      await trx
        .update(systemModuleControlWeapon)
        .set({ weaponStatus: 'inactive', fkLastUserId, fkLastDependencyId })
        .where(eq(systemModuleControlWeapon.serialNumber, activeAssignment[0].serialNumber));
    }

    // Crear asignación, solo si:
    // - No existe una asignación activa actual o
    // - Existe una asignación activa actual pero con diferente número de serie
    if (activeAssignment.length === 0 || activeAssignment[0].serialNumber !== record.serialNumber) {
      const insertArmamentKardex = await kardexArmamentBodySchema.parseAsync({
        fkUserId: record.fkUserId,
        fkWeaponTypeId: record.fkWeaponTypeId,
        serialNumber: record.serialNumber,
        assignmentDate: today,
        weaponStatus: 'active',
        fkLastUserId,
        fkLastDependencyId,
      });
      const insert = await trx.insert(systemModuleUserKardexArmament).values({
        ...insertArmamentKardex,
      });
      result.push(record);
      if (insert.rows.length > 0) {
        await trx
          .update(systemModuleControlWeapon)
          .set({ weaponStatus: 'active', fkLastUserId, fkLastDependencyId })
          .where(eq(systemModuleControlWeapon.serialNumber, record.serialNumber));
      }
    } else {
      const { fkLastUserId, fkLastDependencyId, isDeleted, isEnabled, deletedAt, ...rest } = activeAssignment[0];
      result.push(rest);
    }
  }
  return result;
};

export const validateWeaponAssignmentInFatigue = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkControlWeaponId: string,
  fkFatigueId: string,
  fkWeaponAssignmentId: string,
) => {
  const existingAssignment = await trx
    .select()
    .from(systemModuleAssignmentWeapon)
    .where(
      and(
        eq(systemModuleAssignmentWeapon.fkControlWeaponId, fkControlWeaponId),
        eq(systemModuleAssignmentWeapon.fkFatigueId, fkFatigueId),
        ne(systemModuleAssignmentWeapon.id, fkWeaponAssignmentId),
      ),
    )
    .limit(1);
  if (existingAssignment.length > 0) {
    throw new Error('Ya existe una asignación con la misma arma en el despliegue indicado');
  }
};

export const validateWeaponActiveAssignment = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkControlWeaponId: string,
  fkWeaponAssignmentId: string,
) => {
  const activeWeaponAssignment = await trx
    .select()
    .from(systemModuleAssignmentWeapon)
    .leftJoin(systemModuleControlFatigue, eq(systemModuleAssignmentWeapon.fkFatigueId, systemModuleControlFatigue.id))
    .where(
      and(
        eq(systemModuleControlFatigue.status, 'active'),
        eq(systemModuleAssignmentWeapon.fkControlWeaponId, fkControlWeaponId),
        eq(systemModuleAssignmentWeapon.isDeleted, false),
        ne(systemModuleAssignmentWeapon.id, fkWeaponAssignmentId),
      ),
    )
    .limit(1);
  if (activeWeaponAssignment.length > 0) {
    throw new Error('El arma esta asignada a otro elemento en un despliegue activo');
  }
};

export const handleUserChangeInWeaponAssign = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkUserId: string,
  fkControlWeaponId: string,
  fkFatigueId: string,
  fkWeaponAssignmentId: string,
) => {
  const existingAssignment = await trx
    .select()
    .from(systemModuleAssignmentWeapon)
    .where(
      and(
        eq(systemModuleAssignmentWeapon.fkUserId, fkUserId),
        eq(systemModuleAssignmentWeapon.fkControlWeaponId, fkControlWeaponId),
        eq(systemModuleAssignmentWeapon.fkFatigueId, fkFatigueId),
        ne(systemModuleAssignmentWeapon.id, fkWeaponAssignmentId), // Excluir la asignación actual
      ),
    )
    .limit(1);

  if (existingAssignment.length > 0) {
    throw new Error('El usuario ya tiene una asignación con esta arma en el mismo despliegue');
  }
};
