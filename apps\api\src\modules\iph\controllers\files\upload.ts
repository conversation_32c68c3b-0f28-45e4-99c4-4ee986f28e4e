import db from '@/db';
import { IPHFileTypes, systemModuleControlIphFiles } from '@repo/shared-drizzle/schemas';
import { s3Client } from '@/lib/s3.config';
import { HttpStatus } from '@/utils/http_status';
import { Logger } from '@/utils/logger';
import { ModuleName } from '@/utils/module_names';
import { PutObjectCommand } from '@aws-sdk/client-s3';
import { iphSectionValues } from '@repo/types/values';
import type { Request, Response } from 'express';
import { createHash } from 'node:crypto';
import { Stream } from 'node:stream';
import z from 'zod';
import { sql } from 'drizzle-orm';

export const upload = async (req: Request, res: Response) => {
  try {
    const contentType = req.headers['content-type'] || 'application/octet-stream';
    const contentLength = req.headers['content-length'];
    if (!contentLength) {
      res.status(HttpStatus.BAD_REQUEST).json({ success: false, message: 'Content-Length header is required' });
      return;
    }
    const {
      name: nameQS,
      iphId: iphIdQS,
      section: sectionQS,
      annexId: annexIdQS,
      type: typeQS,
      number: numberQS,
    } = req.query;
    const name = z
      .string()
      .regex(/^(.+)\.([^.]+)$/)
      .parse(nameQS as string);
    const iphId = z
      .string()
      .uuid()
      .parse(iphIdQS as string);
    const section = z.enum(iphSectionValues).parse(sectionQS as string);
    const annexId = z.string().uuid().nullable().optional().parse(annexIdQS);
    const type = z.enum(IPHFileTypes).nullable().optional().parse(typeQS);
    const number = z.number().int().nullable().optional().parse(numberQS);
    const key = ['iph', iphId, section, annexId, type, number, name].filter((e) => e).join('/');

    // if (contentType.startsWith('multipart/form-data')) {
    //   // get file
    // }
    const passThrough = new Stream.PassThrough();
    req.pipe(passThrough);

    const bucket = process.env.AWS_S3_BUCKET;

    const hash = createHash('sha256');

    req.on('data', (chunk) => {
      hash.update(chunk);
    });

    req.on('aborted', () => {
      console.error('Client disconnected before upload completed.');
      passThrough.destroy(new Error('Client disconnected')); // Stop processing
    });

    req.on('error', (err) => {
      console.error('Request stream error:', err);
      passThrough.destroy(err); // Ensure the stream stops
    });

    passThrough.on('error', (err) => {
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Error processing the request stream',
        error: err.message,
      });
    });

    const command = new PutObjectCommand({
      Bucket: bucket,
      Key: key,
      Body: passThrough,
      ContentType: contentType,
      ContentLength: +contentLength,
    });

    await s3Client.send(command);

    const sha256Hash = hash.digest('hex');

    const s3Url = sql.raw(`${[process.env.AWS_S3_ENDPOINT, process.env.AWS_S3_BUCKET].join('/')}/`);

    const data = await db
      .insert(systemModuleControlIphFiles)
      .values({
        contentType,
        fkIphId: iphId,
        type,
        number,
        section,
        annexId,
        path: key,
        name,
        hash: sha256Hash,
        fkLastDependencyId: req.dependencyId as string,
        fkLastUserId: req.userId as string,
      })
      .returning({
        id: systemModuleControlIphFiles.id,
        url: sql`concat('${s3Url}',${systemModuleControlIphFiles.path})`,
        name: systemModuleControlIphFiles.name,
        contentType: systemModuleControlIphFiles.contentType,
        type: systemModuleControlIphFiles.type,
        number: systemModuleControlIphFiles.number,
      });

    res.status(HttpStatus.CREATED).jsonp({ success: true, data });
  } catch (err) {
    const error = err as Error;
    Logger.getInstance().error(error.message, {
      module: ModuleName.IPH,
      method: 'iph/files - upload',
      source: req.headers['user-agent'],
      stack: error.stack,
    });
    if (err instanceof z.ZodError) {
      res.status(HttpStatus.BAD_REQUEST).json({ success: false, message: err.issues });
      return;
    }
    res.status(HttpStatus.BAD_REQUEST).json({ success: false, message: 'Internal server error' });
  }
};
