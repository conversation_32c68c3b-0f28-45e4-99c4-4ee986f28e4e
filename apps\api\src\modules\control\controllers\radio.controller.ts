import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, isNull, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { getLocale } from '@/utils/locale_validator';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';
import {
  catRadioBrand,
  catRadioModel,
  systemModuleAssignmentRadio,
  systemModuleControlRadio,
} from '@repo/shared-drizzle/schemas';
import { radioBodySchema } from '../helpers/radio.helper';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS } = req.query;
  let [deleted, disabled, locale] = [false, false, getLocale()];

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlRadio.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlRadio.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlRadio.id,
        serialNumber: systemModuleControlRadio.serialNumber,
        nationalRegistryNumber: systemModuleControlRadio.nationalRegistryNumber,
        fkRadioBrandId: systemModuleControlRadio.fkRadioBrandId,
        nameRadioBrand: catRadioBrand[locale],
        fkRadioModelId: systemModuleControlRadio.fkRadioModelId,
        nameRadioModel: catRadioModel[locale],
        status: systemModuleControlRadio.status,
        expirationDate: systemModuleControlRadio.expirationDate,
        acquisitionDate: systemModuleControlRadio.acquisitionDate,
        cost: systemModuleControlRadio.cost,
        description: systemModuleControlRadio.description,
      })
      .from(systemModuleControlRadio)
      .leftJoin(catRadioBrand, eq(systemModuleControlRadio.fkRadioBrandId, catRadioBrand.id))
      .leftJoin(catRadioModel, eq(systemModuleControlRadio.fkRadioModelId, catRadioModel.id))
      .where(and(...filters));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.RADIO}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const result = await db
      .select({
        id: systemModuleControlRadio.id,
        serialNumber: systemModuleControlRadio.serialNumber,
        nationalRegistryNumber: systemModuleControlRadio.nationalRegistryNumber,
        fkRadioBrandId: systemModuleControlRadio.fkRadioBrandId,
        nameRadioBrand: catRadioBrand[locale],
        fkRadioModelId: systemModuleControlRadio.fkRadioModelId,
        nameRadioModel: catRadioModel[locale],
        status: systemModuleControlRadio.status,
        expirationDate: systemModuleControlRadio.expirationDate,
        acquisitionDate: systemModuleControlRadio.acquisitionDate,
        cost: systemModuleControlRadio.cost,
        description: systemModuleControlRadio.description,
      })
      .from(systemModuleControlRadio)
      .leftJoin(catRadioBrand, eq(systemModuleControlRadio.fkRadioBrandId, catRadioBrand.id))
      .leftJoin(catRadioModel, eq(systemModuleControlRadio.fkRadioModelId, catRadioModel.id))
      .where(eq(systemModuleControlRadio.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.RADIO}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const insertRadioData = await radioBodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModuleVehicle = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_RADIOS);
    if (!systemModuleVehicle) {
      throw new Error('System module CTRL_RADIOS not found');
    }
    const result = await db.insert(systemModuleControlRadio).values({
      ...insertRadioData,
      fkSystemModuleId: systemModuleVehicle.id,
    });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.RADIO} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    // Validar payload
    const updatetRadioData = await radioBodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const result = await db.transaction(async (trx) => {
      // Obtener datos del radio
      const radio = await trx
        .select()
        .from(systemModuleControlRadio)
        .where(eq(systemModuleControlRadio.id, id))
        .limit(1);
      if (radio.length === 0) {
        throw new Error('Radio no encontrado');
      }

      // Actualizar el radio
      const updateResult = await trx
        .update(systemModuleControlRadio)
        .set({ ...updatetRadioData })
        .where(eq(systemModuleControlRadio.id, id));

      return updateResult;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.RADIO}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Verificar si el radio está asignado actualmente a un usuario
      const activeRadioAssignment = await trx
        .select()
        .from(systemModuleAssignmentRadio)
        .where(
          and(
            eq(systemModuleAssignmentRadio.fkControlRadioId, id),
            isNull(systemModuleAssignmentRadio.assignmentEndDate),
          ),
        )
        .limit(1);

      if (activeRadioAssignment.length > 0) {
        throw new Error('No se puede eliminar el radio porque está asignado actualmente a un elemento');
      }

      // Marcar el radio como eliminado
      const deleteResult = await trx
        .update(systemModuleControlRadio)
        .set({
          isDeleted: true,
          deletedAt: new Date(),
          fkLastUserId: req.userId,
          fkLastDependencyId: req.dependencyId,
        })
        .where(eq(systemModuleControlRadio.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.RADIO}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    locale: localeQS,
    q: qQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlRadio.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlRadio.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleControlRadio.serialNumber, `%${q}%`));
      likeFilters.push(like(systemModuleControlRadio.nationalRegistryNumber, `%${q}%`));
      likeFilters.push(like(catRadioBrand[locale], `%${q}%`));
      likeFilters.push(like(catRadioModel[locale], `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlRadio.expirationDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlRadio.acquisitionDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlRadio.cost}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleControlRadio.description, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleControlRadio.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlRadio.id,
        serialNumber: systemModuleControlRadio.serialNumber,
        nationalRegistryNumber: systemModuleControlRadio.nationalRegistryNumber,
        fkRadioBrandId: systemModuleControlRadio.fkRadioBrandId,
        nameRadioBrand: catRadioBrand[locale],
        fkRadioModelId: systemModuleControlRadio.fkRadioModelId,
        nameRadioModel: catRadioModel[locale],
        status: systemModuleControlRadio.status,
        expirationDate: systemModuleControlRadio.expirationDate,
        acquisitionDate: systemModuleControlRadio.acquisitionDate,
        cost: systemModuleControlRadio.cost,
        description: systemModuleControlRadio.description,
      })
      .from(systemModuleControlRadio)
      .leftJoin(catRadioBrand, eq(systemModuleControlRadio.fkRadioBrandId, catRadioBrand.id))
      .leftJoin(catRadioModel, eq(systemModuleControlRadio.fkRadioModelId, catRadioModel.id))
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlRadio.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.RADIO} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
