import db from '@/db';
import {
  systemModuleAssignmentShift,
  systemModuleAssignmentTacticalEquipment,
  systemModuleAssignmentUniform,
  systemModuleAssignmentWeapon,
  systemModuleControlFatigue,
  systemModuleControlUniform,
  systemModuleControlWeapon,
  systemModuleUser,
  systemModuleUserKardexArmament,
  systemModuleUserKardexEmployment,
  systemModuleUserKardexTacticalEquipment,
  type ShiftAssign,
} from '@repo/shared-drizzle/schemas';
import { assignShiftBodySchema } from '@/modules/assignments/helpers/shift.helper';
import type SystemModuleCache from '@/utils/system_module_cache';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { fatigueStatusValues } from '@repo/types/values';
import type { ExtractTablesWithRelations } from 'drizzle-orm';
import { and, eq, inArray } from 'drizzle-orm';
import type { NodePgQueryResultHKT } from 'drizzle-orm/node-postgres';
import type { PgTransaction } from 'drizzle-orm/pg-core';
import { z } from 'zod';
import type {
  FatigueShiftData,
  FatigueStatusData,
  KardexRecord,
  ShiftAssignMetadata,
  UsersByShiftType,
} from '../interfaces/fatigue.interface';
import moment from 'moment';
import type { KardexArmament } from '@/modules/kardex/interfaces/kardex_armament.interface';
import type { TacticalEquipmentStatuse, WeaponStatuse } from '@repo/types/types';
import type { KardexTacticalEquipment } from '@/modules/kardex/interfaces/kardex_tactical_equipment.interface';
import type { excludeFields } from '@/utils/default_values';
import {
  getIsClosedLabel,
  getShiftNamebyId,
  getStatusLabelbyValue,
  getUserNamebyId,
} from '@/utils/group_name_resolvers';

export const fatigueBodySchema = z.object({
  fkUserId: z.string(),
  fkShiftId: z.string(),
  fatigueDate: z.string(),
  status: z.enum(fatigueStatusValues),
  isClosed: z.boolean().default(false),
  closedAt: z.date().nullable().optional(),
  comments: z.string().nullable().optional(),
  isTemplate: z.boolean().default(false),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});

type FatigueAssignGroupedByStatus = Record<
  string,
  {
    status: string;
    fkUserId: string;
    aliasUser: string | null;
    data: FatigueStatusData[];
  }
>;

type FatigueAssignGroupedByShift = Record<
  string,
  {
    fkShiftId: string;
    shiftName: string | null;
    fkUserId: string;
    aliasUser: string | null;
    data: FatigueShiftData[];
  }
>;

export type AssignedUser = {
  fkUserId: number;
  checkInTime: string | null;
  isLate: boolean;
  isDenied: boolean;
  denialReason: string | null;
  status: string;
};

export const replaceFatigueGroupByField = {
  status: 'status',
  shift: 'fkShiftId',
  user: 'fkUserId',
  closed: 'isClosed',
};

const GROUP_BY_FIELDS = {
  status: systemModuleControlFatigue.status,
  fkShiftId: systemModuleControlFatigue.fkShiftId,
  fkUserId: systemModuleControlFatigue.fkUserId,
  isClosed: systemModuleControlFatigue.isClosed,
} as const;

type GroupByField = keyof typeof GROUP_BY_FIELDS;
export type ReplaceFatigueGroup = keyof typeof replaceFatigueGroupByField;

const GROUP_BY_KEYS = Object.keys(GROUP_BY_FIELDS) as GroupByField[];

export const parseFatigueGroupByParam = (param: unknown): GroupByField | null => {
  const replace = replaceFatigueGroupByField[param as ReplaceFatigueGroup];
  return typeof replace === 'string' && GROUP_BY_KEYS.includes(replace as GroupByField)
    ? (replace as GroupByField)
    : null;
};

export const groupFatigueNameResolvers: Record<string, (key: string) => Promise<string | null>> = {
  fkUserId: getUserNamebyId,
  fkShiftId: getShiftNamebyId,
  status: getStatusLabelbyValue,
  isClosed: async (v) => getIsClosedLabel(v === 'true'),
};

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
export const groupByStatus = (result: any[]): FatigueAssignGroupedByStatus => {
  return result.reduce((acc, row) => {
    const { status, fkUserId, aliasUser, ...rest } = row;
    if (!acc[status]) {
      acc[status] = {
        status,
        fkUserId,
        aliasUser,
        data: [],
      };
    }
    if (rest) {
      acc[status].data.push({
        id: rest.id,
        fkShiftId: rest.fkShiftId,
        shiftName: rest.shiftName,
        startDateTime: String(rest.startDateTime),
        assignedQuantity: rest.assignedQuantity || 0,
        assignedUsers: Array.isArray(rest.assignedUsers)
          ? (rest.assignedUsers as Omit<ShiftAssign, excludeFields>[])
          : [],
        isClosed: rest.isClosed,
        closedAt: rest.closedAt,
        isTemplate: rest.isTemplate,
        comments: rest.comments,
      });
    }
    return acc;
  }, {} as FatigueAssignGroupedByStatus);
};

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
export const groupByShift = (result: any[]): FatigueAssignGroupedByShift => {
  return result.reduce((acc, row) => {
    const { fkShiftId, shiftName, fkUserId, aliasUser, ...rest } = row;
    if (!acc[fkShiftId]) {
      acc[fkShiftId] = {
        fkShiftId: fkShiftId,
        shiftName: shiftName,
        fkUserId,
        aliasUser,
        data: [],
      };
    }
    if (rest) {
      acc[fkShiftId].data.push({
        id: rest.id,
        status: rest.status,
        startDateTime: String(rest.startDateTime),
        assignedQuantity: rest.assignedQuantity || 0,
        assignedUsers: Array.isArray(rest.assignedUsers)
          ? (rest.assignedUsers as Omit<ShiftAssign, excludeFields>[])
          : [],
        isClosed: rest.isClosed,
        closedAt: rest.closedAt,
        isTemplate: rest.isTemplate,
        comments: rest.comments,
      });
    }
    return acc;
  }, {} as FatigueAssignGroupedByShift);
};

export const validateFatigueConditions = async (
  count: number,
  existingSameShift: (typeof systemModuleControlFatigue.$inferSelect)[],
  existingUserAssignment: (typeof systemModuleControlFatigue.$inferSelect)[],
  existingActive: (typeof systemModuleControlFatigue.$inferSelect)[],
) => {
  if (count > 0) {
    if (existingSameShift.length > 0) {
      throw new Error('Ya existe un despliegue con el mismo turno y fecha');
    }
    if (existingUserAssignment.length > 0) {
      throw new Error('El encargado del despliegue ya está asignado a otro despliegue en la misma fecha');
    }
    if (existingActive.length > 0) {
      throw new Error('Ya existe un despliegue activo');
    }
  }
};

export const createBaseMetadata = () => ({
  message: 'Despliegue creado correctamente',
  submittedUsers: [] as Omit<ShiftAssign, excludeFields>[],
  assignedUsers: [] as (Omit<ShiftAssign, excludeFields> & { fkFatigueAssignId: string | null })[],
  unassignedUsers: [] as Omit<ShiftAssign, excludeFields>[],
  usersByShift: {} as UsersByShiftType,
});

export const identifyAssignments = async (
  assigns: Omit<ShiftAssign, 'fkSystemModuleId'>[],
  fatigueId: string,
): Promise<{
  toCreate: Omit<ShiftAssign, 'fkSystemModuleId'>[];
  toUpdate: Omit<ShiftAssign, 'fkSystemModuleId'>[];
  toDelete: Omit<ShiftAssign, 'fkSystemModuleId'>[];
}> => {
  // Obtener asignaciones del despliegue
  const assignedFatigues = await db
    .select()
    .from(systemModuleAssignmentShift)
    .where(eq(systemModuleAssignmentShift.fkFatigueId, fatigueId));

  // Convertir asignaciones en mapas
  /* const assignedFatiguesMap = new Map<string, Omit<ShiftAssign, 'fkSystemModuleId'>>();
  for (const assign of assignedFatigues) {
    assignedFatiguesMap.set(assign.fkUserId, assign);
  } */
  const assignedPayloadMap = new Map<string, Omit<ShiftAssign, 'fkSystemModuleId'>>();
  for (const assign of assigns) {
    assignedPayloadMap.set(assign.fkUserId, assign);
  }

  const toCreate: Omit<ShiftAssign, 'fkSystemModuleId'>[] = [];
  const toUpdate: Omit<ShiftAssign, 'fkSystemModuleId'>[] = [];
  const toDelete: Omit<ShiftAssign, 'fkSystemModuleId'>[] = [];

  // Identificar asignaciones para actualizar o eliminar
  for (const fatigue of assignedFatigues) {
    const assigned = assignedPayloadMap.get(fatigue.fkUserId);
    if (!assigned) {
      toDelete.push(fatigue);
    } else {
      if (
        fatigue.fkFatigueId !== assigned.fkFatigueId ||
        fatigue.checkInTime !== assigned.checkInTime ||
        fatigue.isLate !== assigned.isLate ||
        fatigue.isDenied !== assigned.isDenied ||
        fatigue.denialReason !== assigned.denialReason
      ) {
        toUpdate.push(assigned);
      }
      assignedPayloadMap.delete(fatigue.fkUserId);
    }
  }

  // Las asignaciones que quedan en el mapa son las que se van a insertar
  for (const assign of assignedPayloadMap.values()) {
    toCreate.push(assign);
  }

  return { toCreate, toUpdate, toDelete };
};

export const createShiftAssigns = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkFatigueId: string,
  fkShiftId: string,
  arrayUsersIds: Omit<ShiftAssign, excludeFields>[],
  metadata: ShiftAssignMetadata,
  systemModuleCache: SystemModuleCache,
  fkLastUserId: string | undefined,
  fkLastDependencyId: string | undefined,
) => {
  if (!arrayUsersIds?.length) return metadata;

  const systemModuleShiftAssign = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_ASIGNACIONES_TURNOS);
  if (!systemModuleShiftAssign) throw new Error('System module CTRL_ASIGNACIONES_TURNOS not found');

  // Obtener array solo los IDs de los usuarios
  const userIds = arrayUsersIds.map((user) => user.fkUserId);

  // Obtener todos los usuarios
  const usersResult = await trx
    .select({
      user: systemModuleUser,
      employment: systemModuleUserKardexEmployment,
    })
    .from(systemModuleUser)
    .leftJoin(systemModuleUserKardexEmployment, eq(systemModuleUser.id, systemModuleUserKardexEmployment.fkUserId))
    .where(inArray(systemModuleUser.id, userIds));

  // Estructuras
  const [usersByIdMap, usersByShiftMap] = usersResult.reduce(
    ([byId, byShift], userData) => {
      byId.set(userData.user.id, userData);
      const shiftKey = userData.employment?.fkShiftId ?? 'without_shift';
      if (!byShift[shiftKey]) {
        byShift[shiftKey] = [];
      }
      byShift[shiftKey].push({
        user: {
          id: userData.user.id,
          alias: userData.user.alias,
        },
        employment: userData.employment
          ? {
              id: userData.employment?.id,
              fkShiftId: userData.employment?.fkShiftId,
              cup: userData.employment?.cup,
            }
          : null,
      });
      return [byId, byShift];
    },
    [new Map<string, (typeof usersResult)[0]>(), {} as UsersByShiftType],
  );

  // Añadir el mapa al metadata
  metadata.usersByShift = usersByShiftMap;

  // Procesar usuarios para asignación
  for (const userItem of arrayUsersIds) {
    metadata.submittedUsers.push(userItem);

    // Buscar el usuario en el mapa
    const userData = usersByIdMap.get(userItem.fkUserId);
    if (!userData) {
      // Usuario no encontrado en la base de datos
      updateMetadataError(metadata);
      metadata.unassignedUsers.push(userItem);
      continue;
    }

    // Obtener el shiftId del usuario (null se considera 'without_shift')
    const userShiftId = userData.employment?.fkShiftId ?? null;

    // Verificar si el usuario tiene el mismo turno que el requerido
    if (userShiftId !== fkShiftId) {
      // Usuario tiene un turno diferente o no tiene turno
      updateMetadataError(metadata);
      metadata.unassignedUsers.push(userItem);
      continue;
    }

    // Verificar si el usuario no esta dado de baja
    if (userData.user.isDeleted || !userData.user.isEnabled) {
      updateMetadataError(metadata);
      metadata.unassignedUsers.push(userItem);
      continue;
    }

    try {
      const userFatigueAssign = await assignShiftBodySchema.parseAsync({
        fkUserId: userItem.fkUserId,
        fkFatigueId,
        status: 'unknown',
        fkLastUserId,
        fkLastDependencyId,
      });

      // Asignar usuario al despliegue
      await trx.insert(systemModuleAssignmentShift).values({
        ...userFatigueAssign,
        fkSystemModuleId: systemModuleShiftAssign.id,
      });

      metadata.assignedUsers.push({
        fkUserId: userItem.fkUserId,
        fkFatigueId,
        fkFatigueAssignId: null,
      });
    } catch (error) {
      console.log(error);
      updateMetadataError(metadata);
      metadata.unassignedUsers.push(userItem);
    }
  }
  return metadata;
};

export const createShiftAssignments = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkFatigueId: string,
  fkShiftId: string,
  arrayUsersIds: Omit<ShiftAssign, excludeFields>[],
  metadata: ShiftAssignMetadata,
  systemModuleCache: SystemModuleCache,
  fkLastUserId: string | undefined,
  fkLastDependencyId: string | undefined,
  isTemplate = false,
) => {
  if (isTemplate) {
    const templateAssignments = await trx
      .select()
      .from(systemModuleAssignmentShift)
      .where(eq(systemModuleAssignmentShift.fkFatigueId, fkFatigueId));

    arrayUsersIds.push(
      ...templateAssignments.map((assign) => ({
        fkUserId: assign.fkUserId,
        fkFatigueId,
        //fkLastUserId: fkLastUserId || '',
        //fkLastDependencyId: fkLastDependencyId || '',
      })),
    );
  }

  return createShiftAssigns(
    trx,
    fkFatigueId,
    fkShiftId,
    arrayUsersIds,
    metadata,
    systemModuleCache,
    fkLastUserId,
    fkLastDependencyId,
  );
};

const updateMetadataError = (metadata: ShiftAssignMetadata) => {
  if (metadata.unassignedUsers.length === 0) {
    metadata.message = 'Despliegue creado correctamente, pero no se pudieron asignar algunos elementos';
  }
};

export const startRegisterInKardex = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkFatigueId: string,
  fkLastUserId: string,
  fkLastDependencyId: string,
  startingArray: ('weapon' | 'uniform' | 'tactical_equipment')[],
): Promise<KardexRecord[]> => {
  // Obtener todos los usuarios asignados al despliegue
  const assignedUsers = await trx
    .select({ fkUserId: systemModuleAssignmentShift.fkUserId })
    .from(systemModuleAssignmentShift)
    .where(eq(systemModuleAssignmentShift.fkFatigueId, fkFatigueId));

  if (assignedUsers.length === 0) {
    throw new Error('No se encontraron usuarios asignados al despliegue');
  }

  const userIds = assignedUsers.map((user) => user.fkUserId);
  const result: KardexRecord[] = userIds.map((fkUserId) => ({
    fkUserId,
    weaponAssignments: null,
    uniformAssignments: null,
    tacticalEquipmentAssignments: null,
  }));

  // Procesar cada tipo de asignación en paralelo
  await Promise.all([processWeaponAssignments(), processUniformAssignments(), processTacticalEquipmentAssignments()]);

  // Registrar en Kardex
  await registerKardexEntries();

  return result;

  // Funciones auxiliares
  async function processWeaponAssignments() {
    if (!startingArray.includes('weapon')) return;

    const assignments = await trx
      .select({
        fkUserId: systemModuleAssignmentWeapon.fkUserId,
        weaponData: systemModuleControlWeapon,
        fkWeaponAssignmentId: systemModuleAssignmentWeapon.id,
      })
      .from(systemModuleAssignmentWeapon)
      .innerJoin(
        systemModuleControlWeapon,
        eq(systemModuleAssignmentWeapon.fkControlWeaponId, systemModuleControlWeapon.id),
      )
      .where(
        and(
          inArray(systemModuleAssignmentWeapon.fkUserId, userIds),
          eq(systemModuleAssignmentWeapon.fkFatigueId, fkFatigueId),
        ),
      );

    // Agrupar por usuario
    const grouped = groupBy(assignments, 'fkUserId');
    for (const r of result) {
      r.weaponAssignments = grouped[r.fkUserId] || null;
    }
  }

  async function processUniformAssignments() {
    if (!startingArray.includes('uniform')) return;

    const assignments = await trx
      .select({
        fkUserId: systemModuleAssignmentUniform.fkUserId,
        uniformData: systemModuleControlUniform,
        fkUniformAssignmentId: systemModuleAssignmentUniform.id,
      })
      .from(systemModuleAssignmentUniform)
      .innerJoin(
        systemModuleControlUniform,
        eq(systemModuleAssignmentUniform.fkUniformId, systemModuleControlUniform.id),
      )
      .where(
        and(
          inArray(systemModuleAssignmentUniform.fkUserId, userIds),
          eq(systemModuleAssignmentUniform.fkFatigueId, fkFatigueId),
        ),
      );

    const grouped = groupBy(assignments, 'fkUserId');
    for (const r of result) {
      r.uniformAssignments = grouped[r.fkUserId] || null;
    }
  }

  async function processTacticalEquipmentAssignments() {
    if (!startingArray.includes('tactical_equipment')) return;

    const assignments = await trx
      .select({
        fkUserId: systemModuleAssignmentTacticalEquipment.fkUserId,
        tacticalEquipmentData: systemModuleControlUniform,
        fkTacticalEquipmentAssignmentId: systemModuleAssignmentTacticalEquipment.id,
      })
      .from(systemModuleAssignmentTacticalEquipment)
      .innerJoin(
        systemModuleControlUniform,
        eq(systemModuleAssignmentTacticalEquipment.fkTacticalEquipmentId, systemModuleControlUniform.id),
      )
      .where(
        and(
          inArray(systemModuleAssignmentTacticalEquipment.fkUserId, userIds),
          eq(systemModuleAssignmentTacticalEquipment.fkFatigueId, fkFatigueId),
        ),
      );

    const grouped = groupBy(assignments, 'fkUserId');
    for (const r of result) {
      r.tacticalEquipmentAssignments = grouped[r.fkUserId] || null;
    }
  }

  async function registerKardexEntries() {
    const armamentEntries: KardexArmament[] = [];
    const tacticalEquipmentEntries: KardexTacticalEquipment[] = [];

    result.flatMap((userRecord) => {
      const currentDate = moment().format();

      // Registrar armas
      if (userRecord.weaponAssignments) {
        armamentEntries.push(
          ...userRecord.weaponAssignments.map((weapon) => ({
            fkUserId: userRecord.fkUserId,
            fkWeaponTypeId: weapon.weaponData.fkWeaponTypeId,
            serialNumber: weapon.weaponData.serialNumber,
            assignmentDate: currentDate,
            weaponStatus: 'active' as WeaponStatuse,
            unassignmentDate: null,
            fkLastUserId,
            fkLastDependencyId,
            //details: `Asignacion de arma con número de serie: "${weapon.weaponData.serialNumber}", para despliegue "${fkFatigueId}"`,
          })),
        );
      }

      // Registrar uniformes
      if (userRecord.uniformAssignments) {
        tacticalEquipmentEntries.push(
          ...userRecord.uniformAssignments.map((uniform) => ({
            fkUserId: userRecord.fkUserId,
            fkTacticalEquipmentId: null,
            fkUniformId: uniform.uniformData.id,
            assignmentDate: currentDate,
            assignmentEndDate: null,
            expirationDate: null,
            estimatedReturnDate: null,
            fkSizeId: uniform.uniformData.fkSizeId,
            tacticalEquipmentStatus: 'in_use' as TacticalEquipmentStatuse,
            fkLastUserId,
            fkLastDependencyId,
            //details: `Asignacion de uniforme con número de serie: "${uniform.uniformData.serialNumber}", para despliegue "${fkFatigueId}"`,
          })),
        );
      }

      // Registrar equipo táctico
      if (userRecord.tacticalEquipmentAssignments) {
        tacticalEquipmentEntries.push(
          ...userRecord.tacticalEquipmentAssignments.map((tacticalEquipment) => ({
            fkUserId: userRecord.fkUserId,
            fkTacticalEquipmentId: tacticalEquipment.tacticalEquipmentData.id,
            fkUniformId: null,
            assignmentDate: currentDate,
            assignmentEndDate: null,
            expirationDate: null,
            estimatedReturnDate: null,
            fkSizeId: tacticalEquipment.tacticalEquipmentData.fkSizeId,
            tacticalEquipmentStatus: 'in_use' as TacticalEquipmentStatuse,
            fkLastUserId,
            fkLastDependencyId,
            //details: `Asignacion de equipo táctico con número de serie: "${tacticalEquipment.tacticalEquipmentData.serialNumber}", para despliegue "${fkFatigueId}"`,
          })),
        );
      }
    });

    if (armamentEntries.length > 0) {
      await trx.insert(systemModuleUserKardexArmament).values(armamentEntries);
    }

    if (tacticalEquipmentEntries.length > 0) {
      await trx.insert(systemModuleUserKardexTacticalEquipment).values(tacticalEquipmentEntries);
    }
  }
};

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
const groupBy = (array: any[], key: string) => {
  return array.reduce((acc, item) => {
    if (!acc[item[key]]) {
      acc[item[key]] = [];
    }
    acc[item[key]].push(item);
    return acc;
  }, {});
};
