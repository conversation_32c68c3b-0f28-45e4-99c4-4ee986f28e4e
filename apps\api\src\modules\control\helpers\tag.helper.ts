import { z } from 'zod';

export const tagBodySchema = z.object({
  name: z.string(),
  color: z.string().default('#000000'),
  icon: z.string().default('none'),
  description: z.string().nullable().optional(),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});

export const validateNormalizeHexColor = (color: string): string => {
  // Eliminar espacios y convertir a mayusculas
  const clean = color.trim().toUpperCase();
  // Eliminar el # si existe
  const sinHash = clean.startsWith('#') ? clean.slice(1) : clean;
  // Validar formato hexadecimal (3, 6 u 8 caracteres)
  if (/^#(?:[0-9A-Fa-f]{3}|[0-9A-Fa-f]{6}|[0-9A-Fa-f]{8})$/.test(sinHash)) {
    return `#${sinHash}`;
  }
  // Lanzar error si no es válido
  throw new Error(`Color hexadecimal inválido: "${color}"`);
};
