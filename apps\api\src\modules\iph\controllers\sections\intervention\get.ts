import type { Request, Response } from 'express';
import db from '@/db';
import {
  catMunicipality,
  catSettlement,
  catState,
  systemModuleControlIphIntervention,
  systemModuleControlPlaces,
} from '@repo/shared-drizzle/schemas';
import { HttpStatus } from '@/utils/http_status';
import { getLocale, type Locale } from '@/utils/locale_validator';
import { Logger } from '@/utils/logger';
import { ModuleName } from '@/utils/module_names';
import { and, asc, eq, sql } from 'drizzle-orm';

const query = ({ locale, id }: { locale: Locale; id: string }) => {
  return db
    .select({
      id: systemModuleControlIphIntervention.id,
      arrivalAt: systemModuleControlIphIntervention.arrivalAt,
      wasSiteInspected: systemModuleControlIphIntervention.wasSiteInspected,
      wasRelatedObjectFound: systemModuleControlIphIntervention.wasRelatedObjectFound,
      wasSitePreserved: systemModuleControlIphIntervention.wasSitePreserved,
      wasSitePrioritized: systemModuleControlIphIntervention.wasSitePrioritized,
      isSocialRisk: systemModuleControlIphIntervention.isSocialRisk,
      socialRiskDescription: systemModuleControlIphIntervention.socialRiskDescription,
      isNaturalRisk: systemModuleControlIphIntervention.isNaturalRisk,
      naturalRiskDescription: systemModuleControlIphIntervention.naturalRiskDescription,
      interventionAddress: {
        id: systemModuleControlPlaces.id,
        name: systemModuleControlPlaces.name,
        type: systemModuleControlPlaces.type,
        state: sql`json_build_object('id', ${catState.id}, 'name', ${catState[locale]})`,
        municipality: sql`json_build_object('id', ${catMunicipality.id}, 'name', ${catMunicipality[locale]})`,
        colony: sql`json_build_object('id', ${catSettlement.id}, 'name', ${catSettlement[locale]})`,
        zipCode: systemModuleControlPlaces.zipCode,
        street: systemModuleControlPlaces.street,
        number: systemModuleControlPlaces.number,
        interiorNumber: systemModuleControlPlaces.interiorNumber,
        betweenStreet1: systemModuleControlPlaces.betweenStreet1,
        betweenStreet2: systemModuleControlPlaces.betweenStreet2,
        reference: systemModuleControlPlaces.reference,
        location: systemModuleControlPlaces.location,
        sectors: systemModuleControlPlaces.sectors,
      },
      updatedAt: systemModuleControlIphIntervention.updatedAt,
      createdAt: systemModuleControlIphIntervention.createdAt,
    })
    .from(systemModuleControlIphIntervention)
    .leftJoin(
      systemModuleControlPlaces,
      eq(systemModuleControlPlaces.id, systemModuleControlIphIntervention.fkInterventionAddressPlaceId),
    )
    .leftJoin(catState, eq(catState.id, systemModuleControlPlaces.fkStateId))
    .leftJoin(catMunicipality, eq(catMunicipality.id, systemModuleControlPlaces.fkMunicipalityId))
    .leftJoin(catSettlement, eq(catSettlement.id, systemModuleControlPlaces.fkColonyId))
    .where(and(eq(systemModuleControlIphIntervention.id, id), eq(systemModuleControlIphIntervention.isDeleted, false)))
    .orderBy(asc(systemModuleControlIphIntervention.id))
    .limit(1);
};

export const get = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;

  let locale: Locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const queryResult = await query({ locale, id });
    if (queryResult.length === 0) {
      res.status(HttpStatus.NOT_FOUND).json({ message: 'Not found' });
      return;
    }
    res.status(HttpStatus.OK).json(queryResult[0]);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      module: ModuleName.IPH,
      method: '/iph/section/intervention/get/:id - get',
      source: req.headers['user-agent'],
      stack,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};
