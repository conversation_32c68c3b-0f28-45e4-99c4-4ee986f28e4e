meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/kardex/academic/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 0195444a-9263-27fa-f0ba-c801991db448
}

body:json {
  {
    "fkUserId": "ID Usuario",
    "fkHighestEducationLevelId": "ID Nivel educativo",
    "fkInstitutionId": "ID Institución Externa",
    "careerName": "Nombre Carrera",
    "careerStartDate": "Fecha inicio",
    "graduationDate": "Fecha fin", // Opcional
    "isGraduated": true, 
    "academicFile": {
      "mimeType": "Tipo de contenido",
      "nameFile": "Nombre archivo",
      "value": "Archivo en Base64",
      "fileType": "ID Tipo de Archivo"
    }
  }
}
