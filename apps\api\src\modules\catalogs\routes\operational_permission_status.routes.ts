import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/operational_permission_status.controller';

const catOperationalPermissionStatusRouter = Router();

catOperationalPermissionStatusRouter.get('/', getAll);
catOperationalPermissionStatusRouter.get('/search', searchPaginated);
catOperationalPermissionStatusRouter.get('/:id', getOne);
catOperationalPermissionStatusRouter.post('/', create);
catOperationalPermissionStatusRouter.put('/:id', update);
catOperationalPermissionStatusRouter.delete('/:id', deleteOne);

export default catOperationalPermissionStatusRouter;
