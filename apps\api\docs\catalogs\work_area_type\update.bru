meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/catalogs/work_area_type/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 
}

body:json {
  {
    "key": "test_key_change", // optional
    "name": "Cambio en nombre de prueba", // optional
    "description": "Cambio en descripcion de Nombre de prueba", // optional
    "fkDirectionId": "ID Dirección",
    "fkLeadershipId": "ID Jefatura"
  }
}
