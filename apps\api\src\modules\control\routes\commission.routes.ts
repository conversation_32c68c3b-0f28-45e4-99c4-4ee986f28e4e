import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/commission.controller';

const commissionRouter = Router();

commissionRouter.get('/', getAll);
commissionRouter.get('/search', searchPaginated);
commissionRouter.get('/:id', getOne);
commissionRouter.post('/', create);
commissionRouter.put('/:id', update);
commissionRouter.delete('/:id', deleteOne);

export default commissionRouter;
