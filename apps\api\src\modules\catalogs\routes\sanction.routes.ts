import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/sanction.controller';

const catSanctionRouter = Router();

catSanctionRouter.get('/', getAll);
catSanctionRouter.get('/search', searchPaginated);
catSanctionRouter.get('/:id', getOne);
catSanctionRouter.post('/', create);
catSanctionRouter.put('/:id', update);
catSanctionRouter.delete('/:id', deleteOne);

export default catSanctionRouter;
