// import db from '@/db';
// import { systemSession } from '@/db/schema/system_session.schema';
// import { HttpStatus } from '@/utils/http_status';
// import { Logger } from '@/utils/logger';
// import { eq } from 'drizzle-orm';
// import type { Request, Response, NextFunction } from 'express';
// import { jwtVerify } from 'jose';

// // Clave secreta con el que se firma el token
// const key = new TextEncoder().encode(process.env.JWT_SECRET);

// /**
//  * Middleware para verificar la autenticación mediante un token JWT.
//  * @param req - Objeto de solicitud de Express.
//  * @param res - Objeto de respuesta de Express.
//  * @param next - Función para continuar con el siguiente middleware.
//  */
// const authenticationMiddleware = async (req: Request, res: Response, next: NextFunction) => {
//   try {
//     if (req.path.match(/\.[0-9a-z]{2,5}$/)) {
//       // continue for assets
//       next();
//       return;
//     }

//     // req.cookies.sid
//     const jwt = req.cookies.sid;
//     if (!jwt) {
//       if (req.path.startsWith('/auth')) {
//         return next(); // Saltar la autenticación
//       }
//       res.status(HttpStatus.UNAUTHORIZED).json({ message: 'Unauthorized' });
//       next(new Error('Missing authorization header'));
//       return;
//     }

//     if (req.path.startsWith('/auth')) {
//       res.redirect('/');
//       return;
//     }

//     // Verificar y decodificar el token
//     const { payload } = await jwtVerify(jwt, key);

//     // Obtener la informarción del token JWT
//     const sessionId = payload.sid as string;

//     // Verificar si los headers están presentes
//     if (!sessionId) {
//       res.status(400).json({ success: false, message: 'Missing responsibility headers in token' });
//       next(new Error('Missing responsibility headers in token'));
//       return;
//     }

//     const sessionResult = await db.select().from(systemSession).where(eq(systemSession.id, sessionId)).limit(1);
//     if (sessionResult.length === 0) {
//       res.clearCookie('sid').status(HttpStatus.UNAUTHORIZED).json({ success: false, message: 'Unauthorized' });
//       db.delete(systemSession)
//         .where(eq(systemSession.id, sessionId))
//         .catch((error) => {
//           const { message, stack } = error as Error;
//           Logger.getInstance().error(message, {
//             stack,
//             method: 'authenticationMiddleware',
//             source: req.headers['user-agent'],
//             module: 'auth',
//           });
//         });
//       next(new Error('Unauthorized'));
//       return;
//     }

//     const session = sessionResult[0];
//     if (session.expiresAt.getUTCMilliseconds() < Date.now()) {
//       res.clearCookie('sid').status(HttpStatus.UNAUTHORIZED).json({ success: false, message: 'Unauthorized' });
//       db.delete(systemSession)
//         .where(eq(systemSession.id, sessionId))
//         .catch((error) => {
//           const { message, stack } = error as Error;
//           Logger.getInstance().error(message, {
//             stack,
//             method: 'authenticationMiddleware',
//             source: req.headers['user-agent'],
//             module: 'auth',
//           });
//         });
//       next(new Error('Unauthorized'));
//       return;
//     }

//     next();
//   } catch (err) {
//     const { message, stack } = err as Error;
//     Logger.getInstance().error(message, {
//       method: 'authentcationMiddleware',
//       module: 'middleware',
//       source: req.headers['user-agent'],
//       stack: stack,
//     });
//     res.status(HttpStatus.UNAUTHORIZED).json({ message: 'Unauthorized' });
//     next(err);
//     return;
//   }
// };

// export default authenticationMiddleware;
