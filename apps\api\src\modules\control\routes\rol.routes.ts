import { Router } from 'express';
import {
  assignUserToRole,
  create,
  deleteOne,
  getAll,
  getOne,
  searchPaginated,
  update,
} from '../controllers/rol.controller';

const rolRouter = Router();

rolRouter.get('/', getAll);
rolRouter.get('/search', searchPaginated);
rolRouter.get('/:id', getOne);
rolRouter.post('/', create);
rolRouter.post('/assign', assignUserToRole);
rolRouter.put('/:id', update);
rolRouter.delete('/:id', deleteOne);

export default rolRouter;
