import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/direction_type.controller';

const directionTypeRouter = Router();

directionTypeRouter.get('/', getAll);
directionTypeRouter.get('/search', searchPaginated);
directionTypeRouter.get('/:id', getOne);
directionTypeRouter.post('/', create);
directionTypeRouter.put('/:id', update);
directionTypeRouter.delete('/:id', deleteOne);

export default directionTypeRouter;
