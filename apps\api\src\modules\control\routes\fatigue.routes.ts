import { Router } from 'express';
import {
  create,
  deleteOne,
  end,
  getAll,
  getOne,
  searchPaginated,
  searchPlaces,
  start,
  update,
} from '../controllers/fatigue.controller';

const fatigueRouter = Router();

fatigueRouter.get('/', getAll);
fatigueRouter.get('/search', searchPaginated);
fatigueRouter.get('/places/search', searchPlaces);
fatigueRouter.get('/:id', getOne);
fatigueRouter.post('/', create);
fatigueRouter.post('/start/:id', start);
fatigueRouter.post('/end/:id', end);
fatigueRouter.put('/:id', update);
fatigueRouter.delete('/:id', deleteOne);

export default fatigueRouter;
