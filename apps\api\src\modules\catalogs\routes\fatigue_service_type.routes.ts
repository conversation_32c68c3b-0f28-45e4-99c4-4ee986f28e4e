import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/fatigue_service_type.controller';

const catFatigueServiceTypeRouter = Router();

catFatigueServiceTypeRouter.get('/', getAll);
catFatigueServiceTypeRouter.get('/search', searchPaginated);
catFatigueServiceTypeRouter.get('/:id', getOne);
catFatigueServiceTypeRouter.post('/', create);
catFatigueServiceTypeRouter.put('/:id', update);
catFatigueServiceTypeRouter.delete('/:id', deleteOne);

export default catFatigueServiceTypeRouter;
