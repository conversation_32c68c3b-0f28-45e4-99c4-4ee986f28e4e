meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/user/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 0195008d-1ef1-1f07-3b3e-849120e08563
}

headers {
  authorization: 
}

body:json {
  {
    "basic": {
      "name": "Nombre de prueba", // Requerido
      "firstSurname": "Apellido 1", // Requerido
      "secondSurname": "Apellido 2", // Requerido
      "fkGenderId": "ID Genero", // Requerido
      "phone": "Telefono", // Opcional
      "bornAt": "Cumpleaños", // Opcional
      "fkAddressPlaceId": "ID Lugar", // Opcional
      "curp": "CURP de prueba", // Opcional
      "rfc": "RFC  de prueba", // Opcional
      "fkNationalityId": "ID Nacionalidad", // Opcional
      "fkMaritalStatusId": "ID Estado Civil", // Opcional
      "educationLevel": "Nivel de estudios", // Opcional - ['primaria_trunca','primaria_terminada','secundaria_trunca','secundaria_terminada','preparatoria_trunca','preparatoria_terminada','licenciatura_trunca','licenciatura_terminada','maestria','doctorado']
      "email": "Email de prueba", // Opcional
      "profileFile": {
        "mimeType": "Tipo de contenido", // Requerido
        "name": "Nombre del archivo", // Requerido
        "type": "Tipo de archivo/extensión", // Requerido
        "value": "Archivo en Base64" // Requerido
      } // Opcional
    },
    "employment": {
      "userNumber": "Número de empleado", // Requerido
      "cup": "Código Único de Policia", // Requerido
      "fkCurrentJobPositionId": "ID de Trabajo Policial", // Requerido
      "fkSecondmentId": "ID de Adscripción", // Requerido
      "fkInstitutionId": "ID de Institución", // Requerido
      "federalEntity": "Entidad Federativa", // Opcional - Listado de "federalEntitiesValue"
      "inmediateBoss": "ID Usuario", // Opcional
      "fkLeadershipId": "ID Jefatura/Departamento", // Opcional
      "fkWorkAreaId": "ID Área de Trabajo", // Opcional - Requerido si se elige una Jefatura/Departamento
      "fkShiftId": "ID Turno" // Opcional
    },
    "medical": {
      "bloodGroup": "Grupo sanguíneo", // Requerido,
      "height": 0, // Requerido - Altura en centímetros
      "weight": 0, // Requerido - Peso en kilos
      "allergies": "Alergías", // Opcional
      "ailments": "Padecimientos/Enfermedades", // Opcional
      "medicalInsurance": true, // Opcional - Tiene seguro? (default false)
      "useWheelchair": false // Opcional - Utiliza silla de ruedas? (default false)
    }
  }
}
