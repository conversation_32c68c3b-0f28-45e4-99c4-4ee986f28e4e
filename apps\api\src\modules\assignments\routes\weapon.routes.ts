import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/weapon.controller';

const weaponAssignRouter = Router();

weaponAssignRouter.get('/', getAll);
weaponAssignRouter.get('/search', searchPaginated);
weaponAssignRouter.get('/:id', getOne);
weaponAssignRouter.post('/', create);
weaponAssignRouter.put('/:id', update);
weaponAssignRouter.delete('/:id', deleteOne);

export default weaponAssignRouter;
