import { userStatusInFatigueValues } from '@repo/types/values';
import { z } from 'zod';

export const assignShiftBodySchema = z.object({
  fkUserId: z.string(),
  fkFatigueId: z.string(),
  checkInTime: z.date().nullable().optional(),
  isLate: z.boolean().nullable().optional(),
  isDenied: z.boolean().default(false),
  denialReason: z.string().nullable().optional(),
  status: z.enum(userStatusInFatigueValues).default('unknown'),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});
