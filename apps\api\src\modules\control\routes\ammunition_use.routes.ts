import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/ammunition_use.controller';

const ammunitionUseRouter = Router();

ammunitionUseRouter.get('/', getAll);
ammunitionUseRouter.get('/search', searchPaginated);
ammunitionUseRouter.get('/:id', getOne);
ammunitionUseRouter.post('/', create);
ammunitionUseRouter.put('/:id', update);
ammunitionUseRouter.delete('/:id', deleteOne);

export default ammunitionUseRouter;
