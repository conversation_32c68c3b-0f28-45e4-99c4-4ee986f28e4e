import { placeTypeValues } from '@repo/types/values';
import { z } from 'zod';

export const placeBodySchema = z.object({
  name: z.string(),
  type: z.enum(placeTypeValues).default('street_address'),
  fkStateId: z.string(),
  stateName: z.string(),
  fkMunicipalityId: z.string(),
  municipalityName: z.string(),
  fkColonyId: z.string(),
  colonyName: z.string(),
  zipCode: z.string(),
  street: z.string(),
  number: z.string(),
  interiorNumber: z.string(),
  betweenStreet1: z.string(),
  betweenStreet2: z.string(),
  reference: z.string(),
  location: z.array(z.any()).optional().default([]),
  sectors: z.array(z.string()).optional().default([]),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});
