import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, or, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';
import { systemModuleControlSupplier } from '@repo/shared-drizzle/schemas';
import { supplierBodySchema } from '../helpers/supplier.helper';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS } = req.query;
  let [deleted, disabled] = [false, false];

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlSupplier.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlSupplier.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlSupplier.id,
        name: systemModuleControlSupplier.name,
        contactName: systemModuleControlSupplier.contactName,
        contactPhone: systemModuleControlSupplier.contactPhone,
        contactEmail: systemModuleControlSupplier.contactEmail,
        address: systemModuleControlSupplier.address,
        city: systemModuleControlSupplier.city,
        state: systemModuleControlSupplier.state,
        zipCode: systemModuleControlSupplier.zipCode,
        country: systemModuleControlSupplier.country,
        rfc: systemModuleControlSupplier.rfc,
        website: systemModuleControlSupplier.website,
        comments: systemModuleControlSupplier.comments,
      })
      .from(systemModuleControlSupplier)
      .where(and(...filters));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.SUPPLIER}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleControlSupplier.id,
        name: systemModuleControlSupplier.name,
        contactName: systemModuleControlSupplier.contactName,
        contactPhone: systemModuleControlSupplier.contactPhone,
        contactEmail: systemModuleControlSupplier.contactEmail,
        address: systemModuleControlSupplier.address,
        city: systemModuleControlSupplier.city,
        state: systemModuleControlSupplier.state,
        zipCode: systemModuleControlSupplier.zipCode,
        country: systemModuleControlSupplier.country,
        rfc: systemModuleControlSupplier.rfc,
        website: systemModuleControlSupplier.website,
        comments: systemModuleControlSupplier.comments,
      })
      .from(systemModuleControlSupplier)
      .where(eq(systemModuleControlSupplier.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.SUPPLIER}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const insertData = supplierBodySchema.parse({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_PROVEEDORES);
    if (!systemModule) {
      throw new Error('System module CTRL_PROVEEDORES not found');
    }
    const result = await db.insert(systemModuleControlSupplier).values({
      ...insertData,
      fkSystemModuleId: systemModule.id,
    });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.SUPPLIER} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;
    // Validar payload
    const updateData = supplierBodySchema.parse({ ...req.body });
    const result = await db
      .update(systemModuleControlSupplier)
      .set({ ...updateData })
      .where(eq(systemModuleControlSupplier.id, id));
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.SUPPLIER}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .update(systemModuleControlSupplier)
      .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
      .where(eq(systemModuleControlSupplier.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.SUPPLIER}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, deleted: deletedQS, disabled: disabledQS, q: qQS } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlSupplier.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlSupplier.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleControlSupplier.name, `%${q}%`));
      likeFilters.push(like(systemModuleControlSupplier.contactName, `%${q}%`));
      likeFilters.push(like(systemModuleControlSupplier.contactPhone, `%${q}%`));
      likeFilters.push(like(systemModuleControlSupplier.contactEmail, `%${q}%`));
      likeFilters.push(like(systemModuleControlSupplier.address, `%${q}%`));
      likeFilters.push(like(systemModuleControlSupplier.city, `%${q}%`));
      likeFilters.push(like(systemModuleControlSupplier.state, `%${q}%`));
      likeFilters.push(like(systemModuleControlSupplier.zipCode, `%${q}%`));
      likeFilters.push(like(systemModuleControlSupplier.country, `%${q}%`));
      likeFilters.push(like(systemModuleControlSupplier.rfc, `%${q}%`));
      likeFilters.push(like(systemModuleControlSupplier.website, `%${q}%`));
      likeFilters.push(like(systemModuleControlSupplier.comments, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleControlSupplier.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlSupplier.id,
        name: systemModuleControlSupplier.name,
        contactName: systemModuleControlSupplier.contactName,
        contactPhone: systemModuleControlSupplier.contactPhone,
        contactEmail: systemModuleControlSupplier.contactEmail,
        address: systemModuleControlSupplier.address,
        city: systemModuleControlSupplier.city,
        state: systemModuleControlSupplier.state,
        zipCode: systemModuleControlSupplier.zipCode,
        country: systemModuleControlSupplier.country,
        rfc: systemModuleControlSupplier.rfc,
        website: systemModuleControlSupplier.website,
        comments: systemModuleControlSupplier.comments,
      })
      .from(systemModuleControlSupplier)
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlSupplier.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.SUPPLIER} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
