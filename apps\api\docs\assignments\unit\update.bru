meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/assignments/unit/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 
}

body:json {
  {
    "fkUserId": "ID Usuario",
    "fkUnitControlId": "ID Unidad",
    "fkFatigueId": "ID Fatiga/Despliegue",
    "isDriver": false, // Si tiene Rol de conductor
    "isPassenger": false, // Si tiene Rol de pasajero
    "isInCharge": false, // Si es el encargado de la unidad
    "assignmentDate": "Fecha asignación",
    "assignmentEndDate": "Fecha entrega o desasignación", // Opcional
    "estimatedReturnDate": "Fecha estimada de entrega", // Opcional
    "comments": "Comentarios o Notas" // Opcional
  }
}
