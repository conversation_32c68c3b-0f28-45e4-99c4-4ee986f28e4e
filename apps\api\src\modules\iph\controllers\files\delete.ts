import db from '@/db';
import { HttpStatus } from '@/utils/http_status';
import { Logger } from '@/utils/logger';
import { ModuleName } from '@/utils/module_names';
import { systemModuleControlIphFiles } from '@repo/shared-drizzle/schemas';
import { eq, or } from 'drizzle-orm';
import type { Request, Response } from 'express';

export const deleteFile = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .update(systemModuleControlIphFiles)
      .set({
        isDeleted: true,
      })
      .where(or(eq(systemModuleControlIphFiles.id, id), eq(systemModuleControlIphFiles.path, id)));
    res.json(result.rowCount ? { message: 'success' } : { message: 'not found' });
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      method: 'DELETE iph/files - deleteFile',
      module: ModuleName.IPH,
      stack,
      source: req.headers['user-agent'],
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};
