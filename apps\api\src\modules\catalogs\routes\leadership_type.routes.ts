import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/leadership_type.controller';

const leadershipTypeRouter = Router();

leadershipTypeRouter.get('/', getAll);
leadershipTypeRouter.get('/search', searchPaginated);
leadershipTypeRouter.get('/:id', getOne);
leadershipTypeRouter.post('/', create);
leadershipTypeRouter.put('/:id', update);
leadershipTypeRouter.delete('/:id', deleteOne);

export default leadershipTypeRouter;
