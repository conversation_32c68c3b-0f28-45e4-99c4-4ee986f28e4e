import { Router } from 'express';
import {
  assignRoleToProfile,
  create,
  deleteOne,
  getAll,
  getOne,
  searchPaginated,
  update,
} from '../controllers/profile.controller';

const profileRouter = Router();

profileRouter.get('/', getAll);
profileRouter.get('/search', searchPaginated);
profileRouter.get('/:id', getOne);
profileRouter.post('/', create);
profileRouter.post('/assign', assignRoleToProfile);
profileRouter.put('/:id', update);
profileRouter.delete('/:id', deleteOne);

export default profileRouter;
