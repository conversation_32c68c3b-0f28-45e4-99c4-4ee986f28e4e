/* import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/armament_type.controller';

const catArmamentTypeRouter = Router();

catArmamentTypeRouter.get('/', getAll);
catArmamentTypeRouter.get('/search', searchPaginated);
catArmamentTypeRouter.get('/:id', getOne);
catArmamentTypeRouter.post('/', create);
catArmamentTypeRouter.put('/:id', update);
catArmamentTypeRouter.delete('/:id', deleteOne);

export default catArmamentTypeRouter;
 */
