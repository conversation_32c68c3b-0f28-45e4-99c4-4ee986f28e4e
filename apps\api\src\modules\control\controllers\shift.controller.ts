import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, not, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';
import { systemModuleControlShift } from '@repo/shared-drizzle/schemas';
import { isTimeBlockAvailable, isValidTimeFormat, shiftBodySchema } from '../helpers/shift.helper';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS } = req.query;
  let [deleted, disabled] = [false, false];

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlShift.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlShift.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlShift.id,
        shiftName: systemModuleControlShift.shiftName,
        startTime: systemModuleControlShift.startTime,
        endTime: systemModuleControlShift.endTime,
      })
      .from(systemModuleControlShift)
      .where(and(...filters));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.SHIFT}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleControlShift.id,
        shiftName: systemModuleControlShift.shiftName,
        startTime: systemModuleControlShift.startTime,
        endTime: systemModuleControlShift.endTime,
      })
      .from(systemModuleControlShift)
      .where(eq(systemModuleControlShift.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.SHIFT}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  const { shiftName, startTime, endTime } = req.body;
  try {
    // Validar el formato de las horas
    if (!isValidTimeFormat(startTime) || !isValidTimeFormat(endTime)) {
      throw new Error('El formato de las horas no es válido, debe ser hh:mm');
    }

    const insertData = await shiftBodySchema.parseAsync({
      shiftName,
      startTime,
      endTime,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });

    // Obtener el módulo de turnos
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_TURNOS);
    if (!systemModule) {
      throw new Error('System module CTRL_TURNOS not found');
    }

    // Verificar que no exista un turno con el mismo nombre
    const existingShift = await db
      .select()
      .from(systemModuleControlShift)
      .where(eq(systemModuleControlShift.shiftName, shiftName));
    if (existingShift.length > 0) {
      throw new Error('Ya existe un turno con el mismo nombre');
    }

    // Verificar si el bloque de tiempo está disponible
    const isAvailable = await isTimeBlockAvailable(startTime, endTime);
    if (!isAvailable) {
      throw new Error('El bloque de tiempo no está disponible');
    }

    // Insertar el nuevo turno
    const result = await db.insert(systemModuleControlShift).values({
      ...insertData,
      fkSystemModuleId: systemModule.id,
    });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.SHIFT} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { shiftName, startTime, endTime } = req.body;
  try {
    // Validar el formato de las horas
    if (!isValidTimeFormat(startTime) || !isValidTimeFormat(endTime)) {
      throw new Error('El formato de las horas no es válido, debe ser hh:mm');
    }
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    const updateData = await shiftBodySchema.parseAsync({
      shiftName,
      startTime,
      endTime,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });

    const result = await db.transaction(async (trx) => {
      // Verificar que el turno exista
      const existingShift = await trx
        .select()
        .from(systemModuleControlShift)
        .where(eq(systemModuleControlShift.id, id));
      if (existingShift.length === 0) {
        throw new Error('Turno no encontrado');
      }

      // Verificar que no exista un turno con el mismo nombre
      const existingShiftName = await trx
        .select()
        .from(systemModuleControlShift)
        .where(and(eq(systemModuleControlShift.shiftName, shiftName), not(eq(systemModuleControlShift.id, id))));
      if (existingShiftName.length > 0) {
        throw new Error('Ya existe un turno con el mismo nombre');
      }

      // Verificar si el bloque de tiempo está disponible
      const isAvailable = await isTimeBlockAvailable(startTime, endTime, id);
      if (!isAvailable) {
        throw new Error('El bloque de tiempo no está disponible');
      }

      // Actualizar el turno
      const updateResult = await trx
        .update(systemModuleControlShift)
        .set({ ...updateData })
        .where(eq(systemModuleControlShift.id, id));

      return updateResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.SHIFT}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    // TODO: Validar no se puede eliminar un turno que está asignado a una fatiga en activo o programado
    const result = await db
      .update(systemModuleControlShift)
      .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
      .where(eq(systemModuleControlShift.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.SHIFT}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, deleted: deletedQS, disabled: disabledQS, q: qQS } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlShift.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlShift.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleControlShift.shiftName, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlShift.startTime}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlShift.endTime}::text`, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleControlShift.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlShift.id,
        shiftName: systemModuleControlShift.shiftName,
        startTime: systemModuleControlShift.startTime,
        endTime: systemModuleControlShift.endTime,
      })
      .from(systemModuleControlShift)
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlShift.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.SHIFT} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
