meta {
  name: update
  type: http
  seq: 8
}

put {
  url: {{base}}/control/fatigue/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 
}

body:json {
  {
    "fkUserId": "ID Usuario", // Responsable de la fátiga
    "fkShiftId": "Id Turno",
    "fatigueDate": "Fecha de la Fátiga", // Formato aaaa/mm/dd
    "status": "Estatus Fátiga", // ['draft', 'scheduled', 'active', 'completed']
    "comments": "Comentarios", // Opcional
    "isTemplate": false, 
    "arrayUsersIds": [
      {
        "fkUserId": "ID Usuario", // Asignado a la fátiga
        "status": "Estatus de asignación" // ['early','on_time','late','denied','unknown','vacation','disabled','in_gdl','in_exam','in_course','permission','commissioner','with_license']
      }
    ] // Opcional, enviar [] si no se asignará usuarios al crear fátiga
  }
}
