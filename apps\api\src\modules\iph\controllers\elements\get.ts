// + user (id)
// |
// +-+ citizen (name, first_surname, second_surname)
// |
// +-+ system_module_user_kardex_employment
// | |
// | +-+ cat_secondment -> fk_secondment_id (Adscripción)
// | |
// | +-+ cat_police_level -> fk_police_level_id (Grado del policía)
// |
// +-+ system_module_assignment_vehicle
//   |
//   +-+ systemModuleControlUnit (unit)
import type { Request, Response } from 'express';
import { HttpStatus } from '@/utils/http_status';
import { Logger } from '@/utils/logger';
import db from '@/db';
import {
  catPoliceJob,
  catSecondment,
  systemModuleAssignmentUnit,
  systemModuleControlCitizen,
  systemModuleControlVehicleUnit,
  systemModuleUser,
  systemModuleUserKardexEmployment,
} from '@repo/shared-drizzle/schemas';
import { and, eq } from 'drizzle-orm';

export const getAll = async (req: Request, res: Response) => {
  try {
    const resultDB = await db
      .select()
      .from(systemModuleUser)
      .leftJoin(systemModuleControlCitizen, eq(systemModuleUser.fkCitizenId, systemModuleControlCitizen.id))
      .leftJoin(systemModuleAssignmentUnit, eq(systemModuleUser.id, systemModuleAssignmentUnit.fkUserId))
      .leftJoin(
        systemModuleControlVehicleUnit,
        eq(systemModuleAssignmentUnit.id, systemModuleControlVehicleUnit.fkVehicleId),
      )
      .leftJoin(systemModuleUserKardexEmployment, eq(systemModuleUser.id, systemModuleUserKardexEmployment.fkUserId))
      .leftJoin(catSecondment, eq(systemModuleUserKardexEmployment.fkSecondmentId, catSecondment.id))
      .leftJoin(catPoliceJob, eq(systemModuleUserKardexEmployment.fkCurrentJobPositionId, catPoliceJob.id))
      .where(and(eq(systemModuleUser.isDeleted, false), eq(systemModuleUser.isEnabled, true)));

    type ElementResult = {
      id: string;
      name: string;
      position: {
        id: string;
        name?: string | null;
      };
      vehicleUnit?: {
        id: string;
        code: string;
      } | null;
      secondment: {
        id: string;
        name?: string | null;
      };
    };

    const result: ElementResult[] = [];

    for (const element of resultDB) {
      if (!element.system_module_control_citizen) {
        return undefined;
      }
      if (!element.system_module_user_kardex_employment) {
        return undefined;
      }

      const vehicleUnit = !element.system_module_control_vehicle_unit
        ? null
        : {
            id: element.system_module_control_vehicle_unit.id,
            code: element.system_module_control_vehicle_unit.code,
          };

      let name = element.system_module_control_citizen.name;
      if (element.system_module_control_citizen.firstSurname) {
        name = `${name} ${element.system_module_control_citizen.firstSurname}`;
      }
      if (element.system_module_control_citizen.secondSurname) {
        name = `${name} ${element.system_module_control_citizen.secondSurname}`;
      }

      result.push({
        id: element.system_module_user.id,
        name: name || '',
        position: {
          id: element.system_module_user_kardex_employment.fkCurrentJobPositionId,
          name: element.cat_police_job?.es,
        },
        secondment: {
          id: element.system_module_user_kardex_employment.fkSecondmentId,
          name: element.cat_secondment?.es,
        },
        vehicleUnit,
      });
    }

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'iph/elements/all - getAll',
      source: req.headers['user-agent'],
      module: 'iph/elements',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
