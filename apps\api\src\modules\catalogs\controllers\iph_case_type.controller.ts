// TODO: Changed to enum
// import type { Request, Response } from 'express';
// import { catIphCaseType } from '@/db/schema/catalogs/catalogs.schema';
// import db from '@/db';
// import { and, asc, eq, gt, like, type SQL } from 'drizzle-orm';
// import { Logger } from '@/utils/logger';
// import { getLocale } from '@/utils/locale_validator';
// import { HttpStatus } from '@/utils/http_status';
// import { z } from 'zod';

// export const getAll = async (req: Request, res: Response) => {
//   const { deleted: deletedQS, disabled: disabledQS, locale: localeQS } = req.query;
//   let deleted = false;
//   let disabled = false;
//   let locale = getLocale();

//   if (deletedQS && typeof deletedQS === 'string') {
//     deleted = deletedQS === 'true';
//   }
//   if (disabledQS && typeof disabledQS === 'string') {
//     disabled = disabledQS === 'true';
//   }
//   if (localeQS && typeof localeQS === 'string') {
//     locale = getLocale(localeQS);
//   }
//   try {
//     const filters: SQL[] = [];
//     if (!deleted) {
//       filters.push(eq(catIphCaseType.isDeleted, false));
//     }
//     if (!disabled) {
//       filters.push(eq(catIphCaseType.isEnabled, true));
//     }
//     const result = await db
//       .select({
//         id: catIphCaseType.id,
//         key: catIphCaseType.key,
//         name: catIphCaseType[locale],
//         description: catIphCaseType.description,
//       })
//       .from(catIphCaseType);
//     res.status(HttpStatus.OK).json(result);
//   } catch (error) {
//     const { message, stack } = error as Error;
//     Logger.getInstance().error(message, {
//       stack,
//       method: 'iph_case_type/all - getAll',
//       source: req.headers['user-agent'],
//       module: 'catalogs',
//     });
//     res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
//   }
// };

// export const getOne = async (req: Request, res: Response) => {
//   const { id } = req.params;
//   const { locale: localeQS } = req.query;
//   let locale = getLocale();

//   if (localeQS && typeof localeQS === 'string') {
//     locale = getLocale(localeQS);
//   }
//   try {
//     const result = await db
//       .select({
//         id: catIphCaseType.id,
//         key: catIphCaseType.key,
//         name: catIphCaseType[locale],
//         description: catIphCaseType.description,
//       })
//       .from(catIphCaseType)
//       .where(eq(catIphCaseType.id, id));
//     res.status(HttpStatus.OK).json(result);
//   } catch (error) {
//     const { message, stack } = error as Error;
//     Logger.getInstance().error(message, {
//       stack,
//       method: `iph_case_type/${id} - getOne`,
//       source: req.headers['user-agent'],
//       module: 'catalogs',
//     });
//     res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
//   }
// };

// export const create = async (req: Request, res: Response) => {
//   const bodySchema = z.object({
//     key: z.string(),
//     name: z.string(),
//     description: z.string().optional(),
//     fkLastUserId: z.string(),
//     fkLastDependencyId: z.string(),
//   });

//   const { locale: localeQS } = req.query;

//   let locale = getLocale();

//   if (localeQS && typeof localeQS === 'string') {
//     locale = getLocale(localeQS);
//   }

//   try {
//     const { name, ...insertData } = await bodySchema.parseAsync({
//       ...req.body,
//       fkLastUserId: req.userId,
//       fkLastDependencyId: req.dependencyId,
//     });
//     const result = await db.insert(catIphCaseType).values({ [locale]: name, ...insertData });
//     res.status(HttpStatus.CREATED).json(result);
//   } catch (error) {
//     const { message, stack } = error as Error;
//     Logger.getInstance().error(message, {
//       stack,
//       method: 'iph_case_type - create',
//       source: req.headers['user-agent'],
//       module: 'catalogs',
//     });
//     if (error instanceof z.ZodError) {
//       const { errors } = error;
//       res.status(HttpStatus.BAD_REQUEST).json({ errors });
//       return;
//     }
//     res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
//   }
// };

// export const update = async (req: Request, res: Response) => {
//   const bodySchema = z.object({
//     key: z.string(),
//     name: z.string(),
//     description: z.string().optional(),
//     fkLastUserId: z.string(),
//     fkLastDependencyId: z.string(),
//   });

//   const { locale: localeQS } = req.query;
//   const { id } = req.params;

//   let locale = getLocale();

//   if (localeQS && typeof localeQS === 'string') {
//     locale = getLocale(localeQS);
//   }

//   try {
//     const { name, ...updateData } = await bodySchema.parseAsync({
//       ...req.body,
//       fkLastUserId: req.userId,
//       fkLastDependencyId: req.dependencyId,
//     });
//     const result = await db
//       .update(catIphCaseType)
//       .set({ ...updateData, [locale]: name })
//       .where(eq(catIphCaseType.id, id));
//     res.status(HttpStatus.OK).json(result);
//   } catch (error) {
//     const { message, stack } = error as Error;
//     Logger.getInstance().error(message, {
//       stack,
//       method: `iph_case_type/${id} - update`,
//       source: req.headers['user-agent'],
//       module: 'catalogs',
//     });
//     if (error instanceof z.ZodError) {
//       const { errors } = error;
//       res.status(HttpStatus.BAD_REQUEST).json({ errors });
//       return;
//     }
//     res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
//   }
// };

// export const deleteOne = async (req: Request, res: Response) => {
//   const { id } = req.params;
//   try {
//     const result = await db
//       .update(catIphCaseType)
//       .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
//       .where(eq(catIphCaseType.id, id));

//     res.status(HttpStatus.OK).json(result);
//   } catch (error) {
//     const { message, stack } = error as Error;
//     Logger.getInstance().error(message, {
//       stack,
//       method: `iph_case_type/${id} - deleteOne`,
//       source: req.headers['user-agent'],
//       module: 'catalogs',
//     });
//     res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
//   }
// };

// export const searchPaginated = async (req: Request, res: Response) => {
//   const {
//     lastId: lastIdQS,
//     limit: limitQS,
//     deleted: deletedQS,
//     disabled: disabledQS,
//     locale: localeQS,
//     q: qQS,
//   } = req.query;

//   let lastId = null;
//   let limit = 10;
//   let deleted = false;
//   let disabled = false;
//   let locale = getLocale();
//   let q = null;

//   if (lastIdQS && typeof lastIdQS === 'string') {
//     lastId = lastIdQS;
//   }
//   if (limitQS && typeof limitQS === 'string') {
//     limit = limitQS ? Number.parseInt(limitQS) : 10;
//   }
//   if (deletedQS && typeof deletedQS === 'string') {
//     deleted = deletedQS === 'true';
//   }
//   if (disabledQS && typeof disabledQS === 'string') {
//     disabled = disabledQS === 'true';
//   }
//   if (localeQS && typeof localeQS === 'string') {
//     locale = getLocale(localeQS);
//   }
//   if (qQS && typeof qQS === 'string') {
//     q = qQS;
//   }

//   try {
//     const filters: SQL[] = [];
//     if (!deleted) {
//       filters.push(eq(catIphCaseType.isDeleted, false));
//     }
//     if (!disabled) {
//       filters.push(eq(catIphCaseType.isEnabled, true));
//     }
//     if (q) {
//       filters.push(like(catIphCaseType[locale], `%${q}%`));
//     }

//     if (lastId) {
//       filters.push(gt(catIphCaseType.id, lastId));
//     }
//     const result = await db
//       .select({
//         id: catIphCaseType.id,
//         key: catIphCaseType.key,
//         name: catIphCaseType[locale],
//         description: catIphCaseType.description,
//       })
//       .from(catIphCaseType)
//       .where(and(...filters))
//       .limit(limit)
//       .orderBy(asc(catIphCaseType.id));

//     res.status(HttpStatus.OK).json(result);
//   } catch (error) {
//     const { message, stack } = error as Error;
//     Logger.getInstance().error(message, {
//       stack,
//       method: 'iph_case_type - searchPaginated',
//       source: req.headers['user-agent'],
//       module: 'catalogs',
//     });
//     res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
//   }
// };
