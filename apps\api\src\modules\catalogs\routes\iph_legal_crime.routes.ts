import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/iph_legal_crime.controller';

const catIphLegalCrimeRouter = Router();

catIphLegalCrimeRouter.get('/', getAll);
catIphLegalCrimeRouter.get('/search', searchPaginated);
catIphLegalCrimeRouter.get('/:id', getOne);
catIphLegalCrimeRouter.post('/', create);
catIphLegalCrimeRouter.put('/:id', update);
catIphLegalCrimeRouter.delete('/:id', deleteOne);

export default catIphLegalCrimeRouter;
