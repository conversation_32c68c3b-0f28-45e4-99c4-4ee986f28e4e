import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/commission.controller';

const commissionAssignRouter = Router();

commissionAssignRouter.get('/', getAll);
commissionAssignRouter.get('/search', searchPaginated);
commissionAssignRouter.get('/:id', getOne);
commissionAssignRouter.post('/', create);
commissionAssignRouter.put('/:id', update);
commissionAssignRouter.delete('/:id', deleteOne);

export default commissionAssignRouter;
