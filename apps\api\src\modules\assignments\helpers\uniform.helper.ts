import {
  systemModuleAssignmentUniform,
  systemModuleControlFatigue,
  systemModuleControlUniform,
  systemModuleUserKardexTacticalEquipment,
} from '@repo/shared-drizzle/schemas';
import { kardexTacticalEquipmentBodySchema } from '@/modules/kardex/helpers/kardex_tactical_equipment.helper';
import { and, desc, eq, isNull, ne, type ExtractTablesWithRelations } from 'drizzle-orm';
import type { NodePgQueryResultHKT } from 'drizzle-orm/node-postgres';
import type { PgTransaction } from 'drizzle-orm/pg-core';
import { z } from 'zod';

export const assignUniformBodySchema = z.object({
  fkUserId: z.string(),
  fkUniformId: z.string(),
  fkFatigueId: z.string(),
  assignmentDate: z.string(),
  assignmentEndDate: z.string().nullable().optional(),
  expirationDate: z.string().nullable().optional(),
  estimatedReturnDate: z.string().nullable().optional(),
  comments: z.string().nullable().optional(),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
export const validateDates = (body: any) => {
  const dateFields = [
    { field: 'assignmentDate', error: 'El campo assignmentDate no es una fecha valida' },
    { field: 'estimatedReturnDate', error: 'El campo estimatedReturnDate no es una fecha valida' },
    { field: 'expirationDate', error: 'El campo expirationDate no es una fecha valida' },
    { field: 'assignmentEndDate', error: 'El campo assignmentEndDate no es una fecha valida' },
  ];

  for (const df of dateFields) {
    if (body[df.field]) {
      const date = new Date(body[df.field].replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(date.getTime())) {
        throw new Error(df.error);
      }
    }
  }

  if (body.estimatedReturnDate && new Date(body.estimatedReturnDate) < new Date(body.assignmentDate)) {
    throw new Error('La fecha estimada de retorno no puede ser anterior a la fecha de inicio');
  }

  if (body.assignmentEndDate && new Date(body.assignmentEndDate) < new Date(body.assignmentDate)) {
    throw new Error('La fecha de fin de asignación no puede ser anterior a la fecha de inicio');
  }
};

export const validateUniformAssignmentInFatigue = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkUniformId: string,
  fkFatigueId: string,
  fkUniformAssignmentId: string,
) => {
  const activeAssignment = await trx
    .select()
    .from(systemModuleAssignmentUniform)
    .where(
      and(
        eq(systemModuleAssignmentUniform.fkUniformId, fkUniformId),
        eq(systemModuleAssignmentUniform.fkFatigueId, fkFatigueId),
        ne(systemModuleAssignmentUniform.id, fkUniformAssignmentId),
      ),
    )
    .limit(1);

  if (activeAssignment.length > 0) {
    throw new Error('Ya existe una asignación con el mismo uniforme en el despliegue indicado');
  }
};

export const validateUniformActiveAssignment = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkUniformId: string,
  fkUniformAssignmentId: string,
) => {
  const activeWeaponAssignment = await trx
    .select()
    .from(systemModuleAssignmentUniform)
    .leftJoin(systemModuleControlFatigue, eq(systemModuleAssignmentUniform.fkFatigueId, systemModuleControlFatigue.id))
    .where(
      and(
        eq(systemModuleControlFatigue.status, 'active'),
        eq(systemModuleAssignmentUniform.fkUniformId, fkUniformId),
        eq(systemModuleAssignmentUniform.isDeleted, false),
        ne(systemModuleAssignmentUniform.id, fkUniformAssignmentId),
      ),
    )
    .limit(1);
  if (activeWeaponAssignment.length > 0) {
    throw new Error('El uniform esta asignado a otro elemento en un despliegue activo');
  }
};

export const handleUserChangeInUniformAssign = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkUserId: string,
  fkUniformId: string,
  fkFatigueId: string,
  fkUniformAssignmentId: string,
) => {
  const existingAssignment = await trx
    .select()
    .from(systemModuleAssignmentUniform)
    .where(
      and(
        eq(systemModuleAssignmentUniform.fkUserId, fkUserId),
        eq(systemModuleAssignmentUniform.fkUniformId, fkUniformId),
        eq(systemModuleAssignmentUniform.fkFatigueId, fkFatigueId),
        ne(systemModuleAssignmentUniform.id, fkUniformAssignmentId), // Excluir la asignación actual
      ),
    )
    .limit(1);

  if (existingAssignment.length > 0) {
    throw new Error('El usuario ya tiene una asignación con este uniforme en el mismo despliegue');
  }
};

export const handleKardexUpdate = async (
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, never>, ExtractTablesWithRelations<Record<string, never>>>,
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  updateData: any,
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  assignment: any,
  Uniform: typeof systemModuleControlUniform.$inferInsert,
) => {
  const fieldsToCheck = ['assignmentDate', 'assignmentEndDate', 'estimatedReturnDate'] as const;
  const hasChanged = fieldsToCheck.some((field) => updateData[field] !== assignment[field]);

  if (hasChanged) {
    const lastKardex = await trx
      .select()
      .from(systemModuleUserKardexTacticalEquipment)
      .where(
        and(
          eq(systemModuleUserKardexTacticalEquipment.fkUserId, updateData.fkUserId),
          eq(systemModuleUserKardexTacticalEquipment.fkUniformId, updateData.fkUniformId),
          isNull(systemModuleUserKardexTacticalEquipment.assignmentEndDate),
        ),
      )
      .orderBy(desc(systemModuleUserKardexTacticalEquipment.createdAt))
      .limit(1);

    if (lastKardex.length > 0) {
      await trx
        .update(systemModuleUserKardexTacticalEquipment)
        .set({
          assignmentDate: updateData.assignmentDate,
          assignmentEndDate: updateData.assignmentEndDate,
          expirationDate: updateData.expirationDate,
          estimatedReturnDate: updateData.estimatedReturnDate,
          fkSizeId: Uniform.fkSizeId,
          tacticalEquipmentStatus:
            assignment.assignmentEndDate === null ? 'in_use' : lastKardex[0].tacticalEquipmentStatus,
          fkLastUserId: updateData.fkLastUserId,
          fkLastDependencyId: updateData.fkLastDependencyId,
        })
        .where(eq(systemModuleUserKardexTacticalEquipment.id, lastKardex[0].id));

      if (assignment.assignmentEndDate === null) {
        await trx
          .update(systemModuleControlUniform)
          .set({
            status: 'in_use',
            fkLastUserId: updateData.fkLastUserId,
            fkLastDependencyId: updateData.fkLastDependencyId,
          })
          .where(eq(systemModuleControlUniform.id, updateData.fkUniformId));
      }
    } else {
      const newKardex = await kardexTacticalEquipmentBodySchema.parseAsync({
        fkUserId: updateData.fkUserId,
        fkUniformId: Uniform.id,
        assignmentDate: updateData.assignmentDate,
        assignmentEndDate: updateData.assignmentEndDate,
        expirationDate: updateData.expirationDate,
        estimatedReturnDate: updateData.estimatedReturnDate,
        fkSizeId: Uniform.fkSizeId,
        UniformStatus: updateData.assignmentEndDate === null ? 'in_use' : 'ended_use',
        fkLastUserId: updateData.fkLastUserId,
        fkLastDependencyId: updateData.fkLastDependencyId,
      });

      await trx.insert(systemModuleUserKardexTacticalEquipment).values(newKardex);

      if (updateData.assignmentEndDate === null) {
        await trx
          .update(systemModuleControlUniform)
          .set({
            status: 'in_use',
            fkLastUserId: updateData.fkLastUserId,
            fkLastDependencyId: updateData.fkLastDependencyId,
          })
          .where(eq(systemModuleControlUniform.id, updateData.fkUniformId));
      }
    }
  }
};
