import type { Request, Response } from 'express';
import { getLocale } from '@/utils/locale_validator';
import { Logger } from '@/utils/logger';
import { ModuleName } from '@/utils/module_names';
import { HttpStatus } from '@/utils/http_status';
import db from '@/db';
import {
  catExternalInstitution,
  catPoliceJob,
  catSecondment,
  systemModuleControlCitizen,
  systemModuleControlIphDisposition,
  systemModuleControlIphFiles,
  systemModuleUser,
} from '@repo/shared-drizzle/schemas';
import type { Locale } from '@/utils/locale_validator';
import { and, asc, eq, sql } from 'drizzle-orm';

export const query = ({ locale, id }: { locale: Locale; id: string }) => {
  const s3Url = sql.raw(`${[process.env.AWS_S3_ENDPOINT, process.env.AWS_S3_BUCKET].join('/')}/`);
  return db
    .select({
      id: systemModuleControlIphDisposition.id,
      dispositionAt: systemModuleControlIphDisposition.dispositionAt,
      fileNumber: systemModuleControlIphDisposition.fileNumber,
      firstRespondent: {
        id: systemModuleUser.id,
        name: sql`concat(${systemModuleControlCitizen.name}, ' ', ${systemModuleControlCitizen.firstSurname}, ' ', ${systemModuleControlCitizen.secondSurname})`,
        position: sql`json_build_object('id', ${catPoliceJob.id}, 'name', ${catPoliceJob[locale]})`,
        secondment: sql`json_build_object('id', ${catSecondment.id}, 'name', ${catSecondment[locale]})`,
      },
      dispositionAuthority: {
        name: systemModuleControlIphDisposition.authorityName,
        firstSurname: systemModuleControlIphDisposition.authorityFirstSurname,
        secondSurname: systemModuleControlIphDisposition.authoritySecondSurname,
        position: systemModuleControlIphDisposition.authorityPosition,
        secondment: systemModuleControlIphDisposition.authoritySecondment,
        authority: sql`json_build_object('id', ${systemModuleControlIphDisposition.fkAuthorityInstitutionId}, 'name', ${catExternalInstitution[locale]})`,
      },
      file: {
        id: systemModuleControlIphFiles.id,
        url: sql`concat('${s3Url}',${systemModuleControlIphFiles.path})`,
        name: systemModuleControlIphFiles.name,
        contentType: systemModuleControlIphFiles.contentType,
        type: systemModuleControlIphFiles.type,
        number: systemModuleControlIphFiles.number,
      },
      updatedAt: systemModuleControlIphDisposition.updatedAt,
      createdAt: systemModuleControlIphDisposition.createdAt,
    })
    .from(systemModuleControlIphDisposition)
    .leftJoin(
      catExternalInstitution,
      eq(catExternalInstitution.id, systemModuleControlIphDisposition.fkAuthorityInstitutionId),
    )
    .leftJoin(systemModuleUser, eq(systemModuleUser.id, systemModuleControlIphDisposition.fkFirstRespondentUserId))
    .leftJoin(systemModuleControlCitizen, eq(systemModuleUser.fkCitizenId, systemModuleControlCitizen.id))
    .leftJoin(catPoliceJob, eq(systemModuleControlIphDisposition.fkFirstRespondentPositionId, catPoliceJob.id))
    .leftJoin(catSecondment, eq(systemModuleControlIphDisposition.fkFirstRespondentSecondmentId, catSecondment.id))
    .leftJoin(
      systemModuleControlIphFiles,
      and(
        eq(systemModuleControlIphFiles.fkIphId, systemModuleControlIphDisposition.id),
        eq(systemModuleControlIphFiles.type, 'pdf'),
        eq(systemModuleControlIphFiles.isDeleted, false),
      ),
    )
    .where(and(eq(systemModuleControlIphDisposition.id, id), eq(systemModuleControlIphDisposition.isDeleted, false)))
    .orderBy(asc(systemModuleControlIphDisposition.id))
    .limit(1);
};

export const get = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale: Locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const queryResult = await query({ locale, id });
    if (queryResult.length === 0) {
      res.status(HttpStatus.NOT_FOUND).json({ message: 'Not found' });
      return;
    }
    const result = queryResult[0];
    Object.assign(result, {
      files: result.file ? [result.file] : null,
      file: undefined,
    });
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      module: ModuleName.IPH,
      method: '/iph/section/disposition/get/:id - get',
      source: req.headers['user-agent'],
      stack,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};
