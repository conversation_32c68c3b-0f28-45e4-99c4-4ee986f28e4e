import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import {
  assignRadioBodySchema,
  handleUserChangeInRadioAssign,
  validateDates,
  validateRadioActiveAssignment,
  validateRadioAssignmentInFatigue,
} from '../helpers/radio.helper';
import SystemModuleCache from '@/utils/system_module_cache';
import {
  systemModuleAssignmentRadio,
  systemModuleControlFatigue,
  systemModuleControlRadio,
} from '@repo/shared-drizzle/schemas';
import moment from 'moment';

type RadioAssignGrouped = Record<
  string,
  {
    fkUserId: string;
    data: {
      id: string;
      fkControlRadioId: string;
      nationalRegistryNumber: string | null;
      fkFatigueId: string;
      assignmentDate: string;
      assignmentEndDate: string | null;
      estimatedReturnDate: string | null;
      periodUse: number | null;
      comments: string | null;
    }[];
  }
>;

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, grouped: groupedQS } = req.query;
  let deleted = false;
  let disabled = false;
  let grouped = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentRadio.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentRadio.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentRadio.id,
        fkUserId: systemModuleAssignmentRadio.fkUserId,
        fkControlRadioId: systemModuleAssignmentRadio.fkControlRadioId,
        nationalRegistryNumber: systemModuleControlRadio.nationalRegistryNumber,
        fkFatigueId: systemModuleAssignmentRadio.fkFatigueId,
        assignmentDate: systemModuleAssignmentRadio.assignmentDate,
        assignmentEndDate: systemModuleAssignmentRadio.assignmentEndDate,
        estimatedReturnDate: systemModuleAssignmentRadio.estimatedReturnDate,
        periodUse: systemModuleAssignmentRadio.periodUse,
        comments: systemModuleAssignmentRadio.comments,
      })
      .from(systemModuleAssignmentRadio)
      .leftJoin(systemModuleControlRadio, eq(systemModuleAssignmentRadio.fkControlRadioId, systemModuleControlRadio.id))
      .where(and(...filters));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkControlRadioId: rest.fkControlRadioId,
            nationalRegistryNumber: rest.nationalRegistryNumber || null,
            fkFatigueId: rest.fkFatigueId,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            estimatedReturnDate: rest.estimatedReturnDate || null,
            periodUse: rest.periodUse || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as RadioAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.RADIO}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleAssignmentRadio.id,
        fkUserId: systemModuleAssignmentRadio.fkUserId,
        fkControlRadioId: systemModuleAssignmentRadio.fkControlRadioId,
        nationalRegistryNumber: systemModuleControlRadio.nationalRegistryNumber,
        fkFatigueId: systemModuleAssignmentRadio.fkFatigueId,
        assignmentDate: systemModuleAssignmentRadio.assignmentDate,
        assignmentEndDate: systemModuleAssignmentRadio.assignmentEndDate,
        estimatedReturnDate: systemModuleAssignmentRadio.estimatedReturnDate,
        periodUse: systemModuleAssignmentRadio.periodUse,
        comments: systemModuleAssignmentRadio.comments,
      })
      .from(systemModuleAssignmentRadio)
      .leftJoin(systemModuleControlRadio, eq(systemModuleAssignmentRadio.fkControlRadioId, systemModuleControlRadio.id))
      .where(eq(systemModuleAssignmentRadio.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.RADIO}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar assignmentDate
    const date = new Date(req.body.assignmentDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('El campo assignmentDate no es una fecha valida');
    }

    // Validar estimatedReturnDate
    if (req.body.estimatedReturnDate) {
      const dateEnd = new Date(req.body.estimatedReturnDate.replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(dateEnd.getTime())) {
        throw new Error('El campo estimatedReturnDate no es una fecha valida');
      }
      if (dateEnd < date) {
        throw new Error('La fecha estimada de retorno no puede ser anterior a la fecha de inicio');
      }
    }

    // Quitar del payload el campo assignmentEndDate, no considerar al crear la asignación
    const { assignmentEndDate, ...rest } = req.body;

    // Validar payload
    const insertData = await assignRadioBodySchema.parseAsync({ ...rest });
    const result = await db.transaction(async (trx) => {
      // Validar la existencia del radio
      const radio = await trx
        .select()
        .from(systemModuleControlRadio)
        .where(eq(systemModuleControlRadio.id, insertData.fkControlRadioId))
        .limit(1);
      if (radio.length === 0) {
        throw new Error('Radio no encontrado');
      }

      // Validar que no exista una asignación con el mismo radio en el despliegue indicado
      const existingAssignment = await trx
        .select()
        .from(systemModuleAssignmentRadio)
        .where(
          and(
            eq(systemModuleAssignmentRadio.fkControlRadioId, insertData.fkControlRadioId),
            eq(systemModuleAssignmentRadio.fkFatigueId, insertData.fkFatigueId),
          ),
        )
        .limit(1);
      if (existingAssignment.length > 0) {
        throw new Error('Ya existe una asignación con el mismo radio en el despliegue indicado');
      }

      const systemModuleCache = SystemModuleCache.getInstance();
      const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_ASIGNACIONES_RADIOS);
      if (!systemModule) {
        throw new Error('System module CTRL_ASIGNACIONES_RADIOS not found');
      }
      const newAssignment = await trx.insert(systemModuleAssignmentRadio).values({
        ...insertData,
        assignmentEndDate: null,
        fkSystemModuleId: systemModule.id,
      });
      return newAssignment;
    });

    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.RADIO} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar fechas
    validateDates(req.body);

    // Validar payload
    const updateData = await assignRadioBodySchema.parseAsync({ ...req.body });
    const result = await db.transaction(async (trx) => {
      // Obtener registro de asignación
      const assignment = await trx
        .select()
        .from(systemModuleAssignmentRadio)
        .where(eq(systemModuleAssignmentRadio.id, id))
        .limit(1);
      if (assignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar la existencia del radio
      const radio = await trx
        .select()
        .from(systemModuleControlRadio)
        .where(eq(systemModuleControlRadio.id, updateData.fkControlRadioId))
        .limit(1);
      if (radio.length === 0) {
        throw new Error('Radio no encontrado');
      }

      // Validar si cambio de radio, que no este en el mismo despliegue ya asignado
      if (updateData.fkControlRadioId !== assignment[0].fkControlRadioId) {
        await validateRadioAssignmentInFatigue(trx, updateData.fkControlRadioId, updateData.fkFatigueId, id);
      }

      // Validar si el radio tiene una asignación activa
      await validateRadioActiveAssignment(trx, updateData.fkControlRadioId, updateData.fkFatigueId);

      // Verificar si el usuario cambió
      if (updateData.fkUserId !== assignment[0].fkUserId) {
        await handleUserChangeInRadioAssign(
          trx,
          updateData.fkUserId,
          updateData.fkControlRadioId,
          updateData.fkFatigueId,
          id,
        );
      }

      // Calcular el periodo de uso si se proporciona assignmentEndDate
      let periodUse = assignment[0].periodUse;
      if (updateData.assignmentEndDate) {
        const diffDays = moment(updateData.assignmentEndDate).diff(moment(updateData.assignmentDate), 'days');
        periodUse = diffDays;
      }

      // Actualizar la asignación
      const assignUpdate = await trx
        .update(systemModuleAssignmentRadio)
        .set({ ...updateData, periodUse })
        .where(eq(systemModuleAssignmentRadio.id, id));

      return assignUpdate;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.RADIO}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (tx) => {
      // Validar que exista la asignación
      const existingAssignment = await tx
        .select()
        .from(systemModuleAssignmentRadio)
        .where(eq(systemModuleAssignmentRadio.id, id))
        .limit(1);
      if (existingAssignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar que la asignación no este en un despliegue activo
      const activeRadioAssignment = await tx
        .select()
        .from(systemModuleAssignmentRadio)
        .leftJoin(
          systemModuleControlFatigue,
          eq(systemModuleAssignmentRadio.fkFatigueId, systemModuleControlFatigue.id),
        )
        .where(
          and(
            eq(systemModuleControlFatigue.status, 'active'),
            eq(systemModuleAssignmentRadio.fkFatigueId, existingAssignment[0].fkFatigueId),
            eq(systemModuleAssignmentRadio.id, id),
          ),
        )
        .limit(1);
      if (activeRadioAssignment.length > 0) {
        throw new Error('No es posible eliminar la asignación si este ya se encuentra en un despliegue activo');
      }

      const deleteResult = await tx
        .update(systemModuleAssignmentRadio)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleAssignmentRadio.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.RADIO}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    q: qQS,
    grouped: groupedQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;
  let grouped = false;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentRadio.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentRadio.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleControlRadio.nationalRegistryNumber, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentRadio.assignmentDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentRadio.assignmentEndDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentRadio.estimatedReturnDate}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleAssignmentRadio.comments, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleAssignmentRadio.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentRadio.id,
        fkUserId: systemModuleAssignmentRadio.fkUserId,
        fkControlRadioId: systemModuleAssignmentRadio.fkControlRadioId,
        nationalRegistryNumber: systemModuleControlRadio.nationalRegistryNumber,
        fkFatigueId: systemModuleAssignmentRadio.fkFatigueId,
        assignmentDate: systemModuleAssignmentRadio.assignmentDate,
        assignmentEndDate: systemModuleAssignmentRadio.assignmentEndDate,
        estimatedReturnDate: systemModuleAssignmentRadio.estimatedReturnDate,
        periodUse: systemModuleAssignmentRadio.periodUse,
        comments: systemModuleAssignmentRadio.comments,
      })
      .from(systemModuleAssignmentRadio)
      .leftJoin(systemModuleControlRadio, eq(systemModuleAssignmentRadio.fkControlRadioId, systemModuleControlRadio.id))
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleAssignmentRadio.id));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkControlRadioId: rest.fkControlRadioId,
            nationalRegistryNumber: rest.nationalRegistryNumber || null,
            fkFatigueId: rest.fkFatigueId,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            estimatedReturnDate: rest.estimatedReturnDate || null,
            periodUse: rest.periodUse || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as RadioAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.RADIO} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
