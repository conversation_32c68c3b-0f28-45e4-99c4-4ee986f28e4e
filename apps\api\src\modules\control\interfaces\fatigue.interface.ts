import type { ShiftAssign } from '@repo/shared-drizzle/schemas';
import type { Weapon } from './weapon.interface';
import type { Uniform } from './uniform.interface';
import type { TacticalEquipment } from './tactical_equipment.interface';
import type { excludeFields } from '@/utils/default_values';

interface FatigueBaseData {
  id: string;
  startDateTime: string;
  assignedQuantity: number;
  assignedUsers: Omit<ShiftAssign, 'fkSystemModuleId'>[];
  isClosed: boolean;
  closedAt: Date | null;
  isTemplate: boolean;
  comments: string | null;
}
export interface FatigueStatusData extends FatigueBaseData {
  fkShiftId: string;
  shiftName: string | null;
}

export interface FatigueShiftData extends FatigueBaseData {
  status: string;
}

export interface ShiftAssignMetadata {
  message: string;
  submittedUsers: Omit<ShiftAssign, excludeFields>[];
  assignedUsers: (Omit<ShiftAssign, excludeFields> & { fkFatigueAssignId: string | null })[];
  unassignedUsers: Omit<ShiftAssign, excludeFields>[];
  usersByShift: UsersByShiftType;
}

export type UsersByShiftType = Record<
  string,
  {
    user: { id: string; alias: string };
    employment: { id: string; fkShiftId: string | null; cup: string } | null;
  }[]
>;

export interface KardexRecord {
  fkUserId: string;
  weaponAssignments: RawWeaponAssignment[] | null;
  uniformAssignments: RawUniformAssignment[] | null;
  tacticalEquipmentAssignments: RawTacticalEquipmentAssignment[] | null;
}

interface RawWeaponAssignment {
  fkUserId: string;
  weaponData: Weapon;
  assignmentId: string;
}

interface RawUniformAssignment {
  fkUserId: string;
  uniformData: Uniform;
  assignmentId: string;
}

interface RawTacticalEquipmentAssignment {
  fkUserId: string;
  tacticalEquipmentData: TacticalEquipment;
  assignmentId: string;
}
