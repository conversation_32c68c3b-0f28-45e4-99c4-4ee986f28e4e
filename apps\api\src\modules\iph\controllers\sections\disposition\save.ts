import db from '@/db';
import {
  catExternalInstitution,
  type Disposition,
  systemModuleControlIphDisposition,
} from '@repo/shared-drizzle/schemas';
import genrateKey from '@/utils/generate_key';
import { HttpStatus } from '@/utils/http_status';
import { getLocale } from '@/utils/locale_validator';
import { dispositionSchema } from '@repo/types/schemas';
import type { Request, Response } from 'express';
import z from 'zod';

const saveDisposition = async (req: Request, res: Response) => {
  const { locale: localeQS } = req.query;
  let locale = getLocale();
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  const userId = req.userId as string;
  const dependencyId = req.dependencyId as string;
  const error: { [key: string]: z.ZodError } = {};
  try {
    const dataToUpdate: Disposition = {
      fkLastUserId: userId,
      fkLastDependencyId: dependencyId,
      updatedAt: new Date(),
    };
    const unsafeBody = req.body as z.infer<typeof dispositionSchema>;
    // validate fields individually
    // dispositionAt
    const dispositionAt = dispositionSchema.shape.dispositionAt.safeParse(unsafeBody.dispositionAt);
    if (dispositionAt.success) {
      dataToUpdate.dispositionAt = dispositionAt.data;
    } else {
      error.dispositionAt = dispositionAt.error;
    }
    // fileNumber
    const fileNumber = dispositionSchema.shape.fileNumber.safeParse(unsafeBody.fileNumber);
    if (fileNumber.success) {
      dataToUpdate.fileNumber = fileNumber.data;
    } else {
      error.fileNumber = fileNumber.error;
    }
    // dispositionUser
    const firstRespondent = dispositionSchema.shape.firstRespondent.safeParse(unsafeBody.firstRespondent);
    if (firstRespondent.success) {
      if (firstRespondent.data) {
        dataToUpdate.fkFirstRespondentUserId = firstRespondent.data.id;
        dataToUpdate.fkFirstRespondentPositionId = firstRespondent.data.position?.id;
        dataToUpdate.fkFirstRespondentSecondmentId = firstRespondent.data.secondment?.id;
      }
    } else {
      error.firstRespondent = firstRespondent.error;
    }
    // dispositionAuthority
    const dispositionAuthority = dispositionSchema.shape.dispositionAuthority.safeParse(
      unsafeBody.dispositionAuthority,
    );
    if (dispositionAuthority.success) {
      if (dispositionAuthority.data) {
        dataToUpdate.authorityName = dispositionAuthority.data.name;
        dataToUpdate.authorityFirstSurname = dispositionAuthority.data.firstSurname;
        dataToUpdate.authoritySecondSurname = dispositionAuthority.data.secondSurname;
        dataToUpdate.authorityPosition = dispositionAuthority.data.position;
        dataToUpdate.authoritySecondment = dispositionAuthority.data.secondment;
        if (dispositionAuthority.data.authority) {
          let id: string | undefined = dispositionAuthority.data.authority.id;
          if (dispositionAuthority.data.authority.action === 'create') {
            const result = await db.insert(catExternalInstitution).values({
              id: dispositionAuthority.data.authority.id,
              key: genrateKey(dispositionAuthority.data.authority.name),
              [locale]: dispositionAuthority.data.authority.name,
              fkLastUserId: userId,
              fkLastDependencyId: dependencyId,
            });
            if (result.rowCount === 0) {
              error.transaction = z.ZodError.create([
                {
                  fatal: true,
                  message: 'Error creating institution',
                  code: z.ZodIssueCode.custom,
                  path: ['dispositionAuthority', 'authority'],
                },
              ]);
              id = undefined;
            }
          }
          dataToUpdate.fkAuthorityInstitutionId = id;
        }
      }
    } else {
      error.dispositionAuthority = dispositionAuthority.error;
    }
    // id
    const id = dispositionSchema.shape.id.safeParse(unsafeBody.id);
    if (id.success && id.data) {
      dataToUpdate.id = id.data;
    }

    if (Object.keys(dataToUpdate).length <= 3) {
      // if there is no data to update more than the 4 required fields return Not Modified status
      res.status(HttpStatus.NOT_MODIFIED).json({ message: 'No data to update', error });
      return;
    }
    const updated = await db.insert(systemModuleControlIphDisposition).values(dataToUpdate).onConflictDoUpdate({
      target: systemModuleControlIphDisposition.id,
      set: dataToUpdate,
    });

    res.status(HttpStatus.OK).json({ error, success: (updated.rowCount || 0) > 0 });
  } catch (_error) {
    const err = _error as Error;
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message: err, error });
  }
};

export default saveDisposition;
