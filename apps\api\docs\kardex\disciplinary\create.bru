meta {
  name: create
  type: http
  seq: 5
}

post {
  url: {{base}}/kardex/disciplinary?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

body:json {
  {
    "fkUserId": "ID Usuario",
    "fkTypeDisciplinaryOffenseId": "ID Tipo de Falta Cometida",
    "disciplinaryOffenseDate": "Fecha en que ocurrio la falta",
    "fkSanctionAppliedId": "ID Sancion Aplicada",
    "sanctionAppliedDate": "Fecha en que se aplico la sanción",
    "observations": "Observaciones" // Opcional
  }
}
