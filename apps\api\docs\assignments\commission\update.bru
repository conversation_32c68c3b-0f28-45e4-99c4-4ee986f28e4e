meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/assignments/commission/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 
}

body:json {
  {
    "fkUserId": "ID Usuario",
    "fkCommissionId": "ID Comisión",
    "fkFatigueId": "ID Fatiga/Despliegue",
    "isApproved": true, // Aprovación o no de la asignación de la comisión
    "startedAt": "Fecha inicio comisión",
    "endedAt": "Fecha fin comisión", // Opcional
    "comments": "Comentarios o Notas" // Opcional
  }
}
