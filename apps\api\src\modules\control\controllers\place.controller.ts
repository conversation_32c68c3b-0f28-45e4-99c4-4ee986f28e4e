import type { Request, Response } from 'express';
import { catSettlement, catMunicipality, catState, systemModuleControlPlaces } from '@repo/shared-drizzle/schemas';
import db from '@/db';
import { and, asc, eq, gt, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { getLocale } from '@/utils/locale_validator';
import { HttpStatus } from '@/utils/http_status';
import type { IPlace } from '../interfaces/place.interface';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import SystemModuleCache from '@/utils/system_module_cache';
import { placeBodySchema } from '../helpers/place.helper';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS } = req.query;
  let [deleted, disabled, locale] = [false, false, getLocale()];

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlPlaces.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlPlaces.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlPlaces.id,
        name: systemModuleControlPlaces.name,
        fkStateId: systemModuleControlPlaces.fkStateId,
        nameState: catState[locale],
        fkMunicipalityId: systemModuleControlPlaces.fkMunicipalityId,
        nameMunicipality: catMunicipality[locale],
        fkColonyId: systemModuleControlPlaces.fkColonyId,
        nameColony: catSettlement[locale],
        zipCode: systemModuleControlPlaces.zipCode,
        street: systemModuleControlPlaces.street,
        number: systemModuleControlPlaces.number,
        interiorNumber: systemModuleControlPlaces.interiorNumber,
        betweenStreet1: systemModuleControlPlaces.betweenStreet1,
        betweenStreet2: systemModuleControlPlaces.betweenStreet2,
        reference: systemModuleControlPlaces.reference,
        location: systemModuleControlPlaces.location,
        sectors: systemModuleControlPlaces.sectors,
      })
      .from(systemModuleControlPlaces)
      .leftJoin(catState, eq(systemModuleControlPlaces.fkStateId, catState.id))
      .leftJoin(catMunicipality, eq(systemModuleControlPlaces.fkMunicipalityId, catMunicipality.id))
      .leftJoin(catSettlement, eq(systemModuleControlPlaces.fkColonyId, catSettlement.id))
      .where(and(...filters));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'place/all - getAll',
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const result = await db
      .select({
        id: systemModuleControlPlaces.id,
        name: systemModuleControlPlaces.name,
        fkStateId: systemModuleControlPlaces.fkStateId,
        nameState: catState[locale],
        fkMunicipalityId: systemModuleControlPlaces.fkMunicipalityId,
        nameMunicipality: catMunicipality[locale],
        fkColonyId: systemModuleControlPlaces.fkColonyId,
        nameColony: catSettlement[locale],
        zipCode: systemModuleControlPlaces.zipCode,
        street: systemModuleControlPlaces.street,
        number: systemModuleControlPlaces.number,
        interiorNumber: systemModuleControlPlaces.interiorNumber,
        betweenStreet1: systemModuleControlPlaces.betweenStreet1,
        betweenStreet2: systemModuleControlPlaces.betweenStreet2,
        reference: systemModuleControlPlaces.reference,
        location: systemModuleControlPlaces.location,
        sectors: systemModuleControlPlaces.sectors,
      })
      .from(systemModuleControlPlaces)
      .leftJoin(catState, eq(systemModuleControlPlaces.fkStateId, catState.id))
      .leftJoin(catMunicipality, eq(systemModuleControlPlaces.fkMunicipalityId, catMunicipality.id))
      .leftJoin(catSettlement, eq(systemModuleControlPlaces.fkColonyId, catSettlement.id))
      .where(eq(systemModuleControlPlaces.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `place/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const insertData = await placeBodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_LUGARES);
    if (!systemModule) {
      throw new Error('System module CTRL_LUGARES not found');
    }
    const result = await db.insert(systemModuleControlPlaces).values({
      ...insertData,
      fkSystemModuleId: systemModule.id,
    });

    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'place - create',
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  const {
    name,
    fkStateId,
    fkMunicipalityId,
    fkColonyId,
    zipCode,
    street,
    number,
    interiorNumber,
    betweenStreet1,
    betweenStreet2,
    reference,
    location,
    sectors,
  }: IPlace = req.body;
  try {
    const result = await db
      .update(systemModuleControlPlaces)
      .set({
        name,
        fkStateId,
        fkMunicipalityId,
        fkColonyId,
        zipCode,
        street,
        number,
        interiorNumber,
        betweenStreet1,
        betweenStreet2,
        reference,
        location,
        sectors,
        fkLastUserId: req.userId,
        fkLastDependencyId: req.dependencyId,
      })
      .where(eq(systemModuleControlPlaces.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `place/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .update(systemModuleControlPlaces)
      .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
      .where(eq(systemModuleControlPlaces.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `place/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    locale: localeQS,
    q: qQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlPlaces.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlPlaces.isEnabled, true));
    }
    if (q) {
      likeFilters.push(
        like(
          sql`normalize_text(coalesce(${systemModuleControlPlaces.name}) || ' ' || coalesce(${systemModuleControlPlaces.zipCode}) || ' ' || coalesce(${systemModuleControlPlaces.street}) ' ' || coalesce(${systemModuleControlPlaces.reference}) ' ' || coalesce(${systemModuleControlPlaces.betweenStreet1}) ' ' || coalesce(${systemModuleControlPlaces.betweenStreet2}))`,
          `%${q}%`,
        ),
      );
      likeFilters.push(like(sql`${catState[locale]}::text`, `%${q}%`));
      likeFilters.push(like(sql`${catMunicipality[locale]}::text`, `%${q}%`));
      likeFilters.push(like(sql`${catSettlement[locale]}::text`, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleControlPlaces.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlPlaces.id,
        name: systemModuleControlPlaces.name,
        fkStateId: systemModuleControlPlaces.fkStateId,
        nameState: catState[locale],
        fkMunicipalityId: systemModuleControlPlaces.fkMunicipalityId,
        nameMunicipality: catMunicipality[locale],
        fkColonyId: systemModuleControlPlaces.fkColonyId,
        nameColony: catSettlement[locale],
        zipCode: systemModuleControlPlaces.zipCode,
        street: systemModuleControlPlaces.street,
        number: systemModuleControlPlaces.number,
        interiorNumber: systemModuleControlPlaces.interiorNumber,
        betweenStreet1: systemModuleControlPlaces.betweenStreet1,
        betweenStreet2: systemModuleControlPlaces.betweenStreet2,
        reference: systemModuleControlPlaces.reference,
        location: systemModuleControlPlaces.location,
        sectors: systemModuleControlPlaces.sectors,
      })
      .from(systemModuleControlPlaces)
      .leftJoin(catState, eq(systemModuleControlPlaces.fkStateId, catState.id))
      .leftJoin(catMunicipality, eq(systemModuleControlPlaces.fkMunicipalityId, catMunicipality.id))
      .leftJoin(catSettlement, eq(systemModuleControlPlaces.fkColonyId, catSettlement.id))
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlPlaces.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'place - searchPaginated',
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
