import type { Request, Response } from 'express';
import { catSettlement, catMunicipality, catState } from '@repo/shared-drizzle/schemas';
import db from '@/db';
import { and, asc, eq, gt, like, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { getLocale } from '@/utils/locale_validator';
import { HttpStatus } from '@/utils/http_status';
import { z } from 'zod';

export const getByZipCode = async (req: Request, res: Response) => {
  const { zip: zipParam } = req.params;
  const zipSchema = z
    .string()
    .regex(/^\d+$/, 'Zip code must be a number')
    .nonempty('Zip code is required')
    .length(5, 'Zip code must be 5 digits');
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS } = req.query;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const zip = zipSchema.parse(zipParam);
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(catSettlement.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(catSettlement.isEnabled, true));
    }

    const result = await db
      .select({
        id: catSettlement.id,
        key: catSettlement.key,
        name: catSettlement[locale],
        description: catSettlement.description,
        state: {
          id: catSettlement.fkStateId,
          name: catState[locale],
        },
        municipality: {
          id: catSettlement.fkMunicipalityId,
          name: catMunicipality[locale],
        },
        zipCode: catSettlement.zipCode,
      })
      .from(catSettlement)
      .leftJoin(catState, eq(catSettlement.fkStateId, catState.id))
      .leftJoin(catMunicipality, eq(catSettlement.fkMunicipalityId, catMunicipality.id))
      .where(eq(catSettlement.zipCode, zip));
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'settlement/:zip - getByZipCode',
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const getByStateAndMunicipality = async (req: Request, res: Response) => {
  const { state: stateParam, municipality: municipalityParam } = req.params;
  const stateSchema = z.string().uuid();
  const municipalitySchema = z.string().uuid();
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS } = req.query;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const state = stateSchema.parse(stateParam);
    const municipality = municipalitySchema.parse(municipalityParam);
    const filters: SQL[] = [eq(catSettlement.fkStateId, state), eq(catSettlement.fkMunicipalityId, municipality)];

    if (!deleted) {
      filters.push(eq(catSettlement.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(catSettlement.isEnabled, true));
    }

    const result = await db
      .select({
        id: catSettlement.id,
        key: catSettlement.key,
        name: catSettlement[locale],
        description: catSettlement.description,
        state: {
          id: catSettlement.fkStateId,
          name: catState[locale],
        },
        municipality: {
          id: catSettlement.fkMunicipalityId,
          name: catMunicipality[locale],
        },
        zipCode: catSettlement.zipCode,
      })
      .from(catSettlement)
      .leftJoin(catState, eq(catSettlement.fkStateId, catState.id))
      .leftJoin(catMunicipality, eq(catSettlement.fkMunicipalityId, catMunicipality.id))
      .where(and(...filters));
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'settlement/:state/:municipality - getByStateAndMunicipality',
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const result = await db
      .select({
        id: catSettlement.id,
        key: catSettlement.key,
        name: catSettlement[locale],
        description: catSettlement.description,
        fkStateId: catSettlement.fkStateId,
        stateName: catState[locale],
        fkMunicipalityId: catSettlement.fkMunicipalityId,
        municipalityName: catMunicipality[locale],
        zipCode: catSettlement.zipCode,
      })
      .from(catSettlement)
      .where(eq(catSettlement.id, id))
      .leftJoin(catState, eq(catSettlement.fkStateId, catState.id))
      .leftJoin(catMunicipality, eq(catSettlement.fkMunicipalityId, catMunicipality.id));
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `settlement/${id} - getOne`,
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const create = async (req: Request, res: Response) => {
  const bodySchema = z.object({
    key: z.string(),
    name: z.string(),
    description: z.string().optional(),
    zipCode: z.string(),
    fkStateId: z.string(),
    fkMunicipalityId: z.string(),
    fkLastUserId: z.string(),
    fkLastDependencyId: z.string(),
  });

  const { locale: localeQS } = req.query;

  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const { name, ...insertData } = await bodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const result = await db.insert(catSettlement).values({ [locale]: name, ...insertData });
    res.status(HttpStatus.CREATED).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'settlement - create',
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const update = async (req: Request, res: Response) => {
  const bodySchema = z.object({
    key: z.string(),
    name: z.string(),
    description: z.string().optional(),
    zipCode: z.string(),
    fkStateId: z.string(),
    fkMunicipalityId: z.string(),
    fkLastUserId: z.string(),
    fkLastDependencyId: z.string(),
  });

  const { locale: localeQS } = req.query;
  const { id } = req.params;

  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const { name, ...updateData } = await bodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const result = await db
      .update(catSettlement)
      .set({ ...updateData, [locale]: name })
      .where(eq(catSettlement.id, id));
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `settlement/${id} - update`,
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .update(catSettlement)
      .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
      .where(eq(catSettlement.id, id));

    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `settlement/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const searchByZipPaginated = async (req: Request, res: Response) => {
  const zipSchema = z
    .string()
    .regex(/^\d+$/, 'Zip code must be a number')
    .nonempty('Zip code is required')
    .length(5, 'Zip code must be 5 digits');
  const { zip: zipParam } = req.params;
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    locale: localeQS,
    q: qQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const zip = zipSchema.parse(zipParam);
    const filters: SQL[] = [eq(catSettlement.zipCode, zip)];

    if (!deleted) {
      filters.push(eq(catSettlement.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(catSettlement.isEnabled, true));
    }
    if (q) {
      filters.push(like(sql`normalize_text(${catSettlement[locale]})`, sql`'%' || normalize_text(${q}) || '%'`));
    }

    if (lastId) {
      filters.push(gt(catSettlement.id, lastId));
    }
    const result = await db
      .select({
        id: catSettlement.id,
        key: catSettlement.key,
        name: catSettlement[locale],
        description: catSettlement.description,
        state: {
          id: catSettlement.fkStateId,
          name: catState[locale],
        },
        municipality: {
          id: catSettlement.fkMunicipalityId,
          name: catMunicipality[locale],
        },
        zipCode: catSettlement.zipCode,
      })
      .from(catSettlement)
      .leftJoin(catState, eq(catSettlement.fkStateId, catState.id))
      .leftJoin(catMunicipality, eq(catSettlement.fkMunicipalityId, catMunicipality.id))
      .where(and(...filters))
      .limit(limit)
      .orderBy(asc(catSettlement.id));

    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'settlement/:zip/search - searchByZipPaginated',
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const searchByStateAndMunicipalityPaginated = async (req: Request, res: Response) => {
  const { state: stateParam, municipality: municipalityParam } = req.params;
  const stateSchema = z.string().uuid();
  const municipalitySchema = z.string().uuid();

  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    locale: localeQS,
    q: qQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const state = stateSchema.parse(stateParam);
    const municipality = municipalitySchema.parse(municipalityParam);

    const filters: SQL[] = [eq(catSettlement.fkStateId, state), eq(catSettlement.fkMunicipalityId, municipality)];

    if (!deleted) {
      filters.push(eq(catSettlement.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(catSettlement.isEnabled, true));
    }
    if (q) {
      filters.push(like(sql`normalize_text(${catSettlement[locale]})`, sql`'%' || normalize_text(${q}) || '%'`));
    }

    if (lastId) {
      filters.push(gt(catSettlement.id, lastId));
    }
    const result = await db
      .select({
        id: catSettlement.id,
        key: catSettlement.key,
        name: catSettlement[locale],
        description: catSettlement.description,
        state: {
          id: catSettlement.fkStateId,
          name: catState[locale],
        },
        municipality: {
          id: catSettlement.fkMunicipalityId,
          name: catMunicipality[locale],
        },
        zipCode: catSettlement.zipCode,
      })
      .from(catSettlement)
      .leftJoin(catState, eq(catSettlement.fkStateId, catState.id))
      .leftJoin(catMunicipality, eq(catSettlement.fkMunicipalityId, catMunicipality.id))
      .where(and(...filters))
      .limit(limit)
      .orderBy(asc(catSettlement.id));

    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'settlement/:state:/municipality/search - searchByStateAndMunicipalityPaginated',
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};
