import {
  systemModuleAssignmentUnit,
  systemModuleControlFatigue,
  systemModuleControlVehicleUnit,
  systemModuleUser,
} from '@repo/shared-drizzle/schemas';
import { and, eq, ne, not, type ExtractTablesWithRelations } from 'drizzle-orm';
import type { NodePgQueryResultHKT } from 'drizzle-orm/node-postgres';
import type { PgTransaction } from 'drizzle-orm/pg-core';
import { z } from 'zod';
import type { UnitAssignment } from '../interfaces/unit.interface';
import SystemModuleCache from '@/utils/system_module_cache';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import moment from 'moment';

export const assignUnitBodySchema = z.object({
  fkUserId: z.string(),
  fkUnitControlId: z.string(),
  fkFatigueId: z.string(),
  isDriver: z.boolean().default(false),
  isPassenger: z.boolean().default(false),
  isInCharge: z.boolean().default(false),
  assignmentDate: z.string(),
  assignmentEndDate: z.string().nullable().optional(),
  expirationDate: z.string().nullable().optional(),
  estimatedReturnDate: z.string().nullable().optional(),
  comments: z.string().nullable().optional(),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});

export const processUnitAssignment = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  unitData: UnitAssignment,
  isCreated: boolean,
  fkUnitAssignmentId: string | null,
) => {
  // Validar payload
  const insertData = await assignUnitBodySchema.parseAsync({ ...unitData });

  // Validación de roles
  if (!insertData.isDriver && !insertData.isPassenger) {
    throw new Error('Debe especificar al menos un rol como conductor (isDriver) o pasajero (isPassenger)');
  }

  // Eliminar valores falsos o undefined
  const existingAssignmentConditions = [
    eq(systemModuleAssignmentUnit.fkUnitControlId, insertData.fkUnitControlId),
    eq(systemModuleAssignmentUnit.fkUserId, insertData.fkUserId),
    eq(systemModuleAssignmentUnit.fkFatigueId, insertData.fkFatigueId),
    fkUnitAssignmentId ? not(eq(systemModuleAssignmentUnit.id, fkUnitAssignmentId)) : undefined,
  ].filter(Boolean);

  // Validaciones de existencia (todas las relaciones)
  const [vehicle, userExists, fatigueExists, existingAssignment] = await Promise.all([
    trx
      .select()
      .from(systemModuleControlVehicleUnit)
      .where(eq(systemModuleControlVehicleUnit.id, insertData.fkUnitControlId))
      .limit(1),
    trx.select().from(systemModuleUser).where(eq(systemModuleUser.id, insertData.fkUserId)).limit(1),
    trx
      .select()
      .from(systemModuleControlFatigue)
      .where(eq(systemModuleControlFatigue.id, insertData.fkFatigueId))
      .limit(1),
    trx
      .select()
      .from(systemModuleAssignmentUnit)
      .where(and(...existingAssignmentConditions))
      .limit(1),
  ]);

  if (vehicle.length === 0) throw new Error('Unidad no encontrado');
  if (userExists.length === 0) throw new Error('Usuario no encontrado');
  if (fatigueExists.length === 0) throw new Error('Despliegue no encontrada');
  if (existingAssignment.length > 0)
    throw new Error('Ya existe una asignación del elemento con la misma unidad en el despliegue indicado');

  // Validar que solo exista un condutor de la unidad por despliegue
  if (insertData.isDriver) {
    const existingAssignmentDriver = await trx
      .select()
      .from(systemModuleAssignmentUnit)
      .where(
        and(
          eq(systemModuleAssignmentUnit.fkFatigueId, insertData.fkFatigueId),
          eq(systemModuleAssignmentUnit.isDriver, true),
        ),
      )
      .limit(1);

    if (existingAssignmentDriver.length > 0) {
      throw new Error('Ya existe un conductor asignado a la unidad en el despliegue indicado');
    }
  }

  if (isCreated) {
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_ASIGNACIONES_UNIDADES);
    if (!systemModule) {
      throw new Error('System module CTRL_ASIGNACIONES_UNIDADES not found');
    }

    const newAssignment = await trx
      .insert(systemModuleAssignmentUnit)
      .values({
        ...insertData,
        assignmentEndDate: null,
        fkSystemModuleId: systemModule.id,
      })
      .returning({
        id: systemModuleAssignmentUnit.id,
        fkUserId: systemModuleAssignmentUnit.fkUserId,
        fkUnitControlId: systemModuleAssignmentUnit.fkUnitControlId,
        fkFatigueId: systemModuleAssignmentUnit.fkFatigueId,
        isDriver: systemModuleAssignmentUnit.isDriver,
        isPassenger: systemModuleAssignmentUnit.isPassenger,
        isInCharge: systemModuleAssignmentUnit.isInCharge,
        assignmentDate: systemModuleAssignmentUnit.assignmentDate,
        assignmentEndDate: systemModuleAssignmentUnit.assignmentEndDate,
        estimatedReturnDate: systemModuleAssignmentUnit.estimatedReturnDate,
        comments: systemModuleAssignmentUnit.comments,
      });

    return newAssignment;
  }
  const { isEnabled, isDeleted, deletedAt, createdAt, updatedAt, ...rest } = unitData;
  const updatedAssignemt = await trx
    .update(systemModuleAssignmentUnit)
    .set({ ...rest, updatedAt: new Date(moment().format()) })
    .where(
      and(
        eq(systemModuleAssignmentUnit.fkUserId, unitData.fkUserId),
        eq(systemModuleAssignmentUnit.fkFatigueId, unitData.fkFatigueId),
      ),
    );
  return updatedAssignemt;
};

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
export const validateDates = (body: any) => {
  const dateFields = [
    { field: 'assignmentDate', error: 'El campo assignmentDate no es una fecha valida' },
    { field: 'estimatedReturnDate', error: 'El campo estimatedReturnDate no es una fecha valida' },
    { field: 'expirationDate', error: 'El campo expirationDate no es una fecha valida' },
    { field: 'assignmentEndDate', error: 'El campo assignmentEndDate no es una fecha valida' },
  ];

  for (const df of dateFields) {
    if (body[df.field]) {
      const date = new Date(body[df.field].replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(date.getTime())) {
        throw new Error(df.error);
      }
    }
  }

  if (body.estimatedReturnDate && new Date(body.estimatedReturnDate) < new Date(body.assignmentDate)) {
    throw new Error('La fecha estimada de retorno no puede ser anterior a la fecha de inicio');
  }

  if (body.assignmentEndDate && new Date(body.assignmentEndDate) < new Date(body.assignmentDate)) {
    throw new Error('La fecha de fin de asignación no puede ser anterior a la fecha de inicio');
  }
};

export const validateUnitAssignmentInFatigue = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkUnitControlId: string,
  fkFatigueId: string,
  fkUnitAssignmentId: string,
) => {
  const activeAssignment = await trx
    .select()
    .from(systemModuleAssignmentUnit)
    .where(
      and(
        eq(systemModuleAssignmentUnit.fkUnitControlId, fkUnitControlId),
        eq(systemModuleAssignmentUnit.fkFatigueId, fkFatigueId),
        ne(systemModuleAssignmentUnit.id, fkUnitAssignmentId),
      ),
    )
    .limit(1);
  if (activeAssignment.length > 0) {
    throw new Error('Ya existe una asignación con la misma unidad en el despliegue indicado');
  }
};

export const validateUnitActiveAssignment = async (
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, any>, ExtractTablesWithRelations<Record<string, any>>>,
  fkUnitControlId: string,
  fkUnitAssignmentId: string,
) => {
  const activeWeaponAssignment = await trx
    .select()
    .from(systemModuleAssignmentUnit)
    .leftJoin(systemModuleControlFatigue, eq(systemModuleAssignmentUnit.fkFatigueId, systemModuleControlFatigue.id))
    .where(
      and(
        eq(systemModuleControlFatigue.status, 'active'),
        eq(systemModuleAssignmentUnit.fkUnitControlId, fkUnitControlId),
        eq(systemModuleAssignmentUnit.isDeleted, false),
        ne(systemModuleAssignmentUnit.id, fkUnitAssignmentId),
      ),
    )
    .limit(1);
  if (activeWeaponAssignment.length > 0) {
    throw new Error('La unidad esta asignada a otro elemento en un despliegue activo');
  }
};

export const handleUserChangeInTacticalEquipmentAssig = async (
  trx: PgTransaction<NodePgQueryResultHKT, Record<string, never>, ExtractTablesWithRelations<Record<string, never>>>,
  fkUserId: string,
  fkUnitControlId: string,
  fkFatigueId: string,
  fkUnitAssignmentId: string,
) => {
  const existingAssignment = await trx
    .select()
    .from(systemModuleAssignmentUnit)
    .where(
      and(
        eq(systemModuleAssignmentUnit.fkUserId, fkUserId),
        eq(systemModuleAssignmentUnit.fkUnitControlId, fkUnitControlId),
        eq(systemModuleAssignmentUnit.fkFatigueId, fkFatigueId),
        ne(systemModuleAssignmentUnit.id, fkUnitAssignmentId), // Excluir la asignación actual
      ),
    )
    .limit(1);

  if (existingAssignment.length > 0) {
    throw new Error('El usuario ya tiene una asignación con esta unidad en el mismo despliegue');
  }
};
