meta {
  name: searchPaginated
  type: http
  seq: 2
}

get {
  url: {{base}}/user/search?limit=10&deleted=false&disabled=false&locale=es&q=Prueba&grouped=true&groupedBy=job_position | direction | leadership | work_area | job_status | secondment | institution | shift
  body: none
  auth: none
}

params:query {
  limit: 10
  deleted: false
  disabled: false
  locale: es
  q: Prueba
  grouped: true
  groupedBy: job_position | direction | leadership | work_area | job_status | secondment | institution | shift
  ~lastId: 
}

headers {
  authorization: 
}
