import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/control/controllers/fatigue_category.controller';

const fatigueCategoryRouter = Router();

fatigueCategoryRouter.get('/', getAll);
fatigueCategoryRouter.get('/search', searchPaginated);
fatigueCategoryRouter.get('/:id', getOne);
fatigueCategoryRouter.post('/', create);
fatigueCategoryRouter.put('/:id', update);
fatigueCategoryRouter.delete('/:id', deleteOne);

export default fatigueCategoryRouter;
