meta {
  name: searchPaginated
  type: http
  seq: 3
}

get {
  url: {{base}}/kardex/medical/search?lastId&limit=10&deleted=false&disabled=true&grouped=true&groupedBy=blood_group | medical_insurance | use_wheelchair&q=Prueba
  body: none
  auth: inherit
}

params:query {
  lastId: 
  limit: 10
  deleted: false
  disabled: true
  grouped: true
  groupedBy: blood_group | medical_insurance | use_wheelchair
  q: Prueba
}
