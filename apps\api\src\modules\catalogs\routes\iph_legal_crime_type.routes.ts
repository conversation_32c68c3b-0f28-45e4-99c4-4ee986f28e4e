import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/iph_legal_crime_type.controller';

const catIphLegalCrimeTypeRouter = Router();

catIphLegalCrimeTypeRouter.get('/', getAll);
catIphLegalCrimeTypeRouter.get('/search', searchPaginated);
catIphLegalCrimeTypeRouter.get('/:id', getOne);
catIphLegalCrimeTypeRouter.post('/', create);
catIphLegalCrimeTypeRouter.put('/:id', update);
catIphLegalCrimeTypeRouter.delete('/:id', deleteOne);

export default catIphLegalCrimeTypeRouter;
