import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import {
  systemModuleAssignmentOperationQuadrant,
  systemModuleControlFatigue,
  systemModuleControlOperationQuadrant,
  systemModuleUser,
} from '@repo/shared-drizzle/schemas';
import {
  createOperationQuadrantAssignment,
  handleUserChangeInOperationQuadrantAssign,
  operationQuadrantAssignmentBodySchema,
  validateOperationQuadrantActiveAssignment,
  validateOperationQuadrantAssignmentInFatigue,
} from '../helpers/operation_quadrant.helper';

type OperationQuadrantAssignGrouped = Record<
  string,
  {
    fkUserId: string;
    alias: string | null;
    data: {
      id: string;
      fkOperationQuadrantId: string;
      fkFatigueId: string;
      assignmentDate: string | null;
      assignmentEndDate: string | null;
      comments: string | null;
    }[];
  }
>;

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, grouped: groupedQS } = req.query;
  let deleted = false;
  let disabled = false;
  let grouped = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentOperationQuadrant.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentOperationQuadrant.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentOperationQuadrant.id,
        fkUserId: systemModuleAssignmentOperationQuadrant.fkUserId,
        alias: systemModuleUser.alias,
        fkOperationQuadrantId: systemModuleAssignmentOperationQuadrant.fkOperationQuadrantId,
        fkFatigueId: systemModuleAssignmentOperationQuadrant.fkFatigueId,
        assignmentDate: systemModuleAssignmentOperationQuadrant.assignmentDate,
        assignmentEndDate: systemModuleAssignmentOperationQuadrant.assignmentEndDate,
        comments: systemModuleAssignmentOperationQuadrant.comments,
      })
      .from(systemModuleAssignmentOperationQuadrant)
      .leftJoin(systemModuleUser, eq(systemModuleAssignmentOperationQuadrant.fkUserId, systemModuleUser.id))
      .where(and(...filters));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, alias, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            alias,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkOperationQuadrantId: rest.fkOperationQuadrantId,
            fkFatigueId: rest.fkFatigueId,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as OperationQuadrantAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.OPERATIONAL_QUADRANT}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleAssignmentOperationQuadrant.id,
        fkUserId: systemModuleAssignmentOperationQuadrant.fkUserId,
        alias: systemModuleUser.alias,
        fkOperationQuadrantId: systemModuleAssignmentOperationQuadrant.fkOperationQuadrantId,
        fkFatigueId: systemModuleAssignmentOperationQuadrant.fkFatigueId,
        assignmentDate: systemModuleAssignmentOperationQuadrant.assignmentDate,
        assignmentEndDate: systemModuleAssignmentOperationQuadrant.assignmentEndDate,
        comments: systemModuleAssignmentOperationQuadrant.comments,
      })
      .from(systemModuleAssignmentOperationQuadrant)
      .leftJoin(systemModuleUser, eq(systemModuleAssignmentOperationQuadrant.fkUserId, systemModuleUser.id))
      .where(eq(systemModuleAssignmentOperationQuadrant.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.OPERATIONAL_QUADRANT}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar assignmentDate
    const assignmentDate = new Date(req.body.assignmentDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(assignmentDate.getTime())) {
      throw new Error('El campo assignmentDate no es una fecha valida');
    }

    // Quitar del payload el campo assignmentEndDate, no considerar al crear la asignación
    const { assignmentEndDate, ...rest } = req.body;

    const result = await db.transaction(async (trx) => {
      const resultCreate = await createOperationQuadrantAssignment(trx, [{ ...rest }]);
      return resultCreate;
    });

    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.OPERATIONAL_QUADRANT} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar assignmentDate
    const assignmentDate = new Date(req.body.assignmentDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(assignmentDate.getTime())) {
      throw new Error('El campo assignmentDate no es una fecha valida');
    }
    // Validar assignmentEndDate
    if (req.body.assignmentEndDate) {
      const assignmentEndDate = new Date(req.body.assignmentEndDate.replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(assignmentEndDate.getTime())) {
        throw new Error('El campo assignmentEndDate no es una fecha valida');
      }
      if (assignmentEndDate < assignmentDate) {
        throw new Error('El campo assignmentEndDate no puede ser anterior al campo assignmentDate');
      }
    }

    // Validar payload
    const updateData = await operationQuadrantAssignmentBodySchema.parseAsync({ ...req.body });
    const result = await db.transaction(async (trx) => {
      // Obtener registro de asignación
      const assignment = await trx
        .select()
        .from(systemModuleAssignmentOperationQuadrant)
        .where(eq(systemModuleAssignmentOperationQuadrant.id, id))
        .limit(1);
      if (assignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar la existencia del cuadrante de operación
      const operationQuadrant = await trx
        .select()
        .from(systemModuleControlOperationQuadrant)
        .where(eq(systemModuleControlOperationQuadrant.id, updateData.fkOperationQuadrantId))
        .limit(1);
      if (operationQuadrant.length === 0) {
        throw new Error('Cuadrante de operación no encontrado');
      }

      // Validar si cambio de cuadrante de operación, que no este en el mismo despliegue ya asignado
      if (updateData.fkOperationQuadrantId !== assignment[0].fkOperationQuadrantId) {
        await validateOperationQuadrantAssignmentInFatigue(
          trx,
          updateData.fkOperationQuadrantId,
          updateData.fkFatigueId,
          id,
        );
      }

      // Validar si el cuadrante de operación tiene una asignación activa
      await validateOperationQuadrantActiveAssignment(trx, updateData.fkOperationQuadrantId, updateData.fkFatigueId);

      // Verificar si el usuario cambió
      if (updateData.fkUserId !== assignment[0].fkUserId) {
        await handleUserChangeInOperationQuadrantAssign(
          trx,
          updateData.fkUserId,
          updateData.fkOperationQuadrantId,
          updateData.fkFatigueId,
          id,
        );
      }

      // Actualizar asignación
      const assignUpdate = await trx
        .update(systemModuleAssignmentOperationQuadrant)
        .set({ ...updateData })
        .where(eq(systemModuleAssignmentOperationQuadrant.id, id));

      return assignUpdate;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.OPERATIONAL_QUADRANT}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Validar que exista la asignación
      const existingAssignment = await trx
        .select()
        .from(systemModuleAssignmentOperationQuadrant)
        .where(eq(systemModuleAssignmentOperationQuadrant.fkOperationQuadrantId, id))
        .limit(1);

      if (existingAssignment.length > 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar que la asignación no este en un despliegue activo
      const activeOperationQuadrantAssignment = await trx
        .select()
        .from(systemModuleAssignmentOperationQuadrant)
        .leftJoin(
          systemModuleControlFatigue,
          eq(systemModuleAssignmentOperationQuadrant.fkFatigueId, systemModuleControlFatigue.id),
        )
        .where(
          and(
            eq(systemModuleControlFatigue.status, 'active'),
            eq(systemModuleAssignmentOperationQuadrant.fkFatigueId, existingAssignment[0].fkFatigueId),
            eq(systemModuleAssignmentOperationQuadrant.fkOperationQuadrantId, id),
          ),
        )
        .limit(1);
      if (activeOperationQuadrantAssignment.length > 0) {
        throw new Error('No es posible eliminar la asignación si este ya se encuentra en un despliegue activo');
      }

      const deleteResult = await trx
        .update(systemModuleAssignmentOperationQuadrant)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleAssignmentOperationQuadrant.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.OPERATIONAL_QUADRANT}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    q: qQS,
    grouped: groupedQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;
  let grouped = false;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentOperationQuadrant.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentOperationQuadrant.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleUser.alias, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentOperationQuadrant.assignmentDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentOperationQuadrant.assignmentEndDate}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleAssignmentOperationQuadrant.comments, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleAssignmentOperationQuadrant.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentOperationQuadrant.id,
        fkUserId: systemModuleAssignmentOperationQuadrant.fkUserId,
        alias: systemModuleUser.alias,
        fkOperationQuadrantId: systemModuleAssignmentOperationQuadrant.fkOperationQuadrantId,
        fkFatigueId: systemModuleAssignmentOperationQuadrant.fkFatigueId,
        assignmentDate: systemModuleAssignmentOperationQuadrant.assignmentDate,
        assignmentEndDate: systemModuleAssignmentOperationQuadrant.assignmentEndDate,
        comments: systemModuleAssignmentOperationQuadrant.comments,
      })
      .from(systemModuleAssignmentOperationQuadrant)
      .leftJoin(systemModuleUser, eq(systemModuleAssignmentOperationQuadrant.fkUserId, systemModuleUser.id))
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleAssignmentOperationQuadrant.id));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, alias, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            alias,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkOperationQuadrantId: rest.fkOperationQuadrantId,
            fkFatigueId: rest.fkFatigueId,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as OperationQuadrantAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.OPERATIONAL_QUADRANT} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
