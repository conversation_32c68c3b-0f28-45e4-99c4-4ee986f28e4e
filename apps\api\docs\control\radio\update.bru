meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/control/radio/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 
}

body:json {
  {
    "serialNumber": "Número de Serie",
    "nationalRegistryNumber": "Número Registro Nacional",
    "fkRadioBrandId": "ID Marca Radio", 
    "fkRadioModelId": "ID Modelo Radio",
    "status": "Estado del radio", // ['new', 'in_use', 'damaged', 'lost', 'free', 'disabled', 'ended_use']
    "expirationDate": "Fecha de expiración", // Opcional
    "acquisitionDate": "Fecha de compra/adquisición",
    "cost": 0, // Costo
    "description": "Descripción" // Opcional
  }
}
