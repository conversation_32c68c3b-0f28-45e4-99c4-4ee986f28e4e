import { Router } from 'express';
import weaponAssignRouter from './weapon.routes';
import tacticalEquipmentAssignRouter from './tactical_equipment.routes';
import uniformAssignRouter from './uniform.routes';
import unitAssignRouter from './unit.routes';
import radioAssignRouter from './radio.routes';
import operationQuadrantRouter from './operation_quadrant.routes';
import quadrantGroupingRouter from './quadrant_grouping.routes';
import ammunitionAssignRouter from './ammunition.routes';
import commissionAssignRouter from './commission.routes';
import fatigueAssignRouter from './fatigue.routes';

const assignmentRouter = Router();

assignmentRouter.use('/weapon', weaponAssignRouter);
assignmentRouter.use('/tactical_equipment', tacticalEquipmentAssignRouter);
assignmentRouter.use('/uniform', uniformAssignRouter);
assignmentRouter.use('/unit', unitAssignRouter);
assignmentRouter.use('/radio', radioAssignRouter);
assignmentRouter.use('/operation_quadrant', operationQuadrantRouter);
assignmentRouter.use('/quadrant_grouping', quadrantGroupingRouter);
assignmentRouter.use('/ammunition', ammunitionAssignRouter);
assignmentRouter.use('/commission', commissionAssignRouter);
assignmentRouter.use('/fatigue', fatigueAssignRouter);

export default assignmentRouter;
