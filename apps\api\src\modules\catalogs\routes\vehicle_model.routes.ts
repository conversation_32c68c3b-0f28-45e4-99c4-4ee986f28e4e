import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/vehicle_model.controller';

const catVehicleModelRouter = Router();

catVehicleModelRouter.get('/:brand/search', searchPaginated);
catVehicleModelRouter.get('/:brand', getAll);
catVehicleModelRouter.get('/one/:id', getOne);
catVehicleModelRouter.post('/', create);
catVehicleModelRouter.put('/:id', update);
catVehicleModelRouter.delete('/:id', deleteOne);

export default catVehicleModelRouter;
