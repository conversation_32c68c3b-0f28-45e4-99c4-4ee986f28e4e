import type {
  APIIntegrationStatuse as APIIntegrationStatuseType,
  IPHCaseType as IPHCaseTypeType,
  IPHCommentStatuse as IPHCommentStatuseType,
  IPHCommentType as IPHCommentTypeType,
  IPHExtensionType as IPHExtensionTypeType,
  IPHHowFindOut as IPHHowFindOutType,
  IPHInspectionType as IPHInspectionTypeType,
  IPHIntervieweeType as IPHIntervieweeTypeType,
  PlaceType as IPHPlaceTypeType,
  IPHSection as IPHSectionType,
  IPHStatus as IPHStatusType,
  //IPHUseOfForceType as IPHUseOfForceTypeType,
  VehicleSituation as VehicleSituationType,
  VehicleTerrain as VehicleTerrainType,
  VehicleUseType as VehicleUseTypeType,
  IPHWeaponSize as IPHWeaponSizeType,
  OwnershipStatuse as OwnershipStatuseType,
  WeaponStatuse as WeaponStatuseType,
  DisplayStyleType as DisplayStyleTypeType,
  EvaluationType as EvaluationTypeType,
  UniformStatuse as UniformStatuseType,
  IncidentR<PERSON><PERSON>erType as IncidentReporterTypeType,
  TacticalEquipmentStatuse as TacticalEquipmentStatuseType,
  VehicleStatuse as VehicleStatuseType,
  RadioStatuse as RadioStatuseType,
  FatigueStatuse as FatigueStatuseType,
  UserStatusInFatigue as UserStatusInFatigueType,
  FatiguePlaceType as FatiguePlaceTypeType,
  IncidentTypePriority as IncidentTypePriorityType,
  CadAlertType as CadAlertTypeType,
  CadAlertStatus as CadAlertStatusType,
  CadAlertInformantType as CadAlertInformantTypeType,
  CadAlertSourcetype as CadAlertSourcetypeType,
  IPHInventoryItemType,
  CadAlertActivityType as CadAlertActivityTypeType,
  CadAlertConfigstype as CadAlertConfigstypeType,
  FederalEntitiesType as FederalEntitiesTypeType,
  EducationalLevelType as EducationalLevelTypeType,
  BloodGroupType,
  DepartamentalAreaType as DepartamentalAreaTypeType,
} from '../types';

export const IPHStatus: { [key in IPHStatusType]: string } = {
  aproved: 'Aprobado',
  completed: 'Completado',
  declined: 'Rechazado',
  in_progress: 'En progreso',
  started: 'Iniciado',
};

export const IPHCaseType: { [key in IPHCaseTypeType]: string } = {
  criminal: 'Criminal',
  administrative: 'Administrativo',
};

export const IPHVehicleSituation: { [key in VehicleSituationType]: string } = {
  with_report: 'Con reporte de robo',
  without_report: 'Sin reporte de robo',
  unknown: 'Desconocido',
};

export const IPHHowFindOut: { [key in IPHHowFindOutType]: string } = {
  location: 'Localización',
  arrest: 'Aportación',
  denouncement: 'Denuncia',
  flagrance: 'Flagrancia',
  judicial: 'Mandamiento judicial',
  discovery: 'Descubrimiento',
  emergency: 'Llamada de emergencia',
};

export const IPHInspectionType: { [key in IPHInspectionTypeType]: string } = {
  place: 'Lugar',
  person: 'Persona',
  vehicle: 'Vehiculo',
};

export const IPHInventoryItemTypes: { [key in IPHInventoryItemType]: string } = {
  item: 'Objeto',
  weapon: 'Arma de fuego',
};

// TODO: No existe un tipo IPHUseOfForceType
/*export const IPHUseOfForceType: { [key in IPHUseOfForceTypeType]: string } = {
  authority: 'Autoridad',
  person: 'Persona',
};*/

export const IPHSection: { [key in IPHSectionType]: string } = {
  sec_1: 'Sección 1',
  sec_2: 'Sección 2',
  sec_3: 'Sección 3',
  sec_4: 'Sección 4',
  sec_5: 'Sección 5',
  anx_a: 'Anexo A',
  anx_b: 'Anexo B',
  anx_c: 'Anexo C',
  anx_d: 'Anexo D',
  anx_e: 'Anexo E',
  anx_f: 'Anexo F',
  anx_g: 'Anexo G',
};

export const IPHCommentType: { [key in IPHCommentTypeType]: string } = {
  general: 'General',
  rejection: 'Rechazo',
  suggestion: 'Sugerencia',
};

export const IPHCommentStatus: { [key in IPHCommentStatuseType]: string } = {
  accepted: 'Aceptado',
  declined: 'Rechazado',
  pending: 'Pendiente',
};

export const IPHIntervieweeType: { [key in IPHIntervieweeTypeType]: string } = {
  victim: 'Víctima',
  denouncer: 'Denunciante',
  witness: 'Testigo',
};

export const IPHExtensionType: { [key in IPHExtensionTypeType]: string } = {
  facts: 'Hechos',
  interview: 'Entrevista',
};

export const IPHWeaponSize: { [key in IPHWeaponSizeType]: string } = {
  long: 'Larga',
  short: 'Corta',
};

export const IPHVehicleTerrain: { [key in VehicleTerrainType]: string } = {
  air: 'Aéreo',
  water: 'Acuatico',
  land: 'Terrestre',
};

export const IPHVehicleUseType: { [key in VehicleUseTypeType]: string } = {
  private: 'Particular',
  public: 'Transporte público',
  cargo: 'Carga',
};
export const APIIntegrationStatus: { [key in APIIntegrationStatuseType]: string } = {
  active: 'Activo',
  inactive: 'Inactivo',
  in_progress: 'En progreso',
  in_maintenance: 'En mantenimiento',
};

export const WeaponStatus: { [key in WeaponStatuseType]: string } = {
  active: 'Activo',
  inactive: 'Inactivo',
  in_maintenance: 'En mantenimiento',
};

export const OwnershipStatus: { [key in OwnershipStatuseType]: string } = {
  own: 'Propio',
  comodato: 'Comodato',
};

export const DisplayStyleType: { [key in DisplayStyleTypeType]: string } = {
  dotted: 'Punteado',
  solid: 'Sólido',
  dashed: 'Lineas',
  double: 'Doble',
  groove: 'Surco',
  ridge: 'Cresta',
  inset: 'Inset',
  outset: 'Outset',
  none: 'Ninguno',
};

export const EvaluationType: { [key in EvaluationTypeType]: string } = {
  physical: 'Físico',
  psychological: 'Psicológico',
  medical: 'Médico',
  technical: 'Técnico',
};

export const UniformStatus: { [key in UniformStatuseType]: string } = {
  new: 'Nuevo',
  in_use: 'En uso',
  damaged: 'Dañado',
  lost: 'Perdido',
  disabled: 'Deshabilitado',
  ended_use: 'Uso finalizado',
  free: 'Libre',
};

export const IncidentReporterType: { [key in IncidentReporterTypeType]: string } = {
  report: 'Reporte del 911',
  statement: 'Señalamiento',
  flagancy: 'Flagrancia',
};

export const IPHPlaceType: { [key in IPHPlaceTypeType]: string } = {
  general: 'General',
  accounting: 'Contabilidad',
  airport: 'Aeropuerto',
  amusement_park: 'Parque de entretenimiento',
  aquarium: 'Acuario',
  art_gallery: 'Galería de arte',
  atm: 'Cajero automático',
  bakery: 'Panadería',
  bank: 'Banco',
  bar: 'Bar',
  beauty_salon: 'Salón de belleza',
  bicycle_store: 'Tienda de bicicletas',
  book_store: 'Librería',
  bowling_alley: 'Pasillo de bowling',
  bus_station: 'Estación de autobuses',
  cafe: 'Café',
  campground: 'Camping',
  car_dealer: 'Concesionario de automóviles',
  car_rental: 'Alquiler de automóviles',
  car_repair: 'Reparación de automóviles',
  car_wash: 'Lavado de automóviles',
  casino: 'Casino',
  cemetery: 'Cementerio',
  church: 'Iglesia',
  city_hall: 'Ayuntamiento',
  clothing_store: 'Tienda de ropa',
  convenience_store: 'Tienda de conveniencia',
  courthouse: 'Juzgado',
  dentist: 'Dentista',
  department_store: 'Tienda de departamentos',
  doctor: 'Doctor',
  drugstore: 'Farmacia',
  electrician: 'Electricista',
  electronics_store: 'Tienda de electrónica',
  embassy: 'Embajada',
  fire_station: 'Fuegos',
  florist: 'Floristería',
  funeral_home: 'Casa de funerales',
  furniture_store: 'Tienda de muebles',
  gas_station: 'Gasolinera',
  gym: 'Gimnasio',
  hair_care: 'Peluquería',
  hardware_store: 'Tienda de materiales',
  hindu_temple: 'Templo hindú',
  home_goods_store: 'Tienda de productos domésticos',
  hospital: 'Hospital',
  insurance_agency: 'Agencia de seguros',
  jewelry_store: 'Tienda de joyería',
  laundry: 'Lavandería',
  lawyer: 'Abogado',
  library: 'Biblioteca',
  light_rail_station: 'Estación de trenes',
  liquor_store: 'Tienda de licorería',
  local_government_office: 'Oficina de gobierno local',
  locksmith: 'Cerrajería',
  lodging: 'Alojamiento',
  meal_delivery: 'Entrega de comida',
  meal_takeaway: 'Comida de tomar',
  mosque: 'Masía',
  movie_rental: 'Alquiler de películas',
  movie_theater: 'Cine',
  moving_company: 'Empresa de transporte',
  museum: 'Museo',
  night_club: 'Bar de noche',
  painter: 'Pintor',
  park: 'Parque',
  parking: 'Aparcamiento',
  pet_store: 'Tienda de mascotas',
  pharmacy: 'Farmacia',
  physiotherapist: 'Fisioterapeuta',
  plumber: 'Plomero',
  police: 'Policía',
  post_office: 'Oficina de correos',
  primary_school: 'Colegio primario',
  real_estate_agency: 'Agencia de bienes raíces',
  restaurant: 'Restaurante',
  roofing_contractor: 'Contratista de techo',
  rv_park: 'Parque de automóviles',
  school: 'Escuela',
  secondary_school: 'Colegio secundario',
  shoe_store: 'Tienda de zapatos',
  shopping_mall: 'Centro comercial',
  spa: 'Spa',
  stadium: 'Estadio',
  storage: 'Almacén',
  store: 'Tienda',
  subway_station: 'Estación de metro',
  supermarket: 'Supermercado',
  synagogue: 'Sinagoga',
  taxi_stand: 'Estación de taxis',
  tourist_attraction: 'Atracción turística',
  train_station: 'Estación de tren',
  transit_station: 'Estación de transporte',
  travel_agency: 'Agencia de viajes',
  university: 'Universidad',
  veterinary_care: 'Veterinaria',
  zoo: 'Zoológico',
  archipelago: 'Archipiélago',
  colloquial_area: 'Área colectiva',
  continent: 'Continente',
  country: 'País',
  establishment: 'Establecimiento',
  finance: 'Finanzas',
  floor: 'Piso',
  food: 'Comida',
  general_contractor: 'Contratista general',
  geocode: 'Geocodificación',
  health: 'Salud',
  intersection: 'Intersección',
  landmark: 'Marca de tierra',
  locality: 'Localidad',
  natural_feature: 'Característica natural',
  neighborhood: 'Barrio',
  place_of_worship: 'Lugar de culto',
  plus_code: 'Código Plus',
  point_of_interest: 'Punto de interés',
  political: 'Político',
  post_box: 'Buzón de correos',
  premise: 'Premisa',
  room: 'Habitación',
  route: 'Ruta',
  street_address: 'Dirección de calle',
  street_number: 'Número de calle',
  town_square: 'Plaza de la ciudad',
};

export const TacticalEquipmentStatus: { [key in TacticalEquipmentStatuseType]: string } = {
  new: 'Nuevo',
  in_use: 'En uso',
  damaged: 'Dañado',
  lost: 'Perdido',
  disabled: 'Deshabilitado',
  ended_use: 'Uso finalizado',
  free: 'Libre',
};

export const VehicleStatus: { [key in VehicleStatuseType]: string } = {
  new: 'Nuevo',
  in_use: 'En uso',
  damaged: 'Dañado',
  lost: 'Perdido',
  disabled: 'Deshabilitado',
  ended_use: 'Uso finalizado',
  free: 'Libre',
};

export const RadioStatus: { [key in RadioStatuseType]: string } = {
  new: 'Nuevo',
  in_use: 'En uso',
  damaged: 'Dañado',
  lost: 'Perdido',
  disabled: 'Deshabilitado',
  ended_use: 'Uso finalizado',
  free: 'Libre',
};

export const FatigueStatus: { [key in FatigueStatuseType]: string } = {
  draft: 'Borrador',
  scheduled: 'Programado',
  active: 'Activo',
  completed: 'Completado',
};

export const UserStatusInFatigue: { [key in UserStatusInFatigueType]: string } = {
  early: 'Temprano',
  on_time: 'A tiempo',
  late: 'Tarde',
  denied: 'Falta',
  unknown: 'Desconocida',
  vacation: 'Vacaciones',
  disabled: 'Incapacitado',
  in_gdl: 'En GDL',
  in_exam: 'En examen',
  in_course: 'En curso',
  permission: 'De permiso',
  commissioner: 'Comisionado',
  with_license: 'Con licencia',
};

export const FatiguePlaceType: { [key in FatiguePlaceTypeType]: string } = {
  stand: 'Caseta',
  town_hall: 'Ayuntamiento',
  academy: 'Academia',
};

export const CadAlertType: { [key in CadAlertTypeType]: string } = {
  manual_alert: 'Alerta manual',
  citizen_alert: 'Alerta de ciudadano',
};

export const IncidentTypePriority: { [key in IncidentTypePriorityType]: string } = {
  no_priority: 'Sin prioridad',
  low: 'Baja',
  medium: 'Media',
  high: 'Alta',
  critical: 'Crítica',
};

export const CadAlertStatus: { [key in CadAlertStatusType]: string } = {
  new: 'Nuevo',
  duplicated: 'Duplicado',
  canceled: 'Cancelado',
  in_attention: 'En atención',
  dispatched: 'Despachado',
  resolved: 'Resuelto',
  no_resolved: 'No resuelto',
  paused: 'Pausado',
  transferred: 'Transferido',
};

export const CadAlertInformantType: { [key in CadAlertInformantTypeType]: string } = {
  person: 'Persona',
  business: 'Negocio',
  annonymous: 'Anónimo',
  organization: 'Organización',
};

export const CadAlertSourcetype: { [key in CadAlertSourcetypeType]: string } = {
  radio: 'Radio',
  municipal_telephone: 'Teléfono Municipal',
  '911': '911',
  direct_complaint: 'Denuncia Directa',
  app: 'App',
};

export const CadAlertActivityType: { [key in CadAlertActivityTypeType]: string } = {
  site: 'Sitio',
  citizen_app: 'Aplicación Ciudadana',
  police_app: 'Aplicación Policía',
};

export const CadAlertConfigstype: { [key in CadAlertConfigstypeType]: string } = {
  safe_place: 'Lugar Seguro',
  headquarters: 'Cuartel',
  safe_path: 'Ruta Segura',
  socket_cleanup: 'Limpieza de salas - Socket',
};

export const FederalEntities: { [key in FederalEntitiesTypeType]: string } = {
  aguascalientes: 'Aguascalientes',
  baja_california: 'Baja California',
  baja_california_sur: 'Baja California Sur',
  campeche: 'Campeche',
  coahuila: 'Coahuila de Zaragoza',
  colima: 'Colima',
  chiapas: 'Chiapas',
  chihuahua: 'Chihuahua',
  cdmx: 'CDMX',
  durango: 'Durango',
  guanajuato: 'Guanajuato',
  guerrero: 'Guerrero',
  hidalgo: 'Hidalgo',
  jalisco: 'Jalisco',
  estado_de_mexico: 'Estado de México',
  michoacan: 'Michoacán de Ocampo',
  morelos: 'Morelos',
  nayarit: 'Nayarit',
  nuevo_leon: 'Nuevo León',
  oaxaca: 'Oaxaca',
  puebla: 'Puebla',
  queretaro: 'Querétaro',
  quintana_roo: 'Quintana Roo',
  san_luis_potosi: 'San Luis Potosí',
  sinaloa: 'Sinaloa',
  sonora: 'Sonora',
  tabasco: 'Tabasco',
  tamaulipas: 'Tamaulipas',
  tlaxcala: 'Tlaxcala',
  veracruz: 'Veracruz de Ignacio de la Llave',
  yucatan: 'Yucatán',
  zacatecas: 'Zacatecas',
};

export const EducationalLevel: { [key in EducationalLevelTypeType]: string } = {
  primaria_trunca: 'Primaria (Trunca)',
  primaria_terminada: 'Primaria (Terminada)',
  secundaria_trunca: 'Secundaria (Trunca)',
  secundaria_terminada: 'Secundaria (Terminada)',
  preparatoria_trunca: 'Preparatoria (Trunca)',
  preparatoria_terminada: 'Preparatoria (Terminada)',
  licenciatura_trunca: 'Licenciatura (Trunca)',
  licenciatura_terminada: 'Licenciatura (Terminada)',
  maestria: 'Maestría',
  doctorado: 'Doctorado',
};

export const BloodGroup: { [key in BloodGroupType]: string } = {
  A_plus: 'A+',
  A_minus: 'A-',
  B_plus: 'B+',
  B_minus: 'B-',
  AB_plus: 'AB+',
  AB_minus: 'AB-',
  O_plus: 'O+',
  O_minus: 'O-',
};

export const DepartamentalAreaType: { [key in DepartamentalAreaTypeType]: string } = {
  operational: 'Operacional',
  administrative: 'Administrativo',
};
