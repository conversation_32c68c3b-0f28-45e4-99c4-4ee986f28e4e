import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/vehicle_brand.controller';

const catVehicleBrandRouter = Router();

catVehicleBrandRouter.get('/', getAll);
catVehicleBrandRouter.get('/search', searchPaginated);
catVehicleBrandRouter.get('/:id', getOne);
catVehicleBrandRouter.post('/', create);
catVehicleBrandRouter.put('/:id', update);
catVehicleBrandRouter.delete('/:id', deleteOne);

export default catVehicleBrandRouter;
