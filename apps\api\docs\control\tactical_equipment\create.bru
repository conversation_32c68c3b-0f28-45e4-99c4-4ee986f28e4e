meta {
  name: create
  type: http
  seq: 5
}

post {
  url: {{base}}/control/tactical_equipment?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

body:json {
  {
    "serialNumber": "Número de Serie",
    "fkTacticalEquipmentTypeId": "ID Tipo de equipo táctico",
    "fkSizeId": "ID Tamaño", 
    "tacticalEquipmentStatus": "Estado equipo táctico", // ['new', 'in_use', 'damaged', 'lost', 'free', 'disabled', 'ended_use']
    "expirationDate": "Fecha de expiración", // Opcional
    "acquisitionDate": "Fecha de compra/adquisición",
    "cost": "Costo", // Number
    "fkSupplierId": "ID Proveedor", // Opcional
    "description": "Descripción" // Opcional
  }
}
