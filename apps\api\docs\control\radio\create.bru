meta {
  name: create
  type: http
  seq: 5
}

post {
  url: {{base}}/control/radio?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

body:json {
  {
    "serialNumber": "Número de Serie",
    "nationalRegistryNumber": "Número Registro Nacional",
    "fkRadioBrandId": "ID Marca Radio", 
    "fkRadioModelId": "ID Modelo Radio",
    "status": "Estado del radio", // ['new', 'in_use', 'damaged', 'lost', 'free', 'disabled', 'ended_use']
    "expirationDate": "Fecha de expiración", // Opcional
    "acquisitionDate": "Fecha de compra/adquisición",
    "cost": 0, // Costo
    "description": "Descripción" // Opcional
  }
}
