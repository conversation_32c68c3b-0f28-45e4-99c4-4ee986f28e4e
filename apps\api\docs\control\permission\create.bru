meta {
  name: create
  type: http
  seq: 4
}

post {
  url: {{base}}/control/permission?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

body:json {
  {
    "arrPermission": [
      {
        "name": "<PERSON><PERSON><PERSON>",
        "description": "<PERSON><PERSON><PERSON> de prueba",
        "canRead": true,
        "canCreate": true,
        "canUpdate": true,
        "canDelete": true,
        "fkProfileId": "01951fc6-da7a-35db-0572-ad3562bc939a",
        "fkModulePermissionId": "01961c7e-064d-d92e-8226-b45c0ef96629"
      }
    ]
  }
}
