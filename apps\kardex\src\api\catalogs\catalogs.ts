import type { CatalogItemBase } from '@/components/ComboboxPlus';
import { VITE_BASE_URL } from '@/config/env';
import {
  type CatalogItem,
  type LocationCatalogItem,
  type ApiError,
  type GetAllParams,
  type SearchParams,
  type SettlementCatalogItem,
  FetchError,
} from '@/types/api.types';
import type { incidentTypeSchema } from '@repo/types/schemas';

/* ------------------------------------------------ institution ---------------------------------------------------- */

// searchPaginated
export const searchPaginatedInstitution = async (params: SearchParams = {}): Promise<CatalogItemBase[]> => {
  const { lastId, limit = 5, locale = 'es', q } = params;

  const queryParams = new URLSearchParams({
    limit: limit.toString(),
    locale,
  });

  if (lastId) queryParams.append('lastId', lastId);
  if (q) queryParams.append('q', q);

  const response = await fetch(`${VITE_BASE_URL}/catalogs/institution/search?${queryParams.toString()}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error buscando instituciones');
  }

  return data as CatalogItemBase[];
};

// searchPaginated external institution
export const searchPaginatedExternalInstitution = async (params: SearchParams = {}): Promise<CatalogItemBase[]> => {
  const { lastId, limit = 5, locale = 'es', q } = params;

  const queryParams = new URLSearchParams({
    limit: limit.toString(),
    locale,
  });

  if (lastId) queryParams.append('lastId', lastId);
  if (q) queryParams.append('q', q);

  const response = await fetch(`${VITE_BASE_URL}/catalogs/external_institution/search?${queryParams.toString()}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error buscando instituciones externas');
  }

  return data as CatalogItemBase[];
};

// searchPaginated gender
export const searchPaginatedGender = async (params: SearchParams = {}): Promise<CatalogItemBase[]> => {
  const { lastId, limit = 5, locale = 'es', q } = params;

  const queryParams = new URLSearchParams({
    limit: limit.toString(),
    locale,
  });

  if (lastId) queryParams.append('lastId', lastId);
  if (q) queryParams.append('q', q);

  const response = await fetch(`${VITE_BASE_URL}/catalogs/gender/search?${queryParams.toString()}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error buscando géneros');
  }

  return data as CatalogItemBase[];
};

// searchPaginated marital status
export const searchPaginatedMaritalStatus = async (params: SearchParams = {}): Promise<CatalogItemBase[]> => {
  const { lastId, limit = 5, locale = 'es', q } = params;

  const queryParams = new URLSearchParams({
    limit: limit.toString(),
    locale,
  });

  if (lastId) queryParams.append('lastId', lastId);
  if (q) queryParams.append('q', q);

  const response = await fetch(`${VITE_BASE_URL}/catalogs/marital_status/search?${queryParams.toString()}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error buscando estados civiles');
  }

  return data as CatalogItemBase[];
};

// searchPaginated document type
export const searchPaginatedDocumentType = async (params: SearchParams = {}): Promise<CatalogItemBase[]> => {
  const { lastId, limit = 5, locale = 'es', q } = params;

  const queryParams = new URLSearchParams({
    limit: limit.toString(),
    locale,
  });

  if (lastId) queryParams.append('lastId', lastId);
  if (q) queryParams.append('q', q);

  const response = await fetch(`${VITE_BASE_URL}/catalogs/document_type/search?${queryParams.toString()}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error buscando tipos de documentos');
  }

  return data as CatalogItemBase[];
};

/* ------------------------------------------------ iph_how_find_out ---------------------------------------------------- */

// getAll
export const getAllFindOut = async (params: GetAllParams = {}): Promise<CatalogItem[]> => {
  const { deleted = false, disabled = false, locale = 'es' } = params;

  const queryParams = new URLSearchParams({
    deleted: deleted.toString(),
    disabled: disabled.toString(),
    locale,
  });

  const response = await fetch(`${VITE_BASE_URL}/catalogs/iph_how_find_out?${queryParams}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error obteniendo medios de conocimiento');
  }

  return data as CatalogItem[];
};

// searchPaginated
export const searchPaginatedFindOut = async (params: SearchParams): Promise<CatalogItem[]> => {
  const { lastId, limit = 5, deleted = false, disabled = false, locale = 'es', q } = params;

  const queryParams = new URLSearchParams({
    limit: limit.toString(),
    deleted: deleted.toString(),
    disabled: disabled.toString(),
    locale,
  });

  if (lastId) queryParams.append('lastId', lastId);
  if (q) queryParams.append('q', q);

  const response = await fetch(`${VITE_BASE_URL}/catalogs/iph_how_find_out/search?${queryParams}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error buscando medios de conocimiento');
  }

  return data as CatalogItem[];
};

/* ------------------------------------------------ municipality ---------------------------------------------------- */

// getByState
export const getByStateMunicipality = async (
  stateId: string,
  params: GetAllParams = {},
): Promise<LocationCatalogItem[]> => {
  const { deleted = false, disabled = false, locale = 'es' } = params;

  const queryParams = new URLSearchParams({
    deleted: deleted.toString(),
    disabled: disabled.toString(),
    locale,
  });

  const response = await fetch(`${VITE_BASE_URL}/catalogs/municipality/${stateId}?${queryParams}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error obteniendo municipios');
  }

  return data as LocationCatalogItem[];
};

/* ------------------------------------------------ state ---------------------------------------------------- */

// getAll
export const getAllState = async (params: GetAllParams = {}): Promise<LocationCatalogItem[]> => {
  const { deleted = false, disabled = false, locale = 'es' } = params;

  const queryParams = new URLSearchParams({
    deleted: deleted.toString(),
    disabled: disabled.toString(),
    locale,
  });

  const response = await fetch(`${VITE_BASE_URL}/catalogs/state?${queryParams}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error obteniendo estados');
  }

  return data as LocationCatalogItem[];
};

/* ------------------------------------------------ settlement ---------------------------------------------------- */

// getByStateAndMunicipality
export const getByStateAndMunicipalitySettlement = async (
  stateId: string,
  municipalityId: string,
  params: GetAllParams = {},
): Promise<SettlementCatalogItem[]> => {
  const { deleted = false, disabled = false, locale = 'es' } = params;

  const queryParams = new URLSearchParams({
    deleted: deleted.toString(),
    disabled: disabled.toString(),
    locale,
  });

  const response = await fetch(`${VITE_BASE_URL}/catalogs/settlement/${stateId}/${municipalityId}?${queryParams}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error obteniendo asentamientos');
  }

  return data as SettlementCatalogItem[];
};

// getByZipCode
export const getByZipCodeSettlement = async (
  zipCode: string,
  params: GetAllParams = {},
): Promise<SettlementCatalogItem[]> => {
  const { deleted = false, disabled = false, locale = 'es' } = params;

  const queryParams = new URLSearchParams({
    deleted: deleted.toString(),
    disabled: disabled.toString(),
    locale,
  });

  const response = await fetch(`${VITE_BASE_URL}/catalogs/settlement/${zipCode}?${queryParams}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error obteniendo asentamientos por código postal');
  }

  return data as SettlementCatalogItem[];
};

/* ------------------------------------------------ iph_legal_crime ---------------------------------------------------- */

// searchPaginated
export const searchPaginatedLegalCrime = async (params: SearchParams): Promise<CatalogItem[]> => {
  const { lastId, limit = 10, deleted = false, disabled = false, locale = 'es', q } = params;

  const queryParams = new URLSearchParams({
    limit: limit.toString(),
    deleted: deleted.toString(),
    disabled: disabled.toString(),
    locale,
  });

  if (lastId) queryParams.append('lastId', lastId);
  if (q) queryParams.append('q', q);

  const response = await fetch(`${VITE_BASE_URL}/catalogs/iph_legal_crime/search?${queryParams}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error buscando delitos legales');
  }

  return data as CatalogItem[];
};

/* ------------------------------------------------ iph_legal_crime_type ---------------------------------------------------- */

// searchPaginated
export const searchPaginatedLegalCrimeType = async (params: SearchParams): Promise<CatalogItem[]> => {
  const { lastId, limit = 10, deleted = false, disabled = false, locale = 'es', q } = params;

  const queryParams = new URLSearchParams({
    limit: limit.toString(),
    deleted: deleted.toString(),
    disabled: disabled.toString(),
    locale,
  });

  if (lastId) queryParams.append('lastId', lastId);
  if (q) queryParams.append('q', q);

  const response = await fetch(`${VITE_BASE_URL}/catalogs/iph_legal_crime_type/search?${queryParams}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error buscando tipos de delitos legales');
  }

  return data as CatalogItem[];
};

/* ------------------------------------------------ iph_legal_crime_subtype ---------------------------------------------------- */

// searchPaginated
export const searchPaginatedLegalCrimeSubtype = async (params: SearchParams): Promise<CatalogItem[]> => {
  const { lastId, limit = 10, deleted = false, disabled = false, locale = 'es', q } = params;

  const queryParams = new URLSearchParams({
    limit: limit.toString(),
    deleted: deleted.toString(),
    disabled: disabled.toString(),
    locale,
  });

  if (lastId) queryParams.append('lastId', lastId);
  if (q) queryParams.append('q', q);

  const response = await fetch(`${VITE_BASE_URL}/catalogs/iph_legal_crime_subtype/search?${queryParams}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error buscando subtipos de delitos legales');
  }

  return data as CatalogItem[];
};

/* ------------------------------------------------ vehicle_brand ---------------------------------------------------- */

// searchPaginated
export const searchPaginatedVehicleBrand = async (params: SearchParams = {}): Promise<CatalogItem[]> => {
  const { lastId, limit = 10, deleted = false, disabled = false, locale = 'es', q } = params;

  const queryParams = new URLSearchParams({
    limit: limit.toString(),
    deleted: deleted.toString(),
    disabled: disabled.toString(),
    locale,
  });

  if (lastId) queryParams.append('lastId', lastId);
  if (q) queryParams.append('q', q);

  const response = await fetch(`${VITE_BASE_URL}/catalogs/vehicle_brand/search?${queryParams}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error buscando marcas de vehículos');
  }

  return data as CatalogItem[];
};

/* ------------------------------------------------ vehicle_model ---------------------------------------------------- */

// searchPaginated
export const searchPaginatedVehicleModel = async (
  params: SearchParams & { brand?: string } = {},
): Promise<CatalogItem[]> => {
  const { lastId, limit = 10, deleted = false, disabled = false, locale = 'es', q, brand } = params;

  const queryParams = new URLSearchParams({
    limit: limit.toString(),
    deleted: deleted.toString(),
    disabled: disabled.toString(),
    locale,
  });

  if (lastId) queryParams.append('lastId', lastId);
  if (q) queryParams.append('q', q);

  const response = await fetch(`${VITE_BASE_URL}/catalogs/vehicle_model/${brand || ''}/search?${queryParams}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error buscando modelos de vehículos');
  }

  return data as CatalogItem[];
};

/* ------------------------------------------------ vehicle_type ---------------------------------------------------- */

// getAll
export const getAllVehicleType = async (params: GetAllParams = {}): Promise<CatalogItem[]> => {
  const { deleted = false, disabled = false, locale = 'es' } = params;

  const queryParams = new URLSearchParams({
    deleted: deleted.toString(),
    disabled: disabled.toString(),
    locale,
  });

  const response = await fetch(`${VITE_BASE_URL}/catalogs/vehicle_type?${queryParams}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error obteniendo tipos de vehículos');
  }

  return data as CatalogItem[];
};

// searchPaginated
export const searchPaginatedVehicleType = async (params: SearchParams = {}): Promise<CatalogItem[]> => {
  const { lastId, limit = 10, deleted = false, disabled = false, locale = 'es', q } = params;

  const queryParams = new URLSearchParams({
    limit: limit.toString(),
    deleted: deleted.toString(),
    disabled: disabled.toString(),
    locale,
  });

  if (lastId) queryParams.append('lastId', lastId);
  if (q) queryParams.append('q', q);

  const response = await fetch(`${VITE_BASE_URL}/catalogs/vehicle_type/search?${queryParams}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error buscando tipos de vehículos');
  }

  return data as CatalogItem[];
};

/* ------------------------------------------------ color ---------------------------------------------------- */

// searchPaginated
export const searchPaginatedColor = async (params: SearchParams = {}): Promise<CatalogItem[]> => {
  const { lastId, limit = 10, deleted = false, disabled = false, locale = 'es', q } = params;

  const queryParams = new URLSearchParams({
    limit: limit.toString(),
    deleted: deleted.toString(),
    disabled: disabled.toString(),
    locale,
  });

  if (lastId) queryParams.append('lastId', lastId);
  if (q) queryParams.append('q', q);

  const response = await fetch(`${VITE_BASE_URL}/catalogs/color/search?${queryParams}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error buscando colores');
  }

  return data as CatalogItem[];
};

/* ------------------------------------------------ iph_incident_type ---------------------------------------------------- */

// getAll
export const getAllIncidentType = async (params: GetAllParams = {}): Promise<incidentTypeSchema[]> => {
  const { deleted = false, disabled = false, locale = 'es' } = params;

  const queryParams = new URLSearchParams({
    deleted: deleted.toString(),
    disabled: disabled.toString(),
    locale,
  });

  const response = await fetch(`${VITE_BASE_URL}/catalogs/iph_incident_type?${queryParams}`, {
    headers: {
      'Content-Type': 'application/json',
    },
  });

  const data = await response.json();

  if (!response.ok) {
    const error: ApiError = data;
    throw new FetchError(response, error.message || 'Error obteniendo tipos de incidentes');
  }
  return data
};
