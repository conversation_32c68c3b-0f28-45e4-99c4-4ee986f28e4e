import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/uniform_type.controller';

const catUniformTypeRouter = Router();

catUniformTypeRouter.get('/', getAll);
catUniformTypeRouter.get('/search', searchPaginated);
catUniformTypeRouter.get('/:id', getOne);
catUniformTypeRouter.post('/', create);
catUniformTypeRouter.put('/:id', update);
catUniformTypeRouter.delete('/:id', deleteOne);

export default catUniformTypeRouter;
