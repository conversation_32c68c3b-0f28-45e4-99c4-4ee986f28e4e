import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/iph_incident_type.controller';

const catIphIncidentTypeRouter = Router();

catIphIncidentTypeRouter.get('/', getAll);
catIphIncidentTypeRouter.get('/search', searchPaginated);
catIphIncidentTypeRouter.get('/:id', getOne);
catIphIncidentTypeRouter.post('/', create);
catIphIncidentTypeRouter.put('/:id', update);
catIphIncidentTypeRouter.delete('/:id', deleteOne);

export default catIphIncidentTypeRouter;
