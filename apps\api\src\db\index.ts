import { drizzle } from 'drizzle-orm/node-postgres';
import connectionString from './connection';
import { Pool } from 'pg';
import * as schema from '@repo/shared-drizzle/schemas';

const pool = new Pool({
  connectionString,
});

const db = drizzle(pool, {
  logger: process.env.NODE_ENV === 'development',
  // biome-ignore lint/suspicious/noExplicitAny: <explanation>
  schema: schema as Record<string, any>,
});

export default db;
