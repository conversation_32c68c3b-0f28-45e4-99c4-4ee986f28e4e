import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/iph_legal_crime_subtype.controller';

const catIphLegalCrimeSubtypeRouter = Router();

catIphLegalCrimeSubtypeRouter.get('/:type/search', searchPaginated);
catIphLegalCrimeSubtypeRouter.get('/:type', getAll);
catIphLegalCrimeSubtypeRouter.get('/:id', getOne);
catIphLegalCrimeSubtypeRouter.post('/', create);
catIphLegalCrimeSubtypeRouter.put('/:id', update);
catIphLegalCrimeSubtypeRouter.delete('/:id', deleteOne);

export default catIphLegalCrimeSubtypeRouter;
