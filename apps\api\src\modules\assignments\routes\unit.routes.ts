import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/unit.controller';

const unitAssignRouter = Router();

unitAssignRouter.get('/', getAll);
unitAssignRouter.get('/search', searchPaginated);
unitAssignRouter.get('/:id', getOne);
unitAssignRouter.post('/', create);
unitAssignRouter.put('/:id', update);
unitAssignRouter.delete('/:id', deleteOne);

export default unitAssignRouter;
