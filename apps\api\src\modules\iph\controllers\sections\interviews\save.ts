import type { Request, Response } from 'express';
import { catExternalInstitution, type Interview, systemModuleControlIphInterview } from '@repo/shared-drizzle/schemas';
import { HttpStatus } from '@/utils/http_status';
import { ModuleName } from '@/utils/module_names';
import { Logger } from '@/utils/logger';
import db from '@/db';
import z from 'zod';
import { interviewSchema } from '@repo/types/schemas';
import { getLocale, type Locale } from '@/utils/locale_validator';
import genrateKey from '@/utils/generate_key';
import { saveCitizen } from '../shared';

const validate = async ({
  unsafeBody,
  userId,
  dependencyId,
  locale,
  source,
}: {
  unsafeBody: z.infer<typeof interviewSchema>;
  userId: string;
  dependencyId: string;
  locale: Locale;
  source: string | undefined;
}) => {
  const error: { [key: string]: unknown } = {};
  try {
    const dataToUpdate: Interview = {
      fkIphId: unsafeBody.iphId,
      fkLastUserId: userId,
      fkLastDependencyId: dependencyId,
      isEnabled: true,
      updatedAt: new Date(),
    };
    // interviewAt
    const interviewAt = interviewSchema.shape.interviewAt.safeParse(unsafeBody.interviewAt);
    if (interviewAt.success) {
      dataToUpdate.interviewAt = interviewAt.data;
    } else {
      error.interviewAt = interviewAt.error;
    }
    // intervieweeType
    const intervieweeType = interviewSchema.shape.intervieweeType.safeParse(unsafeBody.intervieweeType);
    if (intervieweeType.success) {
      dataToUpdate.intervieweeType = intervieweeType.data;
    } else {
      error.intervieweeType = intervieweeType.error;
    }
    // interviewee
    if (unsafeBody.interviewee) {
      let id: string | null = unsafeBody.interviewee.id;
      if (unsafeBody.interviewee.action === 'create' || unsafeBody.interviewee.action === 'update') {
        const newCitizenError = await saveCitizen({
          unsafeData: unsafeBody.interviewee,
          userId,
          dependencyId,
          locale,
          objectPath: 'interviewee',
        });
        if (newCitizenError) {
          error.detainedData = newCitizenError;
          if (newCitizenError.id) {
            id = null;
          }
        }
      }
      dataToUpdate.fkIntervieweeCitizenId = id;
    }
    // story
    const story = interviewSchema.shape.story.safeParse(unsafeBody.story);
    if (story.success) {
      dataToUpdate.story = story.data;
    } else {
      error.story = story.error;
    }
    // notes
    const notes = interviewSchema.shape.notes.safeParse(unsafeBody.notes);
    if (notes.success) {
      dataToUpdate.notes = notes.data;
    } else {
      error.notes = notes.error;
    }
    // wasIntervieweeTransferred
    const wasIntervieweeTransferred = interviewSchema.shape.wasIntervieweeTransferred.safeParse(
      unsafeBody.wasIntervieweeTransferred,
    );
    if (wasIntervieweeTransferred.success) {
      dataToUpdate.wasIntervieweeTransferred = wasIntervieweeTransferred.data;
    } else {
      error.wasIntervieweeTransferred = wasIntervieweeTransferred.error;
    }
    // transferExternalInstitution
    const transferExternalInstitution = interviewSchema.shape.transferExternalInstitution.safeParse(
      unsafeBody.transferExternalInstitution,
    );
    if (transferExternalInstitution.success) {
      let id: string | undefined | null = transferExternalInstitution.data?.id;
      if (
        transferExternalInstitution.data &&
        (transferExternalInstitution.data.action === 'create' || transferExternalInstitution.data.action === 'update')
      ) {
        try {
          const res = await db.insert(catExternalInstitution).values({
            [locale]: transferExternalInstitution.data.name,
            key: genrateKey(transferExternalInstitution.data.name),
            fkLastUserId: userId,
            fkLastDependencyId: dependencyId,
          });

          if ((res.rowCount || 0) === 0) {
            id = undefined;
          }
        } catch (err) {
          const { message } = err as Error;
          id = undefined;
          error.transferExternalInstitution = z.ZodError.create([
            {
              fatal: true,
              message: message || 'Error creating institution',
              code: z.ZodIssueCode.custom,
              path: ['transferExternalInstitution', 'authority'],
            },
          ]);
        }
      }
      dataToUpdate.fkTransferExternalInstitutionId = id;
    } else {
      error.transferExternalInstitution = transferExternalInstitution.error;
    }
    // wasInformedOfRights
    const wasInformedOfRights = interviewSchema.shape.wasInformedOfRights.safeParse(unsafeBody.wasInformedOfRights);
    if (wasInformedOfRights.success) {
      dataToUpdate.wasInformedOfRights = wasInformedOfRights.data;
    } else {
      error.wasInformedOfRights = wasInformedOfRights.error;
    }
    // firstRespondent
    const firstRespondent = interviewSchema.shape.firstRespondent.safeParse(unsafeBody.firstRespondent);
    if (firstRespondent.success) {
      if (firstRespondent.data) {
        dataToUpdate.fkFirstRespondentUserId = firstRespondent.data.id;
        dataToUpdate.fkFirstRespondentPositionId = firstRespondent.data.position.id;
        dataToUpdate.fkFirstRespondentSecondmentId = firstRespondent.data.secondment.id;
      }
    } else {
      error.firstRespondent = firstRespondent.error;
    }
    // id
    const id = interviewSchema.shape.id.safeParse(unsafeBody.id);
    if (id.success && id.data) {
      dataToUpdate.id = id.data;
    }

    if (Object.keys(dataToUpdate).length < 6) {
      // if there is no data to update more than the 4 required fields return Not Modified status
      // res.status(HttpStatus.NOT_MODIFIED).json({ message: 'No data to update', error });
      // return;
      error.id = z.ZodError.create([
        {
          fatal: true,
          message: 'No data to update',
          code: z.ZodIssueCode.custom,
          path: ['id'],
        },
      ]);
    }
    const updated = await db.insert(systemModuleControlIphInterview).values(dataToUpdate).onConflictDoUpdate({
      target: systemModuleControlIphInterview.id,
      set: dataToUpdate,
    });
    if ((updated.rowCount || 0) === 0) {
      error.id = z.ZodError.create([
        {
          fatal: true,
          message: 'Error creating interview',
          code: z.ZodIssueCode.custom,
          path: ['id'],
        },
      ]);
    }
    if (Object.keys(error).length > 0) {
      return { error, success: !error.id };
    }
    return { success: !error.id };
  } catch (err) {
    const { message, stack } = err as Error;
    error.transferExternalInstitution = z.ZodError.create([
      {
        fatal: true,
        message: message || 'Error creating interview',
        code: z.ZodIssueCode.custom,
        path: ['interview'],
      },
    ]);
    Logger.getInstance().error(message, {
      module: ModuleName.IPH,
      method: '/iph/annex/interviews/save - validate',
      source,
      stack,
    });
    return { error, success: false };
  }
};

export const save = async (req: Request, res: Response) => {
  const userId = req.userId as string;
  const dependencyId = req.dependencyId as string;
  const { locale: localeQS } = req.query;
  let locale = getLocale();
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const unsafeBody = req.body as z.infer<typeof interviewSchema>;
    const result = await validate({
      unsafeBody,
      userId,
      dependencyId,
      locale,
      source: req.headers['user-agent'],
    });
    res.status(HttpStatus.CREATED).json(result);
  } catch (_error) {
    const { message, stack } = _error as Error;
    Logger.getInstance().error(message, {
      module: ModuleName.IPH,
      method: '/iph/annex/use_of_force/save - save',
      source: req.headers['user-agent'],
      stack,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message: message });
  }
};

export const saveAll = async (req: Request, res: Response) => {
  const userId = req.userId as string;
  const dependencyId = req.dependencyId as string;
  const { locale: localeQS } = req.query;
  let locale = getLocale();
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const arraySchema = z.array(interviewSchema);
    const unsafeBody = req.body as z.infer<typeof arraySchema>;
    const results = unsafeBody.map(async (unsafeItem) => {
      const result = await validate({
        unsafeBody: unsafeItem,
        userId,
        dependencyId,
        locale,
        source: req.headers['user-agent'],
      });
      return { [unsafeItem.id]: result };
    });
    const result = await Promise.all(results);
    res.status(HttpStatus.CREATED).json(result);
  } catch (_error) {
    const { message, stack } = _error as Error;
    Logger.getInstance().error(message, {
      module: ModuleName.IPH,
      method: '/iph/annex/use_of_force/save - save',
      source: req.headers['user-agent'],
      stack,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message: message });
  }
};
