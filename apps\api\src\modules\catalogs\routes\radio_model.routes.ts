import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/radio_model.controller';

const catRadioModelRouter = Router();

catRadioModelRouter.get('/', getAll);
catRadioModelRouter.get('/search', searchPaginated);
catRadioModelRouter.get('/:id', getOne);
catRadioModelRouter.post('/', create);
catRadioModelRouter.put('/:id', update);
catRadioModelRouter.delete('/:id', deleteOne);

export default catRadioModelRouter;
