import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/object_type.controller';

const catObjectTypeRouter = Router();

catObjectTypeRouter.get('/', getAll);
catObjectTypeRouter.get('/search', searchPaginated);
catObjectTypeRouter.get('/:id', getOne);
catObjectTypeRouter.post('/', create);
catObjectTypeRouter.put('/:id', update);
catObjectTypeRouter.delete('/:id', deleteOne);

export default catObjectTypeRouter;
