import { tacticalEquipmentStatusValues } from '@repo/types/values';
import { z } from 'zod';

export const tacticalEquipmentBodySchema = z.object({
  serialNumber: z.string(),
  fkTacticalEquipmentTypeId: z.string(),
  expirationDate: z.string().nullable().optional(),
  cost: z.number(),
  description: z.string().nullable().optional(),
  fkSizeId: z.string(),
  tacticalEquipmentStatus: z.enum(tacticalEquipmentStatusValues),
  acquisitionDate: z.string(),
  fkSupplierId: z.string().nullable().optional(),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});
