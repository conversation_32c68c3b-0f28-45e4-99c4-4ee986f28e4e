import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/external_institution.controller';

const catExternalInstitutionRouter = Router();

catExternalInstitutionRouter.get('/', getAll);
catExternalInstitutionRouter.get('/search', searchPaginated);
catExternalInstitutionRouter.get('/:id', getOne);
catExternalInstitutionRouter.post('/', create);
catExternalInstitutionRouter.put('/:id', update);
catExternalInstitutionRouter.delete('/:id', deleteOne);

export default catExternalInstitutionRouter;
