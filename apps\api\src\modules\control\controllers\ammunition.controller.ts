import type { Request, Response } from 'express';
import { catAmmunitionType, catWeaponCaliber, systemModuleControlAmmunition } from '@repo/shared-drizzle/schemas';
import db from '@/db';
import { and, asc, eq, gt, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { getLocale } from '@/utils/locale_validator';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import { ammunitionBodySchema } from '../helpers/ammunition.helper';
import z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS } = req.query;
  let [deleted, disabled, locale] = [false, false, getLocale()];

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlAmmunition.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlAmmunition.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlAmmunition.id,
        fkTypeAmmunitionId: systemModuleControlAmmunition.fkTypeAmmunitionId,
        nameTypeAmmunition: catAmmunitionType[locale],
        fkCaliberId: systemModuleControlAmmunition.fkCaliberId,
        nameCaliber: catWeaponCaliber[locale],
        quantity: systemModuleControlAmmunition.quantity,
      })
      .from(systemModuleControlAmmunition)
      .leftJoin(catAmmunitionType, eq(systemModuleControlAmmunition.fkTypeAmmunitionId, catAmmunitionType.id))
      .leftJoin(catWeaponCaliber, eq(systemModuleControlAmmunition.fkCaliberId, catWeaponCaliber.id))
      .where(and(...filters));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.AMMUNITION}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const result = await db
      .select({
        id: systemModuleControlAmmunition.id,
        fkTypeAmmunitionId: systemModuleControlAmmunition.fkTypeAmmunitionId,
        nameTypeAmmunition: catAmmunitionType[locale],
        fkCaliberId: systemModuleControlAmmunition.fkCaliberId,
        nameCaliber: catWeaponCaliber[locale],
        quantity: systemModuleControlAmmunition.quantity,
      })
      .from(systemModuleControlAmmunition)
      .leftJoin(catAmmunitionType, eq(systemModuleControlAmmunition.fkTypeAmmunitionId, catAmmunitionType.id))
      .leftJoin(catWeaponCaliber, eq(systemModuleControlAmmunition.fkCaliberId, catWeaponCaliber.id))
      .where(eq(systemModuleControlAmmunition.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.AMMUNITION}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const insertData = ammunitionBodySchema.parse({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_MUNICIONES);
    if (!systemModule) {
      throw new Error('System module CTRL_MUNICIONES not found');
    }
    const result = await db.insert(systemModuleControlAmmunition).values({
      ...insertData,
      fkSystemModuleId: systemModule.id,
    });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.AMMUNITION} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;
    // Validar payload
    const updateData = ammunitionBodySchema.parse({ ...req.body });
    const result = await db
      .update(systemModuleControlAmmunition)
      .set({ ...updateData })
      .where(eq(systemModuleControlAmmunition.id, id));
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.AMMUNITION}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .update(systemModuleControlAmmunition)
      .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
      .where(eq(systemModuleControlAmmunition.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.AMMUNITION}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    locale: localeQS,
    q: qQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlAmmunition.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlAmmunition.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(catAmmunitionType[locale], `%${q}%`));
      likeFilters.push(like(catWeaponCaliber[locale], `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlAmmunition.quantity}::text`, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleControlAmmunition.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlAmmunition.id,
        fkTypeAmmunitionId: systemModuleControlAmmunition.fkTypeAmmunitionId,
        nameTypeAmmunition: catAmmunitionType[locale],
        fkCaliberId: systemModuleControlAmmunition.fkCaliberId,
        nameCaliber: catWeaponCaliber[locale],
        quantity: systemModuleControlAmmunition.quantity,
      })
      .from(systemModuleControlAmmunition)
      .leftJoin(catAmmunitionType, eq(systemModuleControlAmmunition.fkTypeAmmunitionId, catAmmunitionType.id))
      .leftJoin(catWeaponCaliber, eq(systemModuleControlAmmunition.fkCaliberId, catWeaponCaliber.id))
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlAmmunition.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.AMMUNITION} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
