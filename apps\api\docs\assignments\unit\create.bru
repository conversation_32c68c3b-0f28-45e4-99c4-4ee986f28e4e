meta {
  name: create
  type: http
  seq: 5
}

post {
  url: {{base}}/assignments/unit?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

body:json {
  {
    "fkUserId": "ID Usuario",
    "fkUnitControlId": "ID Unidad",
    "fkFatigueId": "ID Fatiga/Despliegue",
    "isDriver": false, // Si tiene Rol de conductor
    "isPassenger": false, // Si tiene Rol de pasajero
    "isInCharge": false, // Si es el encargado de la unidad
    "assignmentDate": "Fecha asignación",
    "assignmentEndDate": "Fecha entrega o desasignación", // Opcional
    "estimatedReturnDate": "Fecha estimada de entrega", // Opcional
    "comments": "Comentarios o Notas" // Opcional
  }
}
