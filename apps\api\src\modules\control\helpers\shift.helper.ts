import db from '@/db';
import { systemModuleControlShift } from '@repo/shared-drizzle/schemas';
import { eq, not } from 'drizzle-orm';
import { z } from 'zod';

export const shiftBodySchema = z.object({
  shiftName: z.string(),
  startTime: z.string(),
  endTime: z.string(),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});

// Validar el formato de la hora
export const isValidTimeFormat = (time: string) => {
  const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
  return timeRegex.test(time);
};

// Función para convertir una hora en formato "hh:mm" a minutos desde la media noche
const convertTimeToMinutes = (time: string): number => {
  const [hours, minutes] = time.split(':').map(Number);
  return hours * 60 + minutes;
};

// Función para verificar si un bloque de tiempo se solapa con los existentes
export const isTimeBlockAvailable = async (
  startTime: string,
  endTime: string,
  excludeId?: string,
): Promise<boolean> => {
  const newStartTime = convertTimeToMinutes(startTime);
  let newEndTime = convertTimeToMinutes(endTime);
  // Si endTime es '00:00', lo tratamos como 1440 minutos (24:00)
  if (endTime === '00:00') newEndTime = 1440;

  // Obtener todos los bloques de tiempo ya registrados, excluyendo el que se está actualizando
  const existingShifts = await db
    .select({
      startTime: systemModuleControlShift.startTime,
      endTime: systemModuleControlShift.endTime,
    })
    .from(systemModuleControlShift)
    .where(excludeId ? not(eq(systemModuleControlShift.id, excludeId)) : undefined);

  // Verificar si algún bloque de tiempo se solapa con el nuevo bloque de tiempo
  for (const block of existingShifts) {
    const existingStartTime = convertTimeToMinutes(block.startTime);
    let existingEndTime = convertTimeToMinutes(block.endTime);

    // Si el bloque existente termina a '00:00', lo tratamos como 1440
    if (block.endTime === '00:00') existingEndTime = 1440;

    // Verificar si hay solapamiento
    if (
      (newStartTime < existingEndTime && newEndTime > existingStartTime) ||
      (newEndTime > 1440 && newStartTime < existingEndTime % 1440) ||
      (existingEndTime > 1440 && newStartTime < existingEndTime % 1440)
    ) {
      return false; // Hay solapamiento, no se puede crear el nuevo bloque de tiempo
    }
  }
  return true; // No hay solapamiento, se puede crear el nuevo bloque de tiempo
};
