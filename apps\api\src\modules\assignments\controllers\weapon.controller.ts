import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import {
  systemModuleAssignmentWeapon,
  systemModuleControlFatigue,
  systemModuleControlWeapon,
} from '@repo/shared-drizzle/schemas';
import {
  assignWeaponBodySchema,
  handleUserChangeInWeaponAssign,
  validateWeaponActiveAssignment,
  validateWeaponAssignmentInFatigue,
} from '../helpers/weapon.helper';
import moment from 'moment';
import SystemModuleCache from '@/utils/system_module_cache';

type WeaponAssignGrouped = Record<
  string,
  {
    fkUserId: string;
    data: {
      id: string;
      fkControlWeaponId: string;
      fkFatigueId: string;
      assignmentDate: string;
      assignmentEndDate: string | null;
      periodUse: number | null;
      comments: string | null;
    }[];
  }
>;

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, grouped: groupedQS } = req.query;
  let deleted = false;
  let disabled = false;
  let grouped = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentWeapon.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentWeapon.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentWeapon.id,
        fkUserId: systemModuleAssignmentWeapon.fkUserId,
        fkControlWeaponId: systemModuleAssignmentWeapon.fkControlWeaponId,
        fkFatigueId: systemModuleAssignmentWeapon.fkFatigueId,
        assignmentDate: systemModuleAssignmentWeapon.assignmentDate,
        assignmentEndDate: systemModuleAssignmentWeapon.assignmentEndDate,
        periodUse: systemModuleAssignmentWeapon.periodUse,
        comments: systemModuleAssignmentWeapon.comments,
      })
      .from(systemModuleAssignmentWeapon)
      .where(and(...filters));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkControlWeaponId: rest.fkControlWeaponId,
            fkFatigueId: rest.fkFatigueId,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            periodUse: rest.periodUse || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as WeaponAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleAssignmentWeapon.id,
        fkUserId: systemModuleAssignmentWeapon.fkUserId,
        fkControlWeaponId: systemModuleAssignmentWeapon.fkControlWeaponId,
        fkFatigueId: systemModuleAssignmentWeapon.fkFatigueId,
        assignmentDate: systemModuleAssignmentWeapon.assignmentDate,
        assignmentEndDate: systemModuleAssignmentWeapon.assignmentEndDate,
        periodUse: systemModuleAssignmentWeapon.periodUse,
        comments: systemModuleAssignmentWeapon.comments,
      })
      .from(systemModuleAssignmentWeapon)
      .where(eq(systemModuleAssignmentWeapon.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar assignmentDate
    const date = new Date(req.body.assignmentDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('El campo assignmentDate no es una fecha valida');
    }

    // Quitar del payload el campo assignmentEndDate, no considerar al crear la asignación
    const { assignmentEndDate, ...rest } = req.body;

    // Validar payload
    const insertData = await assignWeaponBodySchema.parseAsync({ ...rest });
    const result = await db.transaction(async (trx) => {
      // Validar la existencia del arma
      const weapon = await trx
        .select()
        .from(systemModuleControlWeapon)
        .where(eq(systemModuleControlWeapon.id, insertData.fkControlWeaponId))
        .limit(1);
      if (weapon.length === 0) {
        throw new Error('Arma no encontrada');
      }

      // Validar que no exista una asignación con la misma arma en el despliegue indicado
      const existingAssignment = await trx
        .select()
        .from(systemModuleAssignmentWeapon)
        .where(
          and(
            eq(systemModuleAssignmentWeapon.fkControlWeaponId, insertData.fkControlWeaponId),
            eq(systemModuleAssignmentWeapon.fkFatigueId, insertData.fkFatigueId),
          ),
        )
        .limit(1);
      if (existingAssignment.length > 0) {
        throw new Error('Ya existe una asignación con la misma arma en el despliegue indicado');
      }

      const systemModuleCache = SystemModuleCache.getInstance();
      const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_ASIGNACIONES_ARMAS);
      if (!systemModule) {
        throw new Error('System module CTRL_ASIGNACIONES_ARMAS not found');
      }
      const newAssignment = await trx.insert(systemModuleAssignmentWeapon).values({
        ...insertData,
        assignmentEndDate: null,
        fkSystemModuleId: systemModule.id,
      });
      // TODO: El registro en el kardex se realizará una vez la fatiga se inicie
      /*const insertArmamentKardex = kardexArmamentBodySchema.parse({
        fkUserId: insertData.fkUserId,
        fkWeaponTypeId: weapon[0].fkWeaponTypeId,
        serialNumber: weapon[0].serialNumber,
        assignmentDate: insertData.assignmentDate,
        weaponStatus: weapon[0].weaponStatus,
        fkLastUserId: insertData.fkLastUserId,
        fkLastDependencyId: insertData.fkLastDependencyId,
      });
      await trx.insert(systemModuleUserKardexArmament).values({ ...insertArmamentKardex });*/
      return newAssignment;
    });

    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar assignmentDate
    const date = new Date(req.body.assignmentDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('El campo assignmentDate no es una fecha valida');
    }

    // Validar unassionEndDate (si existe)
    if (req.body.assignmentEndDate) {
      const dateEnd = new Date(req.body.assignmentEndDate.replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(dateEnd.getTime())) {
        throw new Error('El campo assignmentEndDate no es una fecha valida');
      }
      if (dateEnd < date) {
        throw new Error('La fecha de fin de asignación no puede ser anterior a la fecha de inicio');
      }
    }

    // Validar payload
    const updateData = await assignWeaponBodySchema.parseAsync({ ...req.body });
    const result = await db.transaction(async (trx) => {
      // Obtener registro de asignación
      const assignment = await trx
        .select()
        .from(systemModuleAssignmentWeapon)
        .where(eq(systemModuleAssignmentWeapon.id, id))
        .limit(1);
      if (assignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }
      // Validar la existencia de la arma
      const weapon = await trx
        .select()
        .from(systemModuleControlWeapon)
        .where(eq(systemModuleControlWeapon.id, updateData.fkControlWeaponId))
        .limit(1);
      if (weapon.length === 0) {
        throw new Error('Arma no encontrada');
      }

      // Validar si cambio de arma, que no este en el mismo despliegue ya asignado
      if (updateData.fkControlWeaponId !== assignment[0].fkControlWeaponId) {
        await validateWeaponAssignmentInFatigue(trx, updateData.fkControlWeaponId, updateData.fkFatigueId, id);
      }

      // Validar si el arma tiene una asignación activa
      await validateWeaponActiveAssignment(trx, updateData.fkControlWeaponId, updateData.fkFatigueId);

      // Verificar si el usuario cambió
      if (updateData.fkUserId !== assignment[0].fkUserId) {
        await handleUserChangeInWeaponAssign(
          trx,
          updateData.fkUserId,
          updateData.fkControlWeaponId,
          updateData.fkFatigueId,
          id,
        );
      }

      // Calcular el periodo de uso si se proporciona assignmentEndDate
      let periodUse = assignment[0].periodUse;
      if (updateData.assignmentEndDate) {
        const diffDays = moment(updateData.assignmentEndDate).diff(moment(updateData.assignmentDate), 'days');
        periodUse = diffDays;
      }

      // Actualizar la asignación
      const assignUpdate = await trx
        .update(systemModuleAssignmentWeapon)
        .set({ ...updateData, periodUse })
        .where(eq(systemModuleAssignmentWeapon.id, id));

      return assignUpdate;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Validar que exista la asignación
      const existingAssignment = await trx
        .select()
        .from(systemModuleAssignmentWeapon)
        .where(eq(systemModuleAssignmentWeapon.id, id))
        .limit(1);
      if (existingAssignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar que la asignación no este en un despliegue activo
      const activeWeaponAssignment = await trx
        .select()
        .from(systemModuleAssignmentWeapon)
        .leftJoin(
          systemModuleControlFatigue,
          eq(systemModuleAssignmentWeapon.fkFatigueId, systemModuleControlFatigue.id),
        )
        .where(
          and(
            eq(systemModuleControlFatigue.status, 'active'),
            eq(systemModuleAssignmentWeapon.fkFatigueId, existingAssignment[0].fkFatigueId),
            eq(systemModuleAssignmentWeapon.id, id),
          ),
        )
        .limit(1);
      if (activeWeaponAssignment.length > 0) {
        throw new Error('No es posible eliminar la asignación si este ya se encuentra en un despliegue activo');
      }

      const deleteResult = await trx
        .update(systemModuleAssignmentWeapon)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleAssignmentWeapon.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    q: qQS,
    grouped: groupedQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;
  let grouped = false;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentWeapon.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentWeapon.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(sql`${systemModuleAssignmentWeapon.assignmentDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentWeapon.assignmentEndDate}::text`, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleAssignmentWeapon.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentWeapon.id,
        fkUserId: systemModuleAssignmentWeapon.fkUserId,
        fkControlWeaponId: systemModuleAssignmentWeapon.fkControlWeaponId,
        fkFatigueId: systemModuleAssignmentWeapon.fkFatigueId,
        assignmentDate: systemModuleAssignmentWeapon.assignmentDate,
        assignmentEndDate: systemModuleAssignmentWeapon.assignmentEndDate,
        periodUse: systemModuleAssignmentWeapon.periodUse,
        comments: systemModuleAssignmentWeapon.comments,
      })
      .from(systemModuleAssignmentWeapon)
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleAssignmentWeapon.id));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkControlWeaponId: rest.fkControlWeaponId,
            fkFatigueId: rest.fkFatigueId,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            periodUse: rest.periodUse || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as WeaponAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.WEAPON} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
