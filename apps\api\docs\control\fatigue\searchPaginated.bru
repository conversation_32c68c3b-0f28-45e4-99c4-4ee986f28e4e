meta {
  name: searchPaginated
  type: http
  seq: 2
}

get {
  url: {{base}}/control/fatigue/search?limit=10&deleted=false&disabled=false&grouped=true&startDate=2025-03-01&endDate=2025-03-25&assigned=true
  body: none
  auth: none
}

params:query {
  limit: 10
  deleted: false
  disabled: false
  grouped: true
  startDate: 2025-03-01
  endDate: 2025-03-25
  assigned: true
  ~groupedBy: status | shift | user | closed
  ~lastId: 
  ~q: Prueba
}
