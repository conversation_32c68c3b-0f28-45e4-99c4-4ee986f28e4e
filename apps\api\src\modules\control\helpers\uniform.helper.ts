import { uniformStatusValues } from '@repo/types/values';
import { z } from 'zod';

export const uniformBodySchema = z.object({
  serialNumber: z.string(),
  fkUniformTypeId: z.string(),
  fkSizeId: z.string(),
  fkColorId: z.string(),
  status: z.enum(uniformStatusValues),
  expirationDate: z.string().nullable().optional(),
  acquisitionDate: z.string(),
  cost: z.number(),
  fkSupplierId: z.string().nullable().optional(),
  description: z.string().nullable().optional(),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});
