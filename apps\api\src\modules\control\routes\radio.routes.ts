import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/radio.controller';

const radioRouter = Router();

radioRouter.get('/', getAll);
radioRouter.get('/search', searchPaginated);
radioRouter.get('/:id', getOne);
radioRouter.post('/', create);
radioRouter.put('/:id', update);
radioRouter.delete('/:id', deleteOne);

export default radioRouter;
