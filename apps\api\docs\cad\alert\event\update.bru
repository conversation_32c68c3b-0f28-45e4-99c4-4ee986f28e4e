meta {
  name: update
  type: http
  seq: 5
}

put {
  url: {{cad}}/alert/event/:id
  body: json
  auth: inherit
}

params:path {
  id: 
}

body:json {
  {
    "fkIncidentTypeId": "01967ebf-0421-8d33-3eb3-2ee444fb6c08",
    "incidentDescription": "Individuo, probablemente en estado de ebriedad, se encuentra recostado fuera del kiosko frente bodega aurrera en Av. <PERSON>",
    "fkReceptorUserId": "0195209d-c94a-f8d0-0bce-8c1f4a7e5ab2",
    //"fkAlertInformantUserId": "ID del Informante", // Opcional
    "alertType": "manual_alert", // ['manual_alert', 'citizen_alert']
    "priorityType": "Tipo de prioridad", // Opcional - ['no_priority', 'low', 'medium', 'high', 'critical']
    "alertSource": "radio", // ['radio', 'municipal_telephone', '911', 'direct_complaint', 'app']
    "location": [{ "lng": -103.742044, "lat": 19.253751 }], // Opcional | Formato esperado { "lng": -121, "lat": 37 }
    //"address": "Datos de domicilio", // Opcional | Todas las propiedades de domicilio
    "alertDate": "2025-05-12", // Formato YYYY-MM-DD
    "alertTime": "12:35" // Formato hh:mm
    //"commentsOnRejection": "Comentarios sobre rechazo" // Opcional | Requerido si el estatus es 'canceled'
  }
}
