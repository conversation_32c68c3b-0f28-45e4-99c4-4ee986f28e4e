import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import {
  catTacticalEquipmentType,
  systemModuleAssignmentTacticalEquipment,
  systemModuleControlFatigue,
  systemModuleControlTacticalEquipment,
} from '@repo/shared-drizzle/schemas';
import {
  assignTacticalEquipmentBodySchema,
  handleUserChangeInTacticalEquipmentAssig,
  validateDates,
  validateTacticalEquipmentActiveAssignment,
  validateTacticalEquipmentAssignmentInFatigue,
} from '../helpers/tactical_equipment.helper';
import SystemModuleCache from '@/utils/system_module_cache';
import { getLocale } from '@/utils/locale_validator';
import moment from 'moment';

type TacticalEquipmentAssignGrouped = Record<
  string,
  {
    fkUserId: string;
    data: {
      id: string;
      fkTacticalEquipmentId: string;
      nameTacticalEquipment: string | null;
      fkFatigueId: string;
      assignmentDate: string;
      assignmentEndDate: string | null;
      estimatedReturnDate: string | null;
      periodUse: number | null;
      comments: string | null;
    }[];
  }
>;

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS, grouped: groupedQS } = req.query;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let grouped = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentTacticalEquipment.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentTacticalEquipment.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentTacticalEquipment.id,
        fkUserId: systemModuleAssignmentTacticalEquipment.fkUserId,
        fkTacticalEquipmentId: systemModuleAssignmentTacticalEquipment.fkTacticalEquipmentId,
        nameTacticalEquipment: catTacticalEquipmentType[locale],
        fkFatigueId: systemModuleAssignmentTacticalEquipment.fkFatigueId,
        assignmentDate: systemModuleAssignmentTacticalEquipment.assignmentDate,
        assignmentEndDate: systemModuleAssignmentTacticalEquipment.assignmentEndDate,
        estimatedReturnDate: systemModuleAssignmentTacticalEquipment.estimatedReturnDate,
        periodUse: systemModuleAssignmentTacticalEquipment.periodUse,
        comments: systemModuleAssignmentTacticalEquipment.comments,
      })
      .from(systemModuleAssignmentTacticalEquipment)
      .leftJoin(
        systemModuleControlTacticalEquipment,
        eq(systemModuleAssignmentTacticalEquipment.fkTacticalEquipmentId, systemModuleControlTacticalEquipment.id),
      )
      .leftJoin(
        catTacticalEquipmentType,
        eq(systemModuleControlTacticalEquipment.fkTacticalEquipmentTypeId, catTacticalEquipmentType.id),
      )
      .where(and(...filters));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkTacticalEquipmentId: rest.fkTacticalEquipmentId,
            nameTacticalEquipment: rest.nameTacticalEquipment || null,
            fkFatigueId: rest.fkFatigueId,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            estimatedReturnDate: rest.estimatedReturnDate || null,
            periodUse: rest.periodUse || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as TacticalEquipmentAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TACTICAL_EQUIPMENT}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const result = await db
      .select({
        id: systemModuleAssignmentTacticalEquipment.id,
        fkUserId: systemModuleAssignmentTacticalEquipment.fkUserId,
        fkTacticalEquipmentId: systemModuleAssignmentTacticalEquipment.fkTacticalEquipmentId,
        nameTacticalEquipment: catTacticalEquipmentType[locale] || null,
        fkFatigueId: systemModuleAssignmentTacticalEquipment.fkFatigueId,
        assignmentDate: systemModuleAssignmentTacticalEquipment.assignmentDate,
        assignmentEndDate: systemModuleAssignmentTacticalEquipment.assignmentEndDate,
        estimatedReturnDate: systemModuleAssignmentTacticalEquipment.estimatedReturnDate,
        periodUse: systemModuleAssignmentTacticalEquipment.periodUse,
        comments: systemModuleAssignmentTacticalEquipment.comments,
      })
      .from(systemModuleAssignmentTacticalEquipment)
      .leftJoin(
        systemModuleControlTacticalEquipment,
        eq(systemModuleAssignmentTacticalEquipment.fkTacticalEquipmentId, systemModuleControlTacticalEquipment.id),
      )
      .leftJoin(
        catTacticalEquipmentType,
        eq(systemModuleControlTacticalEquipment.fkTacticalEquipmentTypeId, catTacticalEquipmentType.id),
      )
      .where(eq(systemModuleAssignmentTacticalEquipment.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TACTICAL_EQUIPMENT}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar assignmentDate
    const date = new Date(req.body.assignmentDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('El campo assignmentDate no es una fecha valida');
    }

    // Validar estimatedReturnDate
    if (req.body.estimatedReturnDate) {
      const dateEnd = new Date(req.body.estimatedReturnDate.replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(dateEnd.getTime())) {
        throw new Error('El campo estimatedReturnDate no es una fecha valida');
      }
      if (dateEnd < date) {
        throw new Error('La fecha estimada de retorno no puede ser anterior a la fecha de inicio');
      }
    }

    // Quitar del payload el campo assignmentEndDate, no considerar al crear la asignación
    const { assignmentEndDate, ...rest } = req.body;

    // Validar payload
    const insertData = await assignTacticalEquipmentBodySchema.parseAsync({ ...rest });
    const result = await db.transaction(async (trx) => {
      // Validar la existencia de equipo táctico
      const tactical_equipment = await trx
        .select()
        .from(systemModuleControlTacticalEquipment)
        .where(eq(systemModuleControlTacticalEquipment.id, insertData.fkTacticalEquipmentId))
        .limit(1);
      if (tactical_equipment.length === 0) {
        throw new Error('Equipo táctico no encontrado');
      }

      // Validar que no exista una asignación con la misma equipo táctico en el despliegue indicado
      const existingAssignment = await trx
        .select()
        .from(systemModuleAssignmentTacticalEquipment)
        .where(
          and(
            eq(systemModuleAssignmentTacticalEquipment.fkTacticalEquipmentId, insertData.fkTacticalEquipmentId),
            eq(systemModuleAssignmentTacticalEquipment.fkFatigueId, insertData.fkFatigueId),
          ),
        )
        .limit(1);

      if (existingAssignment.length > 0) {
        throw new Error('Ya existe una asignación con la misma equipo táctico en el despliegue indicado');
      }

      const systemModuleCache = SystemModuleCache.getInstance();
      const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_ASIGNACIONES_EQUIPO_TACTICO);
      if (!systemModule) {
        throw new Error('System module CTRL_ASIGNACIONES_EQUIPO_TACTICO not found');
      }
      const newAssignment = await trx.insert(systemModuleAssignmentTacticalEquipment).values({
        ...insertData,
        assignmentEndDate: null,
        fkSystemModuleId: systemModule.id,
      });
      /* TODO: El registro en el kardex se realizará una vez la fatiga se inicie
      // Registrar kardex de equipo táctico
      const insertTacticalEquipmentKardex = await kardexTacticalEquipmentBodySchema.parseAsync({
        fkUserId: insertData.fkUserId,
        fkTacticalEquipmentId: tactical_equipment[0].id,
        assignmentDate: insertData.assignmentDate,
        assignmentEndDate: insertData.assignmentEndDate,
        expirationDate: insertData.expirationDate,
        estimatedReturnDate: insertData.estimatedReturnDate,
        fkSizeId: tactical_equipment[0].fkSizeId,
        tacticalEquipmentStatus: 'in_use',
        fkLastUserId: insertData.fkLastUserId,
        fkLastDependencyId: insertData.fkLastDependencyId,
      });
      await trx.insert(systemModuleUserKardexTacticalEquipment).values({ ...insertTacticalEquipmentKardex });
      */
      // Registrar asignación de equipo táctico
      return newAssignment;
    });

    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TACTICAL_EQUIPMENT} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar fechas
    validateDates(req.body);

    // Validar payload
    const updateData = await assignTacticalEquipmentBodySchema.parseAsync({ ...req.body });
    const result = await db.transaction(async (trx) => {
      // Obtener registro de asignación
      const assignment = await trx
        .select()
        .from(systemModuleAssignmentTacticalEquipment)
        .where(eq(systemModuleAssignmentTacticalEquipment.id, id))
        .limit(1);
      if (assignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar la existencia del equipo táctico
      const tacticalEquipment = await trx
        .select()
        .from(systemModuleControlTacticalEquipment)
        .where(eq(systemModuleControlTacticalEquipment.id, updateData.fkTacticalEquipmentId))
        .limit(1);
      if (tacticalEquipment.length === 0) {
        throw new Error('Equipo táctico no encontrado');
      }

      // Validar si cambio de arma, que no este en el mismo despliegue ya asignado
      if (updateData.fkTacticalEquipmentId !== assignment[0].fkTacticalEquipmentId) {
        await validateTacticalEquipmentAssignmentInFatigue(
          trx,
          updateData.fkTacticalEquipmentId,
          updateData.fkFatigueId,
          id,
        );
      }

      // Validar si el equipo táctico tiene una asignación activa
      await validateTacticalEquipmentActiveAssignment(trx, updateData.fkTacticalEquipmentId, id);

      // Verificar si el usuario cambió
      if (updateData.fkUserId !== assignment[0].fkUserId) {
        await handleUserChangeInTacticalEquipmentAssig(
          trx,
          updateData.fkUserId,
          updateData.fkTacticalEquipmentId,
          updateData.fkFatigueId,
          id,
        );
      }

      // Calcular el periodo de uso si se proporciona assignmentEndDate
      let periodUse = assignment[0].periodUse;
      if (updateData.assignmentEndDate) {
        const diffDays = moment(updateData.assignmentEndDate).diff(moment(updateData.assignmentDate), 'days');
        periodUse = diffDays;
      }

      // Actualizar la asignación
      const assignUpdate = await trx
        .update(systemModuleAssignmentTacticalEquipment)
        .set({ ...updateData, periodUse })
        .where(eq(systemModuleAssignmentTacticalEquipment.id, id));

      return assignUpdate;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TACTICAL_EQUIPMENT}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Validar que exista la asignación
      const existingAssignment = await trx
        .select()
        .from(systemModuleAssignmentTacticalEquipment)
        .where(eq(systemModuleAssignmentTacticalEquipment.id, id))
        .limit(1);
      if (existingAssignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar que la asignación no este en un despliegue activo
      const activeTacticalEquipmentAssignment = await trx
        .select()
        .from(systemModuleAssignmentTacticalEquipment)
        .leftJoin(
          systemModuleControlFatigue,
          eq(systemModuleAssignmentTacticalEquipment.fkFatigueId, systemModuleControlFatigue.id),
        )
        .where(
          and(
            eq(systemModuleControlFatigue.status, 'active'),
            eq(systemModuleAssignmentTacticalEquipment.fkFatigueId, existingAssignment[0].fkFatigueId),
            eq(systemModuleAssignmentTacticalEquipment.id, id),
          ),
        )
        .limit(1);
      if (activeTacticalEquipmentAssignment.length > 0) {
        throw new Error('No es posible eliminar la asignación si este ya se encuentra en un despliegue activo');
      }

      const deleteResult = await trx
        .update(systemModuleAssignmentTacticalEquipment)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleAssignmentTacticalEquipment.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TACTICAL_EQUIPMENT}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    locale: localeQS,
    q: qQS,
    grouped: groupedQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let q = null;
  let grouped = false;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentTacticalEquipment.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentTacticalEquipment.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(catTacticalEquipmentType[locale], `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentTacticalEquipment.assignmentDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentTacticalEquipment.assignmentEndDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentTacticalEquipment.estimatedReturnDate}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleAssignmentTacticalEquipment.comments, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleAssignmentTacticalEquipment.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentTacticalEquipment.id,
        fkUserId: systemModuleAssignmentTacticalEquipment.fkUserId,
        fkTacticalEquipmentId: systemModuleAssignmentTacticalEquipment.fkTacticalEquipmentId,
        nameTacticalEquipment: catTacticalEquipmentType[locale],
        fkFatigueId: systemModuleAssignmentTacticalEquipment.fkFatigueId,
        assignmentDate: systemModuleAssignmentTacticalEquipment.assignmentDate,
        assignmentEndDate: systemModuleAssignmentTacticalEquipment.assignmentEndDate,
        estimatedReturnDate: systemModuleAssignmentTacticalEquipment.estimatedReturnDate,
        periodUse: systemModuleAssignmentTacticalEquipment.periodUse,
        comments: systemModuleAssignmentTacticalEquipment.comments,
      })
      .from(systemModuleAssignmentTacticalEquipment)
      .leftJoin(
        systemModuleControlTacticalEquipment,
        eq(systemModuleAssignmentTacticalEquipment.fkTacticalEquipmentId, systemModuleControlTacticalEquipment.id),
      )
      .leftJoin(
        catTacticalEquipmentType,
        eq(systemModuleControlTacticalEquipment.fkTacticalEquipmentTypeId, catTacticalEquipmentType.id),
      )
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleAssignmentTacticalEquipment.id));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkTacticalEquipmentId: rest.fkTacticalEquipmentId,
            nameTacticalEquipment: rest.nameTacticalEquipment || null,
            fkFatigueId: rest.fkFatigueId,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            estimatedReturnDate: rest.estimatedReturnDate || null,
            periodUse: rest.periodUse || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as TacticalEquipmentAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.TACTICAL_EQUIPMENT} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
