import generateAuthToken from '@/middleware/token.middleware';
import { Logger } from '@/utils/logger';
import type { Request, Response } from 'express';

export const generateToken = async (req: Request, res: Response) => {
  const { userid, dependencyid } = req.headers;
  try {
    // Verificar si las propiedades están presentes en el header
    if (!userid || !dependencyid) {
      res.status(400).json({ success: false, message: 'Missing responsibility header in token' });
      return;
    }

    // Generar el token
    const token = await generateAuthToken(userid as string, dependencyid as string);

    // Devolver el token
    res.status(200).json({ token });
    return;
  } catch (err) {
    const { message, stack } = err as Error;
    Logger.getInstance().error(message, {
      method: 'generateToken',
      module: 'controllers',
      source: req.headers['user-agent'],
      stack: stack,
    });
    res.status(500).json({ status: 'error', message: 'Internal server error' });
    return;
  }
};

export const refreshToken = async (req: Request, res: Response) => {
  // TODO: implementar refreshToken
};

export const revokeToken = async (req: Request, res: Response) => {
  // TODO: implementar revokeToken
};
