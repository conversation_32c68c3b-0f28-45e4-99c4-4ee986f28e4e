export interface UnitAssignment {
  fkUserId: string;
  fkUnitControlId: string;
  fkFatigueId: string;
  isDriver: boolean;
  isPassenger: boolean;
  isInCharge: boolean;
  assignmentDate: string;
  assignmentEndDate: string | null;
  estimatedReturnDate: string | null;
  periodUse: number | null;
  comments: string | null;
  fkLastUserId: string;
  fkLastDependencyId: string;
  isEnabled?: boolean;
  isDeleted?: boolean;
  deletedAt?: string | null;
  createdAt?: string;
  updatedAt?: string;
}
