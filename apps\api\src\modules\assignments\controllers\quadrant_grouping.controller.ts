import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';
import {
  systemModuleAssignmentQuadrantGrouping,
  systemModuleControlFatigue,
  systemModuleControlQuadrantGrouping,
  systemModuleUser,
} from '@repo/shared-drizzle/schemas';
import {
  handleUserChangeInQuadrantGroupingAssign,
  quadrantGroupingAssignmentBodySchema,
  validateQuadrantGroupingActiveAssignment,
  validateQuadrantGroupingAssignmentInFatigue,
} from '../helpers/quadrant_grouping.helper';

type QuadrantGroupingAssignGrouped = Record<
  string,
  {
    fkUserId: string;
    alias: string | null;
    data: {
      id: string;
      fkQuadrantGroupingId: string;
      fkFatigueId: string;
      assignmentDate: string;
      assignmentEndDate: string | null;
      comments: string | null;
    }[];
  }
>;

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, grouped: groupedQS } = req.query;
  let deleted = false;
  let disabled = false;
  let grouped = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentQuadrantGrouping.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentQuadrantGrouping.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentQuadrantGrouping.id,
        fkUserId: systemModuleAssignmentQuadrantGrouping.fkUserId,
        alias: systemModuleUser.alias,
        fkQuadrantGroupingId: systemModuleAssignmentQuadrantGrouping.fkQuadrantGroupingId,
        fkFatigueId: systemModuleAssignmentQuadrantGrouping.fkFatigueId,
        assignmentDate: systemModuleAssignmentQuadrantGrouping.assignmentDate,
        assignmentEndDate: systemModuleAssignmentQuadrantGrouping.assignmentEndDate,
        periodUse: systemModuleAssignmentQuadrantGrouping.periodUse,
        comments: systemModuleAssignmentQuadrantGrouping.comments,
      })
      .from(systemModuleAssignmentQuadrantGrouping)
      .leftJoin(systemModuleUser, eq(systemModuleAssignmentQuadrantGrouping.fkUserId, systemModuleUser.id))
      .where(and(...filters));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, alias, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            alias,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkQuadrantGroupingId: rest.fkQuadrantGroupingId,
            fkFatigueId: rest.fkFatigueId,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as QuadrantGroupingAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.QUADRANT_GROUPING}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleAssignmentQuadrantGrouping.id,
        fkUserId: systemModuleAssignmentQuadrantGrouping.fkUserId,
        alias: systemModuleUser.alias,
        fkQuadrantGroupingId: systemModuleAssignmentQuadrantGrouping.fkQuadrantGroupingId,
        fkFatigueId: systemModuleAssignmentQuadrantGrouping.fkFatigueId,
        assignmentDate: systemModuleAssignmentQuadrantGrouping.assignmentDate,
        assignmentEndDate: systemModuleAssignmentQuadrantGrouping.assignmentEndDate,
        periodUse: systemModuleAssignmentQuadrantGrouping.periodUse,
        comments: systemModuleAssignmentQuadrantGrouping.comments,
      })
      .from(systemModuleAssignmentQuadrantGrouping)
      .leftJoin(systemModuleUser, eq(systemModuleAssignmentQuadrantGrouping.fkUserId, systemModuleUser.id))
      .where(eq(systemModuleAssignmentQuadrantGrouping.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.QUADRANT_GROUPING}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar assignmentDate
    const date = new Date(req.body.assignmentDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('El campo assignmentDate no es una fecha valida');
    }

    // Quitar del payload el campo assignmentEndDate, no considerar al crear la asignación
    const { assignmentEndDate, ...rest } = req.body;

    // Validar payload
    const insertData = await quadrantGroupingAssignmentBodySchema.parseAsync({ ...rest });
    const result = await db.transaction(async (trx) => {
      // Validar la existencia del cuadrante de operación
      const quadrantGrouping = await trx
        .select()
        .from(systemModuleControlQuadrantGrouping)
        .where(eq(systemModuleControlQuadrantGrouping.id, insertData.fkQuadrantGroupingId))
        .limit(1);
      if (quadrantGrouping.length === 0) {
        throw new Error('Agrupación de cuadrantes no encontrado');
      }

      // Validar que no exista una asignación con el mismo cuadrante de operación en el despliegue indicado
      const existingAssignment = await trx
        .select()
        .from(systemModuleAssignmentQuadrantGrouping)
        .where(
          and(
            eq(systemModuleAssignmentQuadrantGrouping.fkQuadrantGroupingId, insertData.fkQuadrantGroupingId),
            eq(systemModuleAssignmentQuadrantGrouping.fkFatigueId, insertData.fkFatigueId),
          ),
        )
        .limit(1);
      if (existingAssignment.length > 0) {
        throw new Error('Ya existe una asignación con el mismo cuadrante de operación en el despliegue indicado');
      }

      const systemModuleCache = SystemModuleCache.getInstance();
      const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_ASIGNACIONES_GRUPO_CUADRANTES);
      if (!systemModule) {
        throw new Error('System module CTRL_ASIGNACIONES_GRUPO_CUADRANTES not found');
      }
      const newAssignment = await trx.insert(systemModuleAssignmentQuadrantGrouping).values({
        ...insertData,
        assignmentEndDate: null,
        fkSystemModuleId: systemModule.id,
      });
      return newAssignment;
    });

    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.QUADRANT_GROUPING} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar assignmentDate
    const assignmentDate = new Date(req.body.assignmentDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(assignmentDate.getTime())) {
      throw new Error('El campo assignmentDate no es una fecha valida');
    }
    // Validar assignmentEndDate
    if (req.body.assignmentEndDate) {
      const assignmentEndDate = new Date(req.body.assignmentEndDate.replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(assignmentEndDate.getTime())) {
        throw new Error('El campo assignmentEndDate no es una fecha valida');
      }
      if (assignmentEndDate < assignmentDate) {
        throw new Error('El campo assignmentEndDate no puede ser anterior al campo assignmentDate');
      }
    }

    // Validar payload
    const updateData = await quadrantGroupingAssignmentBodySchema.parseAsync({ ...req.body });
    const result = await db.transaction(async (trx) => {
      // Obtener registro de asignación
      const assignment = await trx
        .select()
        .from(systemModuleAssignmentQuadrantGrouping)
        .where(eq(systemModuleAssignmentQuadrantGrouping.id, id))
        .limit(1);
      if (assignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar la existencia de la agrupación de cuadrantes
      const quadrantGrouping = await trx
        .select()
        .from(systemModuleControlQuadrantGrouping)
        .where(eq(systemModuleControlQuadrantGrouping.id, updateData.fkQuadrantGroupingId))
        .limit(1);
      if (quadrantGrouping.length === 0) {
        throw new Error('Agrupación de cuadrantes no encontrado');
      }

      // Validar si cambio de agrupación de cuadrantes, que no este en el mismo despliegue ya asignado
      if (updateData.fkQuadrantGroupingId !== assignment[0].fkQuadrantGroupingId) {
        await validateQuadrantGroupingAssignmentInFatigue(
          trx,
          updateData.fkQuadrantGroupingId,
          updateData.fkFatigueId,
          id,
        );
      }

      // Validar si el agrupador de cuadrantes tiene una asignación activa
      await validateQuadrantGroupingActiveAssignment(trx, updateData.fkQuadrantGroupingId, updateData.fkFatigueId);

      // Verificar si el usuario cambió
      if (updateData.fkUserId !== assignment[0].fkUserId) {
        await handleUserChangeInQuadrantGroupingAssign(
          trx,
          updateData.fkUserId,
          updateData.fkQuadrantGroupingId,
          updateData.fkFatigueId,
          id,
        );
      }

      // Actualizar la asignación del grupo de cuadrantes
      const assignUpdate = await trx
        .update(systemModuleAssignmentQuadrantGrouping)
        .set({ ...updateData })
        .where(eq(systemModuleAssignmentQuadrantGrouping.id, id));

      return assignUpdate;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.QUADRANT_GROUPING}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Validar la existencia de la asignación
      const existingAssignment = await trx
        .select()
        .from(systemModuleAssignmentQuadrantGrouping)
        .where(eq(systemModuleAssignmentQuadrantGrouping.id, id))
        .limit(1);

      if (existingAssignment.length > 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar que la asignación no este en un despliegue activo
      const activeQuadrantGroupingAssignment = await trx
        .select()
        .from(systemModuleAssignmentQuadrantGrouping)
        .leftJoin(
          systemModuleControlFatigue,
          eq(systemModuleAssignmentQuadrantGrouping.fkFatigueId, systemModuleControlFatigue.id),
        )
        .where(
          and(
            eq(systemModuleControlFatigue.status, 'active'),
            eq(systemModuleAssignmentQuadrantGrouping.fkFatigueId, existingAssignment[0].fkFatigueId),
            eq(systemModuleAssignmentQuadrantGrouping.id, id),
          ),
        )
        .limit(1);
      if (activeQuadrantGroupingAssignment.length > 0) {
        throw new Error('No es posible eliminar la asignación si este ya se encuentra en un despliegue activo');
      }

      const deleteResult = await trx
        .update(systemModuleAssignmentQuadrantGrouping)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleAssignmentQuadrantGrouping.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.QUADRANT_GROUPING}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    q: qQS,
    grouped: groupedQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;
  let grouped = false;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentQuadrantGrouping.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentQuadrantGrouping.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleUser.alias, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentQuadrantGrouping.assignmentDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentQuadrantGrouping.assignmentEndDate}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleAssignmentQuadrantGrouping.comments, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleAssignmentQuadrantGrouping.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentQuadrantGrouping.id,
        fkUserId: systemModuleAssignmentQuadrantGrouping.fkUserId,
        alias: systemModuleUser.alias,
        fkQuadrantGroupingId: systemModuleAssignmentQuadrantGrouping.fkQuadrantGroupingId,
        fkFatigueId: systemModuleAssignmentQuadrantGrouping.fkFatigueId,
        assignmentDate: systemModuleAssignmentQuadrantGrouping.assignmentDate,
        assignmentEndDate: systemModuleAssignmentQuadrantGrouping.assignmentEndDate,
        periodUse: systemModuleAssignmentQuadrantGrouping.periodUse,
        comments: systemModuleAssignmentQuadrantGrouping.comments,
      })
      .from(systemModuleAssignmentQuadrantGrouping)
      .leftJoin(systemModuleUser, eq(systemModuleAssignmentQuadrantGrouping.fkUserId, systemModuleUser.id))
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleAssignmentQuadrantGrouping.id));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, alias, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            alias,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkQuadrantGroupingId: rest.fkQuadrantGroupingId,
            fkFatigueId: rest.fkFatigueId,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as QuadrantGroupingAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.QUADRANT_GROUPING} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
