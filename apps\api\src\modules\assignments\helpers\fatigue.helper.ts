import { z } from 'zod';
import type { FatigueByFatigueData, FatigueByUserData } from '../interfaces/fatigue.initerface';

export const fatigueUnitObjectBodySchema = z.object({
  fkUnitControlId: z.string(),
  isDriver: z.boolean().default(false),
  isPassenger: z.boolean().default(false),
  isInCharge: z.boolean().default(false),
});

export const fatigueBodySchema = z.object({
  fkUserId: z.string(),
  fkFatigueId: z.string(),
  fkArrayCategoryIds: z.array(z.string()),
  fkArrayPlaceIds: z.array(z.string()),
  fkArrayQuadrantsIds: z.array(z.string()).default([]),
  fkUnitObject: fatigueUnitObjectBodySchema.nullable().optional(),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});

export const assignFatigueBodySchema = z.object({
  fkUserId: z.string(),
  fkFatigueId: z.string(),
  categories: z.array(z.string()),
  places: z.array(z.string()),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});

type FatigueAssignGroupedByFatigue = Record<
  string,
  {
    fkFatigueId: string;
    data: FatigueByFatigueData[];
  }
>;

type FatigueAssignGroupedByUser = Record<
  string,
  {
    fkUserId: string;
    aliasUser: string | null;
    data: FatigueByUserData[];
  }
>;

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
export const groupByFatigue = (result: any[]): FatigueAssignGroupedByFatigue => {
  return result.reduce((acc, row) => {
    const { fkFatigueId, ...rest } = row;
    if (!acc[fkFatigueId]) {
      acc[fkFatigueId] = {
        fkFatigueId,
        data: [],
      };
    }
    if (rest) {
      acc[fkFatigueId].data.push({
        id: rest.id,
        fkUserId: rest.fkUserId,
        aliasUser: rest.aliasUser,
        observations: rest.observations,
        assignments: {
          places: rest.assignments?.places || [],
          unit: rest.assignments?.unit || null,
          quadrants: rest.assignments?.quadrants || [],
        },
      });
    }
    return acc;
  }, {} as FatigueAssignGroupedByFatigue);
};

// biome-ignore lint/suspicious/noExplicitAny: <explanation>
export const groupByUser = (result: any[]): FatigueAssignGroupedByUser => {
  return result.reduce((acc, row) => {
    const { fkUserId, aliasUser, ...rest } = row;
    if (!acc[fkUserId]) {
      acc[fkUserId] = {
        fkUserId,
        aliasUser,
        data: [],
      };
    }
    if (rest) {
      acc[fkUserId].data.push({
        id: rest.id,
        fkFatigueId: rest.fkFatigueId,
        observations: rest.observations,
        assignments: {
          places: rest.assignments?.places || [],
          unit: rest.assignments?.unit || null,
          quadrants: rest.assignments?.quadrants || [],
        },
      });
    }
    return acc;
  }, {} as FatigueAssignGroupedByUser);
};
