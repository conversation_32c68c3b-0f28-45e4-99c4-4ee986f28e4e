import { Router } from 'express';
import {
  getByState,
  getOne,
  create,
  update,
  deleteOne,
  searchByStatePaginated,
} from '@/modules/catalogs/controllers/municipality.controller';

const catMunicipalityRouter = Router();

catMunicipalityRouter.get('/:state/search', searchByStatePaginated);
catMunicipalityRouter.get('/:state', getByState);
catMunicipalityRouter.get('/:id', getOne);
catMunicipalityRouter.post('/', create);
catMunicipalityRouter.put('/:id', update);
catMunicipalityRouter.delete('/:id', deleteOne);

export default catMunicipalityRouter;
