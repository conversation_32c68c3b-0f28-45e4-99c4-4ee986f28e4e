import { displayStyleValues } from '@repo/types/values';
import { z } from 'zod';

export const operationalQuadrantBodySchema = z.object({
  quadrantName: z.string(),
  isOperationalStatus: z.boolean(),
  operationalArea: z.number(),
  geometry: z.array(z.any()).optional().default([]),
  displayColor: z.string(),
  displayStyle: z.enum(displayStyleValues).optional().default('solid'),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});
