import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/shift.controller';

const shiftRouter = Router();

shiftRouter.get('/', getAll);
shiftRouter.get('/search', searchPaginated);
shiftRouter.get('/:id', getOne);
shiftRouter.post('/', create);
shiftRouter.put('/:id', update);
shiftRouter.delete('/:id', deleteOne);

export default shiftRouter;
