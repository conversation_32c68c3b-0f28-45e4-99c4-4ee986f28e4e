import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/institution.controller';

const catInstitutionRouter = Router();

catInstitutionRouter.get('/', getAll);
catInstitutionRouter.get('/search', searchPaginated);
catInstitutionRouter.get('/:id', getOne);
catInstitutionRouter.post('/', create);
catInstitutionRouter.put('/:id', update);
catInstitutionRouter.delete('/:id', deleteOne);

export default catInstitutionRouter;
