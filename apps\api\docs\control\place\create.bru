meta {
  name: create
  type: http
  seq: 5
}

post {
  url: {{base}}/control/place?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

headers {
  authorization: 
}

body:json {
  {
    "name": "Nombre de Prueba",
    "fkStateId": "uuid Estado",
    "fkMunicipalityId": "uuid Municipio",
    "fkColonyId": "uuid Colonia",
    "zipCode": 49000,
    "street": "Calle de Prueba",
    "number": "1",
    "interiorNumber": "#",
    "betweenStreet1": "Entre Calle Prueba 1 y Calle Prueba 2",
    "betweenStreet2": "Entre Calle Prueba 3 y Prueba 4",
    "reference": "Cerca de la Calle Prueba 5",
    "location": {
      "lng":0, 
      "lat":1
    },
    "sectors": []
  }
}
