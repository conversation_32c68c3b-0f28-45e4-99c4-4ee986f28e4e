meta {
  name: create
  type: http
  seq: 5
}

post {
  url: {{base}}/control/unit?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

body:json {
  {
    "code": "Código de Unidad", // Código o Alias
    "cost": 0, // Costo de la Unidad
    "fkVehicleStatusId": "ID Estado Vehículo", // Opcional
    "mileage": 0, // Kilometraje del vehículo
    "terrain": "Tipo de vehículo", // ['air', 'water', 'land']
    "isNational": true, // Es nacional el vehículo?
    "fkVehicleTypeId": "ID Tipo Vehículo",
    "fkVehicleBrandId": "ID Marca del vehículo",
    "fkVehicleModelId": "ID Modelo del vehículo",
    "year": 2025, // Año de fabricación del vehículo | Opcional
    "fkVehicleColorId": "ID Color Vehículo",
    "useType": "Tipo de uso del vehículo", // ['private', 'public', 'cargo']
    "licensePlate": "Número de Placas del Vehículo",
    "capacity": 5, // Capacidad de personas
    "niv": "Número de REPUVE",
    "serialNumber": "Número de Serie",
    "situation": "Tipo de situación del vehículo" // ['with_report', 'without_report', 'unknown']
  }
}
