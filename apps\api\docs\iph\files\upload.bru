meta {
  name: upload
  type: http
  seq: 1
}

post {
  url: {{base}}/iph/files?name=file_namsae.jpeg&iphId=01961281-4c3c-29b3-af09-a83cbd71359b&section=sec_1&type=sign | rights | pdf | null | undefined&number=0 | 1 | null | undefined
  body: file
  auth: inherit
}

params:query {
  name: file_namsae.jpeg
  iphId: 01961281-4c3c-29b3-af09-a83cbd71359b
  section: sec_1
  type: sign | rights | pdf | null | undefined
  number: 0 | 1 | null | undefined
  ~annexId: uuid | null | undefined
}

headers {
  Content-Length: 
}

body:file {
  file: @file(/Users/<USER>/Downloads/picture.png) @contentType(image/png)
}
