import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/vehicle_type.controller';

const catVehicleTypeRouter = Router();

catVehicleTypeRouter.get('/', getAll);
catVehicleTypeRouter.get('/search', searchPaginated);
catVehicleTypeRouter.get('/:id', getOne);
catVehicleTypeRouter.post('/', create);
catVehicleTypeRouter.put('/:id', update);
catVehicleTypeRouter.delete('/:id', deleteOne);

export default catVehicleTypeRouter;
