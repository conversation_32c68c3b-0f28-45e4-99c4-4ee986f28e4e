import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/size.controller';

const catSizeRouter = Router();

catSizeRouter.get('/', getAll);
catSizeRouter.get('/search', searchPaginated);
catSizeRouter.get('/:id', getOne);
catSizeRouter.post('/', create);
catSizeRouter.put('/:id', update);
catSizeRouter.delete('/:id', deleteOne);

export default catSizeRouter;
