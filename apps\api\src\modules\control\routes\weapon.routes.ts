import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/weapon.controller';

const weaponRouter = Router();

weaponRouter.get('/', getAll);
weaponRouter.get('/search', searchPaginated);
weaponRouter.get('/:id', getOne);
weaponRouter.post('/', create);
weaponRouter.put('/:id', update);
weaponRouter.delete('/:id', deleteOne);

export default weaponRouter;
