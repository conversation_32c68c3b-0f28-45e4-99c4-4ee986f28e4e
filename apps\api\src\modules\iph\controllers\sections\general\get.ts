import type { Request, Response } from 'express';
import db from '@/db';
import { systemModuleControlIphGeneral } from '@repo/shared-drizzle/schemas';
import { and, eq } from 'drizzle-orm';
import { HttpStatus } from '@/utils/http_status';
import { Logger } from '@/utils/logger';
import { ModuleName } from '@/utils/module_names';

const query = ({ id }: { id: string }) => {
  return db
    .select()
    .from(systemModuleControlIphGeneral)
    .where(and(eq(systemModuleControlIphGeneral.id, id), eq(systemModuleControlIphGeneral.isDeleted, false)))
    .limit(1);
};

export const get = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const queryResult = await query({ id });
    if (queryResult.length === 0) {
      res.status(HttpStatus.NOT_FOUND).json({ message: 'Not found' });
      return;
    }
    res.status(HttpStatus.OK).json(queryResult[0]);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      module: ModuleName.IPH,
      method: '/iph/section/general/get/:id - get',
      source: req.headers['user-agent'],
      stack,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};
