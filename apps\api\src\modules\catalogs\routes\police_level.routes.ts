import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/police_level.controller';

const catPoliceLevelRouter = Router();

catPoliceLevelRouter.get('/', getAll);
catPoliceLevelRouter.get('/search', searchPaginated);
catPoliceLevelRouter.get('/:id', getOne);
catPoliceLevelRouter.post('/', create);
catPoliceLevelRouter.put('/:id', update);
catPoliceLevelRouter.delete('/:id', deleteOne);

export default catPoliceLevelRouter;
