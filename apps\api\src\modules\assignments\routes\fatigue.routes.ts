import { Router } from 'express';
import { create, /*deleteOne,*/ getAll, getOne, /*searchPaginated,*/ update } from '../controllers/fatigue.controller';

const fatigueAssignRouter = Router();

fatigueAssignRouter.get('/', getAll);
//fatigueAssignRouter.get('/search', searchPaginated);
fatigueAssignRouter.get('/:id', getOne);
fatigueAssignRouter.post('/', create);
fatigueAssignRouter.put('/:id', update);
//fatigueAssignRouter.delete('/:id', deleteOne);

export default fatigueAssignRouter;
