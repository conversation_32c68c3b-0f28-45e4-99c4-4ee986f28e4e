import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, or, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';
import { systemModuleControlCommission } from '@repo/shared-drizzle/schemas';
import { commissionBodySchema } from '../helpers/commission.helper';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS } = req.query;
  let deleted = false;
  let disabled = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlCommission.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlCommission.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlCommission.id,
        name: systemModuleControlCommission.name,
        description: systemModuleControlCommission.description,
        icon: systemModuleControlCommission.icon,
      })
      .from(systemModuleControlCommission)
      .where(and(...filters));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.COMMISSION}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleControlCommission.id,
        name: systemModuleControlCommission.name,
        description: systemModuleControlCommission.description,
        icon: systemModuleControlCommission.icon,
      })
      .from(systemModuleControlCommission)
      .where(eq(systemModuleControlCommission.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.COMMISSION}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  const { name, description, icon } = req.body;
  try {
    const insertData = await commissionBodySchema.parseAsync({
      name,
      description,
      icon,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });

    // Obtener el módulo de comisiones
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_COMISIONES);
    if (!systemModule) {
      throw new Error('System module CTRL_COMISIONES not found');
    }

    // Insertar la nueva comision
    const result = await db.insert(systemModuleControlCommission).values({
      ...insertData,
      fkSystemModuleId: systemModule.id,
    });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.COMMISSION} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { name, description, icon } = req.body;
  try {
    const updateData = await commissionBodySchema.parseAsync({
      name,
      description,
      icon,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });

    const result = await db.transaction(async (trx) => {
      // Verificar que la comision exista
      const existingCommission = await trx
        .select()
        .from(systemModuleControlCommission)
        .where(eq(systemModuleControlCommission.id, id));
      if (existingCommission.length === 0) {
        throw new Error('Comisión no encontrada');
      }

      // Actualizar la comision
      const updateResult = await trx
        .update(systemModuleControlCommission)
        .set({ ...updateData })
        .where(eq(systemModuleControlCommission.id, id));

      return updateResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.COMMISSION}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Verificar que la comision exista
      const existingCommission = await trx
        .select()
        .from(systemModuleControlCommission)
        .where(eq(systemModuleControlCommission.id, id));
      if (existingCommission.length === 0) {
        throw new Error('Comisión no encontrada');
      }
      const deleteResult = await trx
        .update(systemModuleControlCommission)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleControlCommission.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.COMMISSION}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, deleted: deletedQS, disabled: disabledQS, q: qQS } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlCommission.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlCommission.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleControlCommission.name, `%${q}%`));
      likeFilters.push(like(systemModuleControlCommission.description, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleControlCommission.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlCommission.id,
        name: systemModuleControlCommission.name,
        description: systemModuleControlCommission.description,
        icon: systemModuleControlCommission.icon,
      })
      .from(systemModuleControlCommission)
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlCommission.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.COMMISSION} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
