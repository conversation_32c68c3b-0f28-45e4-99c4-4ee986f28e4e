import { Router } from 'express';
import {
  create,
  deleteOne,
  getAll,
  getOne,
  searchPaginated,
  update,
} from '../controllers/quadrant_grouping.controller';

const quadrantGroupingRouter = Router();

quadrantGroupingRouter.get('/', getAll);
quadrantGroupingRouter.get('/search', searchPaginated);
quadrantGroupingRouter.get('/:id', getOne);
quadrantGroupingRouter.post('/', create);
quadrantGroupingRouter.put('/:id', update);
quadrantGroupingRouter.delete('/:id', deleteOne);

export default quadrantGroupingRouter;
