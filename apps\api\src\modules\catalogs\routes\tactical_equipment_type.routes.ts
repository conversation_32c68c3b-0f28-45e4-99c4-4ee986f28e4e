import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/tactical_equipment_type.controller';

const catTacticalEquipmentTypeRouter = Router();

catTacticalEquipmentTypeRouter.get('/', getAll);
catTacticalEquipmentTypeRouter.get('/search', searchPaginated);
catTacticalEquipmentTypeRouter.get('/:id', getOne);
catTacticalEquipmentTypeRouter.post('/', create);
catTacticalEquipmentTypeRouter.put('/:id', update);
catTacticalEquipmentTypeRouter.delete('/:id', deleteOne);

export default catTacticalEquipmentTypeRouter;
