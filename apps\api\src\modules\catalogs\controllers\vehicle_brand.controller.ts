import type { Request, Response } from 'express';
import { catVehicleBrand } from '@repo/shared-drizzle/schemas';
import db from '@/db';
import { and, asc, eq, gt, like, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { getLocale } from '@/utils/locale_validator';
import { HttpStatus } from '@/utils/http_status';
import { z } from 'zod';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS } = req.query;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(catVehicleBrand.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(catVehicleBrand.isEnabled, true));
    }
    const result = await db
      .select({
        id: catVehicleBrand.id,
        key: catVehicleBrand.key,
        name: catVehicleBrand[locale],
        description: catVehicleBrand.description,
      })
      .from(catVehicleBrand);
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'vehicle_brand/all - getAll',
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const result = await db
      .select({
        id: catVehicleBrand.id,
        key: catVehicleBrand.key,
        name: catVehicleBrand[locale],
        description: catVehicleBrand.description,
      })
      .from(catVehicleBrand)
      .where(eq(catVehicleBrand.id, id));
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `vehicle_brand/${id} - getOne`,
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const create = async (req: Request, res: Response) => {
  const bodySchema = z.object({
    key: z.string(),
    name: z.string(),
    description: z.string().optional(),
    fkLastUserId: z.string(),
    fkLastDependencyId: z.string(),
  });

  const { locale: localeQS } = req.query;

  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const { name, ...insertData } = await bodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const result = await db.insert(catVehicleBrand).values({ [locale]: name, ...insertData });
    res.status(HttpStatus.CREATED).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'vehicle_brand - create',
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const update = async (req: Request, res: Response) => {
  const bodySchema = z.object({
    key: z.string(),
    name: z.string(),
    description: z.string().optional(),
    fkLastUserId: z.string(),
    fkLastDependencyId: z.string(),
  });

  const { locale: localeQS } = req.query;
  const { id } = req.params;

  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const { name, ...updateData } = await bodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const result = await db
      .update(catVehicleBrand)
      .set({ ...updateData, [locale]: name })
      .where(eq(catVehicleBrand.id, id));
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `vehicle_brand/${id} - update`,
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .update(catVehicleBrand)
      .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
      .where(eq(catVehicleBrand.id, id));

    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `vehicle_brand/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    locale: localeQS,
    q: qQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(catVehicleBrand.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(catVehicleBrand.isEnabled, true));
    }
    if (q) {
      filters.push(like(sql`normalize_text(${catVehicleBrand[locale]})`, sql`'%' || normalize_text(${q}) || '%'`));
    }

    if (lastId) {
      filters.push(gt(catVehicleBrand.id, lastId));
    }
    const result = await db
      .select({
        id: catVehicleBrand.id,
        key: catVehicleBrand.key,
        name: catVehicleBrand[locale],
        description: catVehicleBrand.description,
      })
      .from(catVehicleBrand)
      .where(and(...filters))
      .limit(limit)
      .orderBy(asc(catVehicleBrand.id));

    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'vehicle_brand - searchPaginated',
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};
