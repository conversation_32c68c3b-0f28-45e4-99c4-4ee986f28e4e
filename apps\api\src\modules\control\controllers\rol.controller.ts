import type { Request, Response } from 'express';
import db from '@/db';
import { z } from 'zod';
import { and, asc, eq, gt, inArray, like, or, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { systemModuleRol, systemModulePivotUserRole, systemModuleUser } from '@repo/shared-drizzle/schemas';
import { ModuleName } from '@/utils/module_names';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import type { IPivotUserRole } from '../interfaces/rol.interface';
import { roleBodySchema } from '../helpers/rol.helper';
import SystemModuleCache from '@/utils/system_module_cache';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS } = req.query;
  let deleted = false;
  let disabled = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleRol.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleRol.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleRol.id,
        name: systemModuleRol.name,
        description: systemModuleRol.description,
      })
      .from(systemModuleRol)
      .where(and(...filters));
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.ROL}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleRol.id,
        name: systemModuleRol.name,
        description: systemModuleRol.description,
      })
      .from(systemModuleRol)
      .where(eq(systemModuleRol.id, id))
      .limit(1);
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.ROL}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const insertData = await roleBodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_ROLES);
    if (!systemModule) {
      throw new Error('System module CTRL_ROLES not found');
    }
    const result = await db.insert(systemModuleRol).values({
      ...insertData,
      fkSystemModuleId: systemModule.id,
    });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.ROL} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const updateData = await roleBodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const result = await db
      .update(systemModuleRol)
      .set({ ...updateData })
      .where(eq(systemModuleRol.id, id));
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.ROL}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .update(systemModuleRol)
      .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
      .where(eq(systemModuleRol.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.ROL}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, deleted: deletedQS, disabled: disabledQS, q: qQS } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleRol.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleRol.isEnabled, true));
    }
    if (q) {
      filters.push(like(systemModuleRol.name, `%${q}%`));
      filters.push(like(systemModuleRol.description, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleRol.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleRol.id,
        name: systemModuleRol.name,
        description: systemModuleRol.description,
      })
      .from(systemModuleRol)
      .where(and(...filters))
      .limit(limit)
      .orderBy(asc(systemModuleRol.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.ROL} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const assignUserToRole = async (req: Request, res: Response) => {
  const { fkUserId, arrFkRoleId }: { fkUserId: string; arrFkRoleId: string[] } = req.body;
  try {
    // Obtener los roles almacenados en el array
    const sqRol = db.$with('sq').as(db.select().from(systemModuleRol).where(inArray(systemModuleRol.id, arrFkRoleId)));
    const rol = await db.with(sqRol).select().from(sqRol);
    if (rol.length === 0) {
      throw new Error('Roles not found');
    }
    // Verificar que todos los roles almacenados en el array existan en el sistema
    if (rol.length !== arrFkRoleId.length) {
      // Obtener los roles que no existen en el sistema
      const missing = arrFkRoleId.filter((item) => !rol.some((rol) => rol.id === item));
      res.status(HttpStatus.NOT_FOUND).json({ message: 'Roles not found', result: missing });
      return;
    }
    // Obtener el usuario
    const sqUser = db.$with('sq').as(db.select().from(systemModuleUser).where(eq(systemModuleUser.id, fkUserId)));
    const user = await db.with(sqUser).select().from(sqUser);
    if (user.length === 0) {
      throw new Error('User not found');
    }
    const filters: SQL[] = [];
    const insertValues: IPivotUserRole[] = [];
    for (const r of arrFkRoleId) {
      // Almacenar en una lista la relación usuario-rol para posteriormente verificar si ya está asignado
      const filter = and(eq(systemModulePivotUserRole.fkUserId, fkUserId), eq(systemModulePivotUserRole.fkRolId, r));
      if (filter !== undefined) {
        filters.push(filter);
      }
      if (req.userId !== undefined && req.dependencyId !== undefined) {
        // Listar los valores que se van a insertar en la tabla
        insertValues.push({ fkUserId, fkRolId: r, fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId });
      }
    }
    // Verificar si ya está asignado
    const sqAssignment = db.$with('sq').as(
      db
        .select()
        .from(systemModulePivotUserRole)
        .where(or(...filters)),
    );
    const assignmentExist = await db.with(sqAssignment).select().from(sqAssignment);
    if (assignmentExist.length > 0) {
      // Obtener los roles que ya están asignados
      const assigned = arrFkRoleId.filter((item) =>
        assignmentExist.some((assign) => assign.fkRolId === item && assign.fkUserId === fkUserId),
      );
      res.status(HttpStatus.NOT_FOUND).json({ message: 'Roles already assigned', result: assigned });
      return;
    }
    // Insertar los valores en la tabla
    const result = await db.insert(systemModulePivotUserRole).values(insertValues);
    res.status(HttpStatus.OK).json({ message: 'Role assigned', result });
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack: stack,
      method: `${ModuleName.ROL} - assignUserToRole`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
