// TODO: delete this file
import type { Request, Response } from 'express';
import { HttpStatus } from '@/utils/http_status';
import { Logger } from '@/utils/logger';
import db from '@/db';
import { and, eq } from 'drizzle-orm';
import { systemModuleControlPlaces } from '@repo/shared-drizzle/schemas';

export const getAll = async (req: Request, res: Response) => {
  try {
    const result = await db
      .select({
        id: systemModuleControlPlaces.id,
        name: systemModuleControlPlaces.name,
        type: systemModuleControlPlaces.type,
        state: {
          id: systemModuleControlPlaces.fkStateId,
          // name: systemModuleControlPlaces.stateName,
        },
        municipality: {
          id: systemModuleControlPlaces.fkMunicipalityId,
          // name: systemModuleControlPlaces.municipalityName,
        },
        colony: {
          id: systemModuleControlPlaces.fkColonyId,
          // name: systemModuleControlPlaces.colonyName,
        },
        zipCode: systemModuleControlPlaces.zipCode,
        street: systemModuleControlPlaces.street,
        number: systemModuleControlPlaces.number,
        interiorNumber: systemModuleControlPlaces.interiorNumber,
        betweenStreet1: systemModuleControlPlaces.betweenStreet1,
        betweenStreet2: systemModuleControlPlaces.betweenStreet2,
        reference: systemModuleControlPlaces.reference,
        location: systemModuleControlPlaces.location,
        sectors: systemModuleControlPlaces.sectors,
      })
      .from(systemModuleControlPlaces)
      .leftJoin(systemModuleControlPlaces, eq(systemModuleControlPlaces.fkStateId, systemModuleControlPlaces.id))
      .leftJoin(systemModuleControlPlaces, eq(systemModuleControlPlaces.fkMunicipalityId, systemModuleControlPlaces.id))
      .leftJoin(systemModuleControlPlaces, eq(systemModuleControlPlaces.fkColonyId, systemModuleControlPlaces.id))
      .where(and(eq(systemModuleControlPlaces.isDeleted, false), eq(systemModuleControlPlaces.isEnabled, true)));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'iph/places/all - getAll',
      source: req.headers['user-agent'],
      module: 'iph/places',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
