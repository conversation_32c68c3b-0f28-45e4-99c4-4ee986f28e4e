import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, isNull, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { getLocale } from '@/utils/locale_validator';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';
import {
  catColor,
  catVehicleBrand,
  catVehicleModel,
  catVehicleType,
  systemModuleAssignmentUnit,
  systemModuleControlVehicle,
  systemModuleControlVehicleUnit,
} from '@repo/shared-drizzle/schemas';
import { vehicleBodySchema } from '../helpers/vehicle.helper';
import { UnitBodySchema } from '../helpers/unit.helper';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS } = req.query;
  let [deleted, disabled, locale] = [false, false, getLocale()];

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlVehicleUnit.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlVehicleUnit.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlVehicleUnit.id,
        unitStatus: systemModuleControlVehicleUnit.unitStatus,
        code: systemModuleControlVehicleUnit.code,
        mileage: systemModuleControlVehicle.mileage,
        terrain: systemModuleControlVehicle.terrain,
        isNational: systemModuleControlVehicle.isNational,
        fkVehicleTypeId: systemModuleControlVehicle.fkVehicleTypeId,
        nameVehicleType: catVehicleType[locale],
        fkVehicleBrandId: systemModuleControlVehicle.fkVehicleBrandId,
        nameVehicleBrand: catVehicleBrand[locale],
        fkVehicleModelId: systemModuleControlVehicle.fkVehicleModelId,
        nameVehicleModel: catVehicleModel[locale],
        year: systemModuleControlVehicle.year,
        fkVehicleColorId: systemModuleControlVehicle.fkVehicleColorId,
        nameVehicleColor: catColor[locale],
        useType: systemModuleControlVehicle.useType,
        licensePlate: systemModuleControlVehicle.licensePlate,
        capacity: systemModuleControlVehicle.capacity,
        niv: systemModuleControlVehicle.niv,
        serialNumber: systemModuleControlVehicle.serialNumber,
        situation: systemModuleControlVehicle.situation,
        cost: systemModuleControlVehicleUnit.cost,
      })
      .from(systemModuleControlVehicleUnit)
      .leftJoin(
        systemModuleControlVehicle,
        eq(systemModuleControlVehicleUnit.fkVehicleId, systemModuleControlVehicle.id),
      )
      .leftJoin(catVehicleType, eq(systemModuleControlVehicle.fkVehicleTypeId, catVehicleType.id))
      .leftJoin(catVehicleBrand, eq(systemModuleControlVehicle.fkVehicleBrandId, catVehicleBrand.id))
      .leftJoin(catVehicleModel, eq(systemModuleControlVehicle.fkVehicleModelId, catVehicleModel.id))
      .leftJoin(catColor, eq(systemModuleControlVehicle.fkVehicleColorId, catColor.id))
      .where(and(...filters));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIT}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const result = await db
      .select({
        id: systemModuleControlVehicleUnit.id,
        unitStatus: systemModuleControlVehicleUnit.unitStatus,
        code: systemModuleControlVehicleUnit.code,
        mileage: systemModuleControlVehicle.mileage,
        terrain: systemModuleControlVehicle.terrain,
        isNational: systemModuleControlVehicle.isNational,
        fkVehicleTypeId: systemModuleControlVehicle.fkVehicleTypeId,
        nameVehicleType: catVehicleType[locale],
        fkVehicleBrandId: systemModuleControlVehicle.fkVehicleBrandId,
        nameVehicleBrand: catVehicleBrand[locale],
        fkVehicleModelId: systemModuleControlVehicle.fkVehicleModelId,
        nameVehicleModel: catVehicleModel[locale],
        year: systemModuleControlVehicle.year,
        fkVehicleColorId: systemModuleControlVehicle.fkVehicleColorId,
        nameVehicleColor: catColor[locale],
        useType: systemModuleControlVehicle.useType,
        licensePlate: systemModuleControlVehicle.licensePlate,
        capacity: systemModuleControlVehicle.capacity,
        niv: systemModuleControlVehicle.niv,
        serialNumber: systemModuleControlVehicle.serialNumber,
        situation: systemModuleControlVehicle.situation,
        cost: systemModuleControlVehicleUnit.cost,
      })
      .from(systemModuleControlVehicleUnit)
      .leftJoin(
        systemModuleControlVehicle,
        eq(systemModuleControlVehicleUnit.fkVehicleId, systemModuleControlVehicle.id),
      )
      .leftJoin(catVehicleType, eq(systemModuleControlVehicle.fkVehicleTypeId, catVehicleType.id))
      .leftJoin(catVehicleBrand, eq(systemModuleControlVehicle.fkVehicleBrandId, catVehicleBrand.id))
      .leftJoin(catVehicleModel, eq(systemModuleControlVehicle.fkVehicleModelId, catVehicleModel.id))
      .leftJoin(catColor, eq(systemModuleControlVehicle.fkVehicleColorId, catColor.id))
      .where(eq(systemModuleControlVehicleUnit.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIT}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const { code, cost, ...rest } = req.body;
    const insertVehicleData = await vehicleBodySchema.parseAsync({
      ...rest,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModuleVehicle = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_VEHICULOS);
    const systemModuleUnit = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_UNIDADES);
    if (!systemModuleVehicle) {
      throw new Error('System module CTRL_VEHICULOS not found');
    }
    if (!systemModuleUnit) {
      throw new Error('System module CTRL_UNIDADES not found');
    }
    const result = await db.transaction(async (trx) => {
      const vehicleResult = await trx
        .insert(systemModuleControlVehicle)
        .values({
          ...insertVehicleData,
          fkSystemModuleId: systemModuleVehicle.id,
        })
        .returning({ vehicleId: systemModuleControlVehicle.id });
      if (vehicleResult.length === 0) {
        trx.rollback();
      }
      const insertUnitData = await UnitBodySchema.parseAsync({
        fkVehicleId: vehicleResult[0].vehicleId,
        code,
        unitStatus: 'new',
        cost,
        fkLastUserId: req.userId,
        fkLastDependencyId: req.dependencyId,
      });
      const vehicleUnitResult = await trx.insert(systemModuleControlVehicleUnit).values({
        ...insertUnitData,
        fkSystemModuleId: systemModuleUnit.id,
      });
      if (vehicleUnitResult.rowCount === 0) {
        trx.rollback();
      }
      return vehicleUnitResult.rows[0];
    });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIT} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const { code, unitStatus, cost, ...vehicleData } = req.body;
    vehicleData.fkLastUserId = req.userId;
    vehicleData.fkLastDependencyId = req.dependencyId;
    // Validar payload

    const result = await db.transaction(async (trx) => {
      // Obtener datos del la unidad
      const vehicle = await trx
        .select()
        .from(systemModuleControlVehicleUnit)
        .where(eq(systemModuleControlVehicleUnit.id, id))
        .limit(1);
      if (vehicle.length === 0) {
        throw new Error('Unidad no encontrada');
      }

      const updatetUnitData = await UnitBodySchema.parseAsync({
        code,
        unitStatus,
        cost,
        fkLastUserId: vehicleData.fkLastUserId,
        fkLastDependencyId: vehicleData.fkLastDependencyId,
      });

      // Actualizar el unidad
      const updateResult = await trx
        .update(systemModuleControlVehicleUnit)
        .set({ ...updatetUnitData })
        .where(eq(systemModuleControlVehicleUnit.id, id));

      const updateVehicleData = await vehicleBodySchema.parseAsync({ ...vehicleData });

      // Actualizar vehículo
      await trx
        .update(systemModuleControlVehicle)
        .set({ ...updateVehicleData })
        .where(eq(systemModuleControlVehicle.id, vehicle[0].fkVehicleId));

      return updateResult;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIT}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Verificar si la unidad está asignado actualmente a un usuario
      const activeUnitAssignment = await trx
        .select()
        .from(systemModuleAssignmentUnit)
        .where(
          and(eq(systemModuleAssignmentUnit.fkUnitControlId, id), isNull(systemModuleAssignmentUnit.assignmentEndDate)),
        )
        .limit(1);

      if (activeUnitAssignment.length > 0) {
        throw new Error('No se puede eliminar la unidad porque está asignado actualmente a un elemento');
      }

      // Marcar la unidad como eliminado
      const deleteResult = await trx
        .update(systemModuleControlVehicleUnit)
        .set({
          isDeleted: true,
          deletedAt: new Date(),
          fkLastUserId: req.userId,
          fkLastDependencyId: req.dependencyId,
        })
        .where(eq(systemModuleControlVehicleUnit.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIT}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    locale: localeQS,
    q: qQS,
    includeVehicle: includeVehicleQS,
    vehicleFields: vehicleFieldsQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let q = null;
  let includeVehicle = false;
  let vehicleFields: string[] = [];

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  if (includeVehicleQS && typeof includeVehicleQS === 'string') {
    includeVehicle = includeVehicleQS === 'true';
  }
  if (vehicleFieldsQS && typeof vehicleFieldsQS === 'string') {
    vehicleFields = vehicleFieldsQS.split(',');
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlVehicleUnit.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlVehicleUnit.isEnabled, true));
    }
    // TODO: Cambiar este filtro
    if (q) {
      likeFilters.push(like(sql`${systemModuleControlVehicle.mileage}::text`, `%${q}%`));
      likeFilters.push(like(catVehicleType[locale], `%${q}%`));
      likeFilters.push(like(catVehicleBrand[locale], `%${q}%`));
      likeFilters.push(like(catVehicleModel[locale], `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlVehicle.year}::text`, `%${q}%`));
      likeFilters.push(like(catColor[locale], `%${q}%`));
      likeFilters.push(like(systemModuleControlVehicle.licensePlate, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlVehicle.capacity}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleControlVehicle.niv, `%${q}%`));
      likeFilters.push(like(systemModuleControlVehicle.serialNumber, `%${q}%`));
      likeFilters.push(like(systemModuleControlVehicleUnit.code, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlVehicleUnit.cost}::text`, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleControlVehicleUnit.id, lastId));
    }

    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    const selectConfig: Record<string, any> = {
      id: systemModuleControlVehicleUnit.id,
      unitStatus: systemModuleControlVehicleUnit.unitStatus,
      code: systemModuleControlVehicleUnit.code,
    };

    if (includeVehicle) {
      // biome-ignore lint/suspicious/noExplicitAny: <explanation>
      const vehicleSelect: Record<string, any> = {};

      if (vehicleFields.length > 0) {
        // biome-ignore lint/suspicious/noExplicitAny: <explanation>
        const availableVehicleFields: Record<string, any> = {
          mileage: systemModuleControlVehicle.mileage,
          terrain: systemModuleControlVehicle.terrain,
          isNational: systemModuleControlVehicle.isNational,
          fkVehicleTypeId: systemModuleControlVehicle.fkVehicleTypeId,
          nameVehicleType: catVehicleType[locale],
          fkVehicleBrandId: systemModuleControlVehicle.fkVehicleBrandId,
          nameVehicleBrand: catVehicleBrand[locale],
          fkVehicleModelId: systemModuleControlVehicle.fkVehicleModelId,
          nameVehicleModel: catVehicleModel[locale],
          year: systemModuleControlVehicle.year,
          fkVehicleColorId: systemModuleControlVehicle.fkVehicleColorId,
          nameVehicleColor: catColor[locale],
          useType: systemModuleControlVehicle.useType,
          licensePlate: systemModuleControlVehicle.licensePlate,
          capacity: systemModuleControlVehicle.capacity,
          niv: systemModuleControlVehicle.niv,
          serialNumber: systemModuleControlVehicle.serialNumber,
          situation: systemModuleControlVehicle.situation,
          cost: systemModuleControlVehicleUnit.cost,
        };

        for (const field of vehicleFields) {
          if (availableVehicleFields[field]) {
            vehicleSelect[field] = availableVehicleFields[field];
          }
        }
      } else {
        // Si no se especificaron campos, incluimos todos
        vehicleSelect.mileage = systemModuleControlVehicle.mileage;
        vehicleSelect.terrain = systemModuleControlVehicle.terrain;
        vehicleSelect.isNational = systemModuleControlVehicle.isNational;
        vehicleSelect.fkVehicleTypeId = systemModuleControlVehicle.fkVehicleTypeId;
        vehicleSelect.nameVehicleType = catVehicleType[locale];
        vehicleSelect.fkVehicleBrandId = systemModuleControlVehicle.fkVehicleBrandId;
        vehicleSelect.nameVehicleBrand = catVehicleBrand[locale];
        vehicleSelect.fkVehicleModelId = systemModuleControlVehicle.fkVehicleModelId;
        vehicleSelect.nameVehicleModel = catVehicleModel[locale];
        vehicleSelect.year = systemModuleControlVehicle.year;
        vehicleSelect.fkVehicleColorId = systemModuleControlVehicle.fkVehicleColorId;
        vehicleSelect.nameVehicleColor = catColor[locale];
        vehicleSelect.useType = systemModuleControlVehicle.useType;
        vehicleSelect.licensePlate = systemModuleControlVehicle.licensePlate;
        vehicleSelect.capacity = systemModuleControlVehicle.capacity;
        vehicleSelect.niv = systemModuleControlVehicle.niv;
        vehicleSelect.serialNumber = systemModuleControlVehicle.serialNumber;
        vehicleSelect.situation = systemModuleControlVehicle.situation;
        vehicleSelect.cost = systemModuleControlVehicleUnit.cost;
      }
      selectConfig.vehicle = vehicleSelect;
    }

    const query = db.select(selectConfig).from(systemModuleControlVehicleUnit);

    if (includeVehicle) {
      query
        .leftJoin(
          systemModuleControlVehicle,
          eq(systemModuleControlVehicleUnit.fkVehicleId, systemModuleControlVehicle.id),
        )
        .leftJoin(catVehicleType, eq(systemModuleControlVehicle.fkVehicleTypeId, catVehicleType.id))
        .leftJoin(catVehicleBrand, eq(systemModuleControlVehicle.fkVehicleBrandId, catVehicleBrand.id))
        .leftJoin(catVehicleModel, eq(systemModuleControlVehicle.fkVehicleModelId, catVehicleModel.id))
        .leftJoin(catColor, eq(systemModuleControlVehicle.fkVehicleColorId, catColor.id));
    }

    const result = await query
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlVehicleUnit.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIT} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
