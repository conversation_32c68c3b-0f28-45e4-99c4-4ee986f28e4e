import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/weapon_type.controller';

const catWeaponTypeRouter = Router();

catWeaponTypeRouter.get('/', getAll);
catWeaponTypeRouter.get('/search', searchPaginated);
catWeaponTypeRouter.get('/:id', getOne);
catWeaponTypeRouter.post('/', create);
catWeaponTypeRouter.put('/:id', update);
catWeaponTypeRouter.delete('/:id', deleteOne);

export default catWeaponTypeRouter;
