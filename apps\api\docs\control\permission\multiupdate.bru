meta {
  name: multiupdate
  type: http
  seq: 5
}

post {
  url: {{base}}/control/permission/multiupdate?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

body:json {
  [
    {
      "id": "ID Permiso 1",
      "name": "Nombre de prueba 1",
      "description": "Descripción de prueba 1", // Opcional
      "canRead": true,
      "canCreate": true,
      "canUpdate": true,
      "canDelete": true,
      "fkProfileId": "ID Perfil 1",
      "fkModulePermissionId": "ID Módulo (Donde se aplicaran los permisos) 1"
    },
    {
      "id": "ID Permiso 2",
      "name": "Nombre de prueba 2",
      "description": "Descripción de prueba 2", // Opcional
      "canRead": true,
      "canCreate": true,
      "canUpdate": true,
      "canDelete": true,
      "fkProfileId": "ID Perfil 2",
      "fkModulePermissionId": "ID Módulo (Donde se aplicaran los permisos) 2"
    }
  ]
}
