import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, ne, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import {
  systemModuleAssignmentTacticalEquipment,
  systemModuleAssignmentUnit,
  systemModuleControlFatigue,
  systemModuleControlVehicleUnit,
} from '@repo/shared-drizzle/schemas';
import {
  assignUnitBodySchema,
  processUnitAssignment,
  validateDates,
  validateUnitActiveAssignment,
  validateUnitAssignmentInFatigue,
} from '../helpers/unit.helper';
import { handleUserChangeInTacticalEquipmentAssig } from '../helpers/tactical_equipment.helper';
import moment from 'moment';

type VehicleAssignGrouped = Record<
  string,
  {
    fkUserId: string;
    data: {
      id: string;
      fkUnitControlId: string;
      code: string | null;
      fkFatigueId: string;
      isDriver: boolean;
      isPassenger: boolean;
      isInCharge: boolean;
      assignmentDate: string;
      assignmentEndDate: string | null;
      estimatedReturnDate: string | null;
      periodUse: number | null;
      comments: string | null;
    }[];
  }
>;

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, grouped: groupedQS } = req.query;
  let deleted = false;
  let disabled = false;
  let grouped = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentUnit.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentUnit.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentUnit.id,
        fkUserId: systemModuleAssignmentUnit.fkUserId,
        fkUnitControlId: systemModuleAssignmentUnit.fkUnitControlId,
        code: systemModuleControlVehicleUnit.code,
        fkFatigueId: systemModuleAssignmentUnit.fkFatigueId,
        isDriver: systemModuleAssignmentUnit.isDriver,
        isPassenger: systemModuleAssignmentUnit.isPassenger,
        isInCharge: systemModuleAssignmentUnit.isInCharge,
        assignmentDate: systemModuleAssignmentUnit.assignmentDate,
        assignmentEndDate: systemModuleAssignmentUnit.assignmentEndDate,
        estimatedReturnDate: systemModuleAssignmentUnit.estimatedReturnDate,
        periodUse: systemModuleAssignmentUnit.periodUse,
        comments: systemModuleAssignmentUnit.comments,
      })
      .from(systemModuleAssignmentUnit)
      .leftJoin(
        systemModuleControlVehicleUnit,
        eq(systemModuleAssignmentUnit.fkUnitControlId, systemModuleControlVehicleUnit.id),
      )
      .where(and(...filters));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkUnitControlId: rest.fkUnitControlId,
            code: rest.code || null,
            fkFatigueId: rest.fkFatigueId,
            isDriver: rest.isDriver,
            isPassenger: rest.isPassenger,
            isInCharge: rest.isInCharge,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            estimatedReturnDate: rest.estimatedReturnDate || null,
            periodUse: rest.periodUse || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as VehicleAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIT}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleAssignmentUnit.id,
        fkUserId: systemModuleAssignmentUnit.fkUserId,
        fkUnitControlId: systemModuleAssignmentUnit.fkUnitControlId,
        code: systemModuleControlVehicleUnit.code,
        fkFatigueId: systemModuleAssignmentUnit.fkFatigueId,
        isDriver: systemModuleAssignmentUnit.isDriver,
        isPassenger: systemModuleAssignmentUnit.isPassenger,
        isInCharge: systemModuleAssignmentUnit.isInCharge,
        assignmentDate: systemModuleAssignmentUnit.assignmentDate,
        assignmentEndDate: systemModuleAssignmentUnit.assignmentEndDate,
        estimatedReturnDate: systemModuleAssignmentUnit.estimatedReturnDate,
        periodUse: systemModuleAssignmentUnit.periodUse,
        comments: systemModuleAssignmentUnit.comments,
      })
      .from(systemModuleAssignmentUnit)
      .leftJoin(
        systemModuleControlVehicleUnit,
        eq(systemModuleAssignmentUnit.fkUnitControlId, systemModuleControlVehicleUnit.id),
      )
      .where(eq(systemModuleAssignmentUnit.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIT}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar assignmentDate
    const date = new Date(req.body.assignmentDate.replace(/\//g, '-'));
    if (Number.isNaN(date.getTime())) {
      throw new Error('El campo assignmentDate no es una fecha valida');
    }

    // Validar estimatedReturnDate
    if (req.body.estimatedReturnDate) {
      const dateEnd = new Date(req.body.estimatedReturnDate.replace(/\//g, '-'));
      if (Number.isNaN(dateEnd.getTime())) {
        throw new Error('El campo estimatedReturnDate no es una fecha valida');
      }
      const startOfAssignment = new Date(date.setHours(0, 0, 0, 0));
      const startOfEstimated = new Date(dateEnd.setHours(0, 0, 0, 0));
      if (startOfEstimated < startOfAssignment) {
        throw new Error('La fecha estimada de retorno no puede ser anterior a la fecha de inicio');
      }
    }

    // Quitar campos no necesarios y establecer valores default solo en campos necesarios
    const { assignmentEndDate, isInCharge = false, ...rest } = req.body;

    const result = await db.transaction(async (trx) => {
      const resultCreate = await processUnitAssignment(trx, { ...rest, isInCharge }, true, null);
      return resultCreate;
    });

    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIT} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar fechas
    validateDates(req.body);

    // Validar payload
    const updateData = await assignUnitBodySchema.parseAsync({ ...req.body });

    // Validación de roles (isDriver e isPassenger)
    if (!updateData.isDriver && !updateData.isPassenger) {
      throw new Error(
        'Debe especificar al menos un rol del elemento como conductor (isDriver) o pasajero (isPassenger)',
      );
    }

    const result = await db.transaction(async (trx) => {
      // Obtener registro de asignación
      const assignment = await trx
        .select()
        .from(systemModuleAssignmentUnit)
        .where(eq(systemModuleAssignmentUnit.id, id))
        .limit(1);
      if (assignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar la existencia de la unidad
      const unit = await trx
        .select()
        .from(systemModuleControlVehicleUnit)
        .where(eq(systemModuleControlVehicleUnit.id, updateData.fkUnitControlId))
        .limit(1);
      if (unit.length === 0) {
        throw new Error('Unidad no encontrado');
      }

      // Detectar cambios relevantes
      const fatigueChanged =
        updateData.fkFatigueId !== undefined && updateData.fkFatigueId !== assignment[0].fkFatigueId;
      const driverStatusChanged = updateData.isDriver !== undefined && updateData.isDriver !== assignment[0].isDriver;
      const willBeDriver = updateData.isDriver ?? assignment[0].isDriver;

      // Validación de conductor único (si es aplicable)
      if (willBeDriver && (fatigueChanged || driverStatusChanged)) {
        const targetFatigueId = fatigueChanged ? updateData.fkFatigueId : assignment[0].fkFatigueId;
        const existingDriver = await trx
          .select()
          .from(systemModuleAssignmentUnit)
          .where(
            and(
              eq(systemModuleAssignmentUnit.fkFatigueId, targetFatigueId),
              eq(systemModuleAssignmentUnit.isDriver, true),
              ne(systemModuleAssignmentUnit.id, id),
            ),
          )
          .limit(1);
        if (existingDriver.length > 0) {
          throw new Error(
            `Ya existe un conductor asignado a la unidad para ${fatigueChanged ? 'el nuevo despliegue' : 'este despliegue'}. Solo puede haber un conductor por despliegue`,
          );
        }
      }

      // Validar si cambio de unidad, que no este en el mismo despliegue ya asignado
      if (updateData.fkUnitControlId !== assignment[0].fkUnitControlId) {
        validateUnitAssignmentInFatigue(trx, updateData.fkUnitControlId, updateData.fkFatigueId, id);
      }

      // Validar si la unidad tiene una asignación activa
      await validateUnitActiveAssignment(trx, updateData.fkUnitControlId, id);

      // Verificar si el usuario cambió
      if (updateData.fkUserId !== assignment[0].fkUserId) {
        await handleUserChangeInTacticalEquipmentAssig(
          trx,
          updateData.fkUserId,
          updateData.fkUnitControlId,
          updateData.fkFatigueId,
          id,
        );
      }

      // Calcular el periodo de uso si se proporciona assignmentEndDate
      let periodUse = assignment[0].periodUse;
      if (updateData.assignmentEndDate) {
        const diffDays = moment(updateData.assignmentEndDate).diff(moment(updateData.assignmentDate), 'days');
        periodUse = diffDays;
      }

      // Actualizar la asignación
      const assignUpdate = await trx
        .update(systemModuleAssignmentTacticalEquipment)
        .set({ ...updateData, periodUse })
        .where(eq(systemModuleAssignmentTacticalEquipment.id, id));

      return assignUpdate;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIT}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Validar que exista la asignación
      const existingAssignment = await trx
        .select()
        .from(systemModuleAssignmentUnit)
        .where(eq(systemModuleAssignmentUnit.id, id))
        .limit(1);
      if (existingAssignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar que la asignación no este en un despliegue activo
      const activeUnitAssignment = await trx
        .select()
        .from(systemModuleAssignmentUnit)
        .leftJoin(systemModuleControlFatigue, eq(systemModuleAssignmentUnit.fkFatigueId, systemModuleControlFatigue.id))
        .where(
          and(
            eq(systemModuleControlFatigue.status, 'active'),
            eq(systemModuleAssignmentUnit.fkFatigueId, existingAssignment[0].fkFatigueId),
            eq(systemModuleAssignmentUnit.id, id),
          ),
        )
        .limit(1);
      if (activeUnitAssignment.length > 0) {
        throw new Error('No es posible eliminar la asignación si este ya se encuentra en un despliegue activo');
      }

      const deleteResult = await trx
        .update(systemModuleAssignmentUnit)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleAssignmentUnit.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIT}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    q: qQS,
    grouped: groupedQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;
  let grouped = false;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentUnit.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentUnit.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleControlVehicleUnit.code, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentUnit.assignmentDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentUnit.assignmentEndDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentUnit.estimatedReturnDate}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleAssignmentUnit.comments, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleAssignmentUnit.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentUnit.id,
        fkUserId: systemModuleAssignmentUnit.fkUserId,
        fkUnitControlId: systemModuleAssignmentUnit.fkUnitControlId,
        code: systemModuleControlVehicleUnit.code,
        fkFatigueId: systemModuleAssignmentUnit.fkFatigueId,
        isDriver: systemModuleAssignmentUnit.isDriver,
        isPassenger: systemModuleAssignmentUnit.isPassenger,
        isInCharge: systemModuleAssignmentUnit.isInCharge,
        assignmentDate: systemModuleAssignmentUnit.assignmentDate,
        assignmentEndDate: systemModuleAssignmentUnit.assignmentEndDate,
        estimatedReturnDate: systemModuleAssignmentUnit.estimatedReturnDate,
        periodUse: systemModuleAssignmentUnit.periodUse,
        comments: systemModuleAssignmentUnit.comments,
      })
      .from(systemModuleAssignmentUnit)
      .leftJoin(
        systemModuleControlVehicleUnit,
        eq(systemModuleAssignmentUnit.fkUnitControlId, systemModuleControlVehicleUnit.id),
      )
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleAssignmentUnit.id));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkUnitControlId: rest.fkUnitControlId,
            code: rest.code || null,
            fkFatigueId: rest.fkFatigueId,
            isDriver: rest.isDriver,
            isPassenger: rest.isPassenger,
            isInCharge: rest.isInCharge,
            assignmentDate: rest.assignmentDate,
            assignmentEndDate: rest.assignmentEndDate || null,
            estimatedReturnDate: rest.estimatedReturnDate || null,
            periodUse: rest.periodUse || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as VehicleAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIT} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
