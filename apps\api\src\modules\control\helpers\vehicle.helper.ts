import { iphVehicleSituationValues, vehicleTerrainValues, vehicleUseTypeValues } from '@repo/types/values';
import { z } from 'zod';

export const vehicleBodySchema = z.object({
  fkVehicleStatusId: z.string().nullable().optional(),
  mileage: z.number(),
  terrain: z.enum(vehicleTerrainValues),
  isNational: z.boolean(),
  fkVehicleTypeId: z.string(),
  fkVehicleBrandId: z.string(),
  fkVehicleModelId: z.string(),
  year: z.number().nullable().optional(),
  fkVehicleColorId: z.string(),
  useType: z.enum(vehicleUseTypeValues),
  licensePlate: z.string(),
  capacity: z.number(),
  niv: z.string(),
  serialNumber: z.string(),
  situation: z.enum(iphVehicleSituationValues),
  fkLastUserId: z.string(),
  fkLastDependencyId: z.string(),
});
