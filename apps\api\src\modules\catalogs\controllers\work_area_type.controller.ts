import type { Request, Response } from 'express';
import { catDirectionType, catLeadershipType, catWorkAreaType } from '@repo/shared-drizzle/schemas';
import db from '@/db';
import { and, asc, eq, gt, like, or, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { getLocale } from '@/utils/locale_validator';
import { HttpStatus } from '@/utils/http_status';
import { z } from 'zod';
import { ModuleName } from '@/utils/module_names';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS } = req.query;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(catWorkAreaType.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(catWorkAreaType.isEnabled, true));
    }
    const result = await db
      .select({
        id: catWorkAreaType.id,
        key: catWorkAreaType.key,
        name: catWorkAreaType[locale],
        description: catWorkAreaType.description,
        fkDirectionId: catWorkAreaType.fkDirectionId,
        nameDirection: catDirectionType[locale],
        fkLeadershipId: catWorkAreaType.fkLeadershipId,
        nameLeadership: catLeadershipType[locale],
      })
      .from(catWorkAreaType)
      .leftJoin(catDirectionType, eq(catWorkAreaType.fkDirectionId, catDirectionType.id))
      .leftJoin(catLeadershipType, eq(catWorkAreaType.fkLeadershipId, catLeadershipType.id));
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'work_area_type/all - getAll',
      source: req.headers['user-agent'],
      module: ModuleName.CATALOGS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const result = await db
      .select({
        id: catWorkAreaType.id,
        key: catWorkAreaType.key,
        name: catWorkAreaType[locale],
        description: catWorkAreaType.description,
        fkDirectionId: catWorkAreaType.fkDirectionId,
        nameDirection: catDirectionType[locale],
        fkLeadershipId: catWorkAreaType.fkLeadershipId,
        nameLeadership: catLeadershipType[locale],
      })
      .from(catWorkAreaType)
      .leftJoin(catDirectionType, eq(catWorkAreaType.fkDirectionId, catDirectionType.id))
      .leftJoin(catLeadershipType, eq(catWorkAreaType.fkLeadershipId, catLeadershipType.id))
      .where(eq(catWorkAreaType.id, id));
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `work_area_type/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CATALOGS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const create = async (req: Request, res: Response) => {
  const bodySchema = z.object({
    key: z.string(),
    name: z.string(),
    description: z.string().optional(),
    fkDirectionId: z.string(),
    fkLeadershipId: z.string(),
    fkLastUserId: z.string(),
    fkLastDependencyId: z.string(),
  });

  const { locale: localeQS } = req.query;

  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const { name, ...insertData } = await bodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const result = await db.insert(catWorkAreaType).values({ [locale]: name, ...insertData });
    res.status(HttpStatus.CREATED).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'work_area_type - create',
      source: req.headers['user-agent'],
      module: ModuleName.CATALOGS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const update = async (req: Request, res: Response) => {
  const bodySchema = z.object({
    key: z.string(),
    name: z.string(),
    description: z.string().optional(),
    fkDirectionId: z.string(),
    fkLeadershipId: z.string(),
    fkLastUserId: z.string(),
    fkLastDependencyId: z.string(),
  });

  const { locale: localeQS } = req.query;
  const { id } = req.params;

  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const { name, ...updateData } = await bodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const result = await db
      .update(catWorkAreaType)
      .set({ ...updateData, [locale]: name })
      .where(eq(catWorkAreaType.id, id));
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `work_area_type/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CATALOGS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .update(catWorkAreaType)
      .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
      .where(eq(catWorkAreaType.id, id));

    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `work_area_type/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CATALOGS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    locale: localeQS,
    q: qQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(catWorkAreaType.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(catWorkAreaType.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(catWorkAreaType[locale], `%${q}%`));
      likeFilters.push(like(catDirectionType[locale], `%${q}%`));
      likeFilters.push(like(catLeadershipType[locale], `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(catWorkAreaType.id, lastId));
    }
    const result = await db
      .select({
        id: catWorkAreaType.id,
        key: catWorkAreaType.key,
        name: catWorkAreaType[locale],
        description: catWorkAreaType.description,
        nameDirection: catDirectionType[locale],
        fkDirectionId: catDirectionType.id,
        nameLeadership: catLeadershipType[locale],
      })
      .from(catWorkAreaType)
      .leftJoin(catDirectionType, eq(catWorkAreaType.fkDirectionId, catDirectionType.id))
      .leftJoin(catLeadershipType, eq(catWorkAreaType.fkLeadershipId, catLeadershipType.id))
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(catWorkAreaType.id));

    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'work_area_type - searchPaginated',
      source: req.headers['user-agent'],
      module: ModuleName.CATALOGS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};
