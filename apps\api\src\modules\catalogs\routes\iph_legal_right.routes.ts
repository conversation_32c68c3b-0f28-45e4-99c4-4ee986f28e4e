import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/iph_legal_right.controller';

const catIphLegalRightRouter = Router();

catIphLegalRightRouter.get('/', getAll);
catIphLegalRightRouter.get('/search', searchPaginated);
catIphLegalRightRouter.get('/:id', getOne);
catIphLegalRightRouter.post('/', create);
catIphLegalRightRouter.put('/:id', update);
catIphLegalRightRouter.delete('/:id', deleteOne);

export default catIphLegalRightRouter;
