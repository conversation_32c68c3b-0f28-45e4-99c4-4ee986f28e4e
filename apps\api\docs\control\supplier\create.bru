meta {
  name: create
  type: http
  seq: 5
}

post {
  url: {{base}}/control/supplier?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

body:json {
  {
    "name": "Nombre del Proveedor",
    "contactName": "Nombre del contacto", // Opcional
    "contactPhone": "Teléfono del contacto", // Opcional
    "contactEmail": "Correo electrónico del contacto", // Opcional
    "address": "Dirección del proveedor", // Opcional
    "city": "Ciudad del proveedor", // Opcional
    "state": "Estado del proveedor", // Opcional
    "ZipCode": "Código postal del proveedor", // Opcional
    "country": "País del proveedor", // Opcional
    "rfc": "RFC del proveedor", // Opcional
    "website": "Sitio Web del proveedor", // Opcional
    "comments": "Comentarios" // Opcional
  }
}
