import { Router } from 'express';
import {
  getAll,
  getOne,
  create,
  update,
  deleteOne,
  searchPaginated,
} from '@/modules/catalogs/controllers/shift_adjustment_type.controller';

const catShiftAdjustmentTypeRouter = Router();

catShiftAdjustmentTypeRouter.get('/', getAll);
catShiftAdjustmentTypeRouter.get('/search', searchPaginated);
catShiftAdjustmentTypeRouter.get('/:id', getOne);
catShiftAdjustmentTypeRouter.post('/', create);
catShiftAdjustmentTypeRouter.put('/:id', update);
catShiftAdjustmentTypeRouter.delete('/:id', deleteOne);

export default catShiftAdjustmentTypeRouter;
