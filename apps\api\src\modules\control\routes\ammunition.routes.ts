import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/ammunition.controller';

const ammunitionRouter = Router();

ammunitionRouter.get('/', getAll);
ammunitionRouter.get('/search', searchPaginated);
ammunitionRouter.get('/:id', getOne);
ammunitionRouter.post('/', create);
ammunitionRouter.put('/:id', update);
ammunitionRouter.delete('/:id', deleteOne);

export default ammunitionRouter;
