import type { Request, Response } from 'express';
import {
  catAppearanceOf,
  catColor,
  catWeaponCaliber,
  type IphInventory,
  systemModuleControlIphInventory,
} from '@repo/shared-drizzle/schemas';
import { HttpStatus } from '@/utils/http_status';
import { ModuleName } from '@/utils/module_names';
import { Logger } from '@/utils/logger';
import db from '@/db';
import { inventorySchema, weaponDataSchema } from '@repo/types/schemas';
import { getLocale, type Locale } from '@/utils/locale_validator';
import genrateKey from '@/utils/generate_key';
import z from 'zod';

const validate = async ({
  unsafeBody,
  userId,
  dependencyId,
  locale,
  source,
}: {
  unsafeBody: z.infer<typeof inventorySchema>;
  userId: string;
  dependencyId: string;
  locale: Locale;
  source: string | undefined;
}) => {
  const error: { [key: string]: unknown } = {};
  try {
    const dataToUpdate: IphInventory = {
      fkIphId: unsafeBody.iphId,
      fkLastUserId: userId,
      fkLastDependencyId: dependencyId,
      isEnabled: true,
      updatedAt: new Date(),
    };
    // number
    const number = inventorySchema.shape.number.safeParse(unsafeBody.number);
    if (number.success && number.data) {
      dataToUpdate.number = number.data;
    } else {
      error.number = number.error;
    }
    // isInspection
    const isInspection = inventorySchema.shape.isInspection.safeParse(unsafeBody.isInspection);
    if (isInspection.success && isInspection.data) {
      dataToUpdate.isInspection = isInspection.data;
    } else {
      error.isInspection = isInspection.error;
    }
    // inspectionType
    const inspectionType = inventorySchema.shape.inspectionType.safeParse(unsafeBody.inspectionType);
    if (inspectionType.success && inspectionType.data) {
      dataToUpdate.inspectionType = inspectionType.data;
    } else {
      error.inspectionType = inspectionType.error;
    }
    // itemType
    const itemType = inventorySchema.shape.itemType.safeParse(unsafeBody.itemType);
    if (itemType.success && itemType.data) {
      dataToUpdate.itemType = itemType.data;
    } else {
      error.itemType = itemType.error;
    }
    // whereWasFound
    const whereWasFound = inventorySchema.shape.whereWasFound.safeParse(unsafeBody.whereWasFound);
    if (whereWasFound.success && whereWasFound.data) {
      dataToUpdate.whereWasFound = whereWasFound.data;
    } else {
      error.whereWasFound = whereWasFound.error;
    }
    // weaponData
    const size = weaponDataSchema.shape.size.safeParse(unsafeBody.weaponData?.size);
    if (size.success) {
      dataToUpdate.weaponDataSize = size.data;
    } else {
      error.weaponDataSize = size.error;
    }
    const serialNumber = weaponDataSchema.shape.serialNumber.safeParse(unsafeBody.weaponData?.serialNumber);
    if (serialNumber.success) {
      dataToUpdate.weaponDataSerialNumber = serialNumber.data;
    } else {
      error.weaponDataSerialNumber = serialNumber.error;
    }
    const registrationNumber = weaponDataSchema.shape.registrationNumber.safeParse(
      unsafeBody.weaponData?.registrationNumber,
    );
    if (registrationNumber.success) {
      dataToUpdate.weaponDataRegistrationNumber = registrationNumber.data;
    } else {
      error.weaponDataRegistrationNumber = registrationNumber.error;
    }
    const color = weaponDataSchema.shape.color.safeParse(unsafeBody.weaponData?.color);
    if (color.success && color.data) {
      let id: string | undefined = color.data.id;
      if (color.data.action === 'create') {
        try {
          const newColor = await db.insert(catColor).values({
            id: color.data.id,
            [locale]: color.data.name,
            key: genrateKey(color.data.name),
            fkLastDependencyId: dependencyId,
            fkLastUserId: userId,
          });
          if (newColor.rowCount === 0) {
            error.objectData = z.ZodError.create([
              {
                fatal: true,
                message: 'Error creating color',
                code: z.ZodIssueCode.custom,
                path: ['color'],
              },
            ]);
            id = undefined;
          }
        } catch (err) {
          id = undefined;
          error.objectData = z.ZodError.create([
            {
              fatal: true,
              message: 'Error creating color',
              code: z.ZodIssueCode.custom,
              path: ['color'],
            },
          ]);
        }
      }
      if (id) {
        dataToUpdate.fkWeaponDataColorId = id;
      }
    } else {
      error.weaponDataColor = color.error;
    }
    const caliber = weaponDataSchema.shape.caliber.safeParse(unsafeBody.weaponData?.caliber);
    if (caliber.success && caliber.data) {
      let id: string | undefined = caliber.data.id;
      if (caliber.data.action === 'create') {
        try {
          const newCaliber = await db.insert(catWeaponCaliber).values({
            id: caliber.data.id,
            [locale]: caliber.data.name,
            key: genrateKey(caliber.data.name),
            fkLastDependencyId: dependencyId,
            fkLastUserId: userId,
          });
          if (newCaliber.rowCount === 0) {
            error.objectData = z.ZodError.create([
              {
                fatal: true,
                message: 'Error creating caliber',
                code: z.ZodIssueCode.custom,
                path: ['weapon_caliber'],
              },
            ]);
            id = undefined;
          }
        } catch (err) {
          id = undefined;
          error.objectData = z.ZodError.create([
            {
              fatal: true,
              message: 'Error creating color',
              code: z.ZodIssueCode.custom,
              path: ['weapon_caliber'],
            },
          ]);
        }
      }
      if (id) {
        dataToUpdate.fkWeaponDataCaliberId = id;
      }
    } else {
      error.weaponDataCaliber = caliber.error;
    }
    // objectData
    const objectData = inventorySchema.shape.objectData.safeParse(unsafeBody.objectData);
    if (objectData.success && objectData.data) {
      let id: string | undefined = objectData.data.id;
      if (objectData.data.action === 'create') {
        try {
          const newObject = await db.insert(catAppearanceOf).values({
            id: objectData.data.id,
            [locale]: objectData.data.name,
            key: genrateKey(objectData.data.name),
            fkLastDependencyId: dependencyId,
            fkLastUserId: userId,
          });
          if (newObject.rowCount === 0) {
            error.objectData = z.ZodError.create([
              {
                fatal: true,
                message: 'Error creating object',
                code: z.ZodIssueCode.custom,
                path: ['objectData'],
              },
            ]);
            id = undefined;
          }
        } catch (err) {
          id = undefined;
          error.objectData = z.ZodError.create([
            {
              fatal: true,
              message: 'Error creating object',
              code: z.ZodIssueCode.custom,
              path: ['objectData'],
            },
          ]);
        }
      }
      if (id) {
        dataToUpdate.fkObjectDatAappearanceOfId = id;
      }
    } else {
      error.objectData = objectData.error;
    }
    // observations
    const observations = inventorySchema.shape.observations.safeParse(unsafeBody.observations);
    if (observations.success && observations.data) {
      dataToUpdate.observations = observations.data;
    } else {
      error.observations = observations.error;
    }
    // destination
    const destination = inventorySchema.shape.destination.safeParse(unsafeBody.destination);
    if (destination.success && destination.data) {
      dataToUpdate.destination = destination.data;
    } else {
      error.destination = destination.error;
    }
    // whomSecuredData
    const whomSecuredData = inventorySchema.shape.whomSecuredData.safeParse(unsafeBody.whomSecuredData);
    if (whomSecuredData.success && whomSecuredData.data) {
      dataToUpdate.whomSecuredData = whomSecuredData.data;
    } else {
      error.whomSecuredData = whomSecuredData.error;
    }
    // witnessOne
    const witnessOne = inventorySchema.shape.witnessOne.safeParse(unsafeBody.witnessOne);
    if (witnessOne.success && witnessOne.data) {
      dataToUpdate.witnessOneData = witnessOne.data;
    } else {
      error.witnessOne = witnessOne.error;
    }
    // witnessTwo
    const witnessTwo = inventorySchema.shape.witnessTwo.safeParse(unsafeBody.witnessTwo);
    if (witnessTwo.success && witnessTwo.data) {
      dataToUpdate.witnessTwoData = witnessTwo.data;
    } else {
      error.witnessTwo = witnessTwo.error;
    }
    // firstRespondent
    const firstRespondent = inventorySchema.shape.firstRespondent.safeParse(unsafeBody.firstRespondent);
    if (firstRespondent.success && firstRespondent.data) {
      dataToUpdate.fkFirstRespondentUserId = firstRespondent.data.id;
      dataToUpdate.fkFirstRespondentPositionId = firstRespondent.data.position?.id;
      dataToUpdate.fkFirstRespondentSecondmentId = firstRespondent.data.secondment?.id;
    } else {
      error.firstRespondent = firstRespondent.error;
    }
    // id
    const id = inventorySchema.shape.id.safeParse(unsafeBody.id);
    if (id.success && id.data) {
      dataToUpdate.id = id.data;
    }

    if (Object.keys(dataToUpdate).length < 6) {
      // if there is no data to update more than the 4 required fields return Not Modified status
      // res.status(HttpStatus.NOT_MODIFIED).json({ message: 'No data to update', error });
      // return;
      error.id = z.ZodError.create([
        {
          fatal: true,
          message: 'No data to update',
          code: z.ZodIssueCode.custom,
          path: ['id'],
        },
      ]);
    }
    const updated = await db.insert(systemModuleControlIphInventory).values(dataToUpdate).onConflictDoUpdate({
      target: systemModuleControlIphInventory.id,
      set: dataToUpdate,
    });
    if ((updated.rowCount || 0) === 0) {
      error.id = z.ZodError.create([
        {
          fatal: true,
          message: 'Error creating interview',
          code: z.ZodIssueCode.custom,
          path: ['id'],
        },
      ]);
    }
    if (Object.keys(error).length > 0) {
      return { error, success: !error.id };
    }
    return { success: !error.id };
  } catch (err) {
    const { message, stack } = err as Error;
    error.transferExternalInstitution = z.ZodError.create([
      {
        fatal: true,
        message: message || 'Error creating interview',
        code: z.ZodIssueCode.custom,
        path: ['interview'],
      },
    ]);
    Logger.getInstance().error(message, {
      module: ModuleName.IPH,
      method: '/iph/annex/interviews/save - validate',
      source,
      stack,
    });
    return { error, success: false };
  }
};

export const save = async (req: Request, res: Response) => {
  const userId = req.userId as string;
  const dependencyId = req.dependencyId as string;
  const { locale: localeQS } = req.query;
  let locale = getLocale();
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const unsafeBody = req.body as z.infer<typeof inventorySchema>;
    const result = await validate({
      unsafeBody,
      userId,
      dependencyId,
      locale: locale,
      source: req.headers['user-agent'],
    });
    res.status(HttpStatus.CREATED).json(result);
  } catch (_error) {
    const { message, stack } = _error as Error;
    Logger.getInstance().error(message, {
      module: ModuleName.IPH,
      method: '/iph/annex/inventory/save - save',
      source: req.headers['user-agent'],
      stack,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message: message });
  }
};

export const saveAll = async (req: Request, res: Response) => {
  const userId = req.userId as string;
  const dependencyId = req.dependencyId as string;
  const { locale: localeQS } = req.query;
  let locale = getLocale();
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const arraySchema = z.array(inventorySchema);
    const unsafeBody = req.body as z.infer<typeof arraySchema>;
    const results = unsafeBody.map(async (unsafeItem) => {
      const result = await validate({
        unsafeBody: unsafeItem,
        userId,
        dependencyId,
        locale,
        source: req.headers['user-agent'],
      });
      return { [unsafeItem.id]: result };
    });
    const result = await Promise.all(results);
    res.status(HttpStatus.CREATED).json(result);
  } catch (_error) {
    const { message, stack } = _error as Error;
    Logger.getInstance().error(message, {
      module: ModuleName.IPH,
      method: '/iph/annex/inventory/save - save',
      source: req.headers['user-agent'],
      stack,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message: message });
  }
};
