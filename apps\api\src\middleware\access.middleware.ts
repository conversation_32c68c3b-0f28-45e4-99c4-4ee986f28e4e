import { Logger } from '@/utils/logger';
import type { Request, Response, NextFunction } from 'express';

const accessMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const url = req.originalUrl;
  const start = performance.now();
  res.on('finish', () => {
    const end = performance.now();
    const duration = end - start;
    const message = [
      req.method,
      url,
      res.statusCode < 400 ? res.statusCode : res.statusCode,
      '-',
      `${duration} ms`,
    ].join(' ');
    Logger.getInstance().info(message, {
      method: 'accessLogMiddleware',
      module: 'middleware',
      source: req.headers['user-agent'],
    });
  });
  next();
};

export default accessMiddleware;
