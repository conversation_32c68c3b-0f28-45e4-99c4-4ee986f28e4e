import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, isNull, like, or, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';
import {
  systemModuleControlQuadrantGrouping,
  systemModuleAssignmentQuadrantGrouping,
} from '@repo/shared-drizzle/schemas';
import { quadrantGroupingBodySchema } from '../helpers/quadrant_grouping.helper';
export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS } = req.query;
  let deleted = false;
  let disabled = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlQuadrantGrouping.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlQuadrantGrouping.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlQuadrantGrouping.id,
        name: systemModuleControlQuadrantGrouping.name,
        description: systemModuleControlQuadrantGrouping.description,
      })
      .from(systemModuleControlQuadrantGrouping)
      .where(and(...filters));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.QUADRANT_GROUPING}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleControlQuadrantGrouping.id,
        name: systemModuleControlQuadrantGrouping.name,
        description: systemModuleControlQuadrantGrouping.description,
      })
      .from(systemModuleControlQuadrantGrouping)
      .where(eq(systemModuleControlQuadrantGrouping.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.QUADRANT_GROUPING}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    const insertData = quadrantGroupingBodySchema.parse({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_AGRUPACION_CUADRANTES);
    if (!systemModule) {
      throw new Error('System module CTRL_AGRUPACION_CUADRANTES not found');
    }
    const result = await db.insert(systemModuleControlQuadrantGrouping).values({
      ...insertData,
      fkSystemModuleId: systemModule.id,
    });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.QUADRANT_GROUPING} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;
    // Validar payload
    const updateData = await quadrantGroupingBodySchema.parseAsync({ ...req.body });
    const result = await db.transaction(async (trx) => {
      // Obtener datos del grupo de cuadrantes
      const quadrtantGrouping = await trx
        .select()
        .from(systemModuleControlQuadrantGrouping)
        .where(eq(systemModuleControlQuadrantGrouping.id, id))
        .limit(1);
      if (quadrtantGrouping.length === 0) {
        throw new Error('Agrupación de cuadrantes no encontrada');
      }

      // Actualizar el grupo de cuadrantes
      const updateResult = await trx
        .update(systemModuleControlQuadrantGrouping)
        .set({
          ...updateData,
          fkLastUserId: req.userId,
          fkLastDependencyId: req.dependencyId,
        })
        .where(eq(systemModuleControlQuadrantGrouping.id, id));

      return updateResult;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.QUADRANT_GROUPING}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Verificar si el agrupador de cuadrante está asignado actualmente a un usuario
      const activeQuadrantGroupingAssignment = await trx
        .select()
        .from(systemModuleAssignmentQuadrantGrouping)
        .where(
          and(
            eq(systemModuleAssignmentQuadrantGrouping.fkQuadrantGroupingId, id),
            isNull(systemModuleAssignmentQuadrantGrouping.assignmentEndDate),
          ),
        )
        .limit(1);

      if (activeQuadrantGroupingAssignment.length > 0) {
        throw new Error(
          'No se puede eliminar el agrupador de cuadrante porque está asignado actualmente a una operación',
        );
      }

      // Marcar el agrupador de cuadrante como eliminado
      const deleteResult = await trx
        .update(systemModuleControlQuadrantGrouping)
        .set({
          isDeleted: true,
          deletedAt: new Date(),
          fkLastUserId: req.userId,
          fkLastDependencyId: req.dependencyId,
        })
        .where(eq(systemModuleControlQuadrantGrouping.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.QUADRANT_GROUPING}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, deleted: deletedQS, disabled: disabledQS, q: qQS } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlQuadrantGrouping.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlQuadrantGrouping.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleControlQuadrantGrouping.name, `%${q}%`));
      likeFilters.push(like(systemModuleControlQuadrantGrouping.description, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleControlQuadrantGrouping.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlQuadrantGrouping.id,
        name: systemModuleControlQuadrantGrouping.name,
        description: systemModuleControlQuadrantGrouping.description,
      })
      .from(systemModuleControlQuadrantGrouping)
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlQuadrantGrouping.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.QUADRANT_GROUPING} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
