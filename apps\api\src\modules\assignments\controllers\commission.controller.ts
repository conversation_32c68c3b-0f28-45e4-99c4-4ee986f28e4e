import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, eq, gt, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';
import {
  systemModuleAssignmentCommission,
  systemModuleControlCommission,
  systemModuleControlFatigue,
} from '@repo/shared-drizzle/schemas';
import { assignCommissionBodySchema } from '../helpers/commission.helper';

type CommissionAssignGrouped = Record<
  string,
  {
    fkUserId: string;
    data: {
      id: string;
      fkCommissionId: string;
      fkFatigueId: string;
      isApproved: boolean;
      startedAt: Date;
      endedAt: Date | null;
      comments: string | null;
    }[];
  }
>;

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, grouped: groupedQS } = req.query;
  let deleted = false;
  let disabled = false;
  let grouped = false;

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentCommission.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentCommission.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentCommission.id,
        fkUserId: systemModuleAssignmentCommission.fkUserId,
        fkCommissionId: systemModuleAssignmentCommission.fkCommissionId,
        fkFatigueId: systemModuleAssignmentCommission.fkFatigueId,
        isApproved: systemModuleAssignmentCommission.isApproved,
        startedAt: systemModuleAssignmentCommission.startedAt,
        endedAt: systemModuleAssignmentCommission.endedAt,
        comments: systemModuleAssignmentCommission.comments,
      })
      .from(systemModuleAssignmentCommission)
      .where(and(...filters));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkCommissionId: rest.fkCommissionId,
            fkFatigueId: rest.fkFatigueId,
            isApproved: rest.isApproved,
            startedAt: rest.startedAt || null,
            endedAt: rest.endedAt || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as CommissionAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.COMMISSION}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .select({
        id: systemModuleAssignmentCommission.id,
        fkUserId: systemModuleAssignmentCommission.fkUserId,
        fkCommissionId: systemModuleAssignmentCommission.fkCommissionId,
        fkFatigueId: systemModuleAssignmentCommission.fkFatigueId,
        isApproved: systemModuleAssignmentCommission.isApproved,
        startedAt: systemModuleAssignmentCommission.startedAt,
        endedAt: systemModuleAssignmentCommission.endedAt,
        comments: systemModuleAssignmentCommission.comments,
      })
      .from(systemModuleAssignmentCommission)
      .where(eq(systemModuleAssignmentCommission.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.COMMISSION}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar startedAt
    const date = new Date(req.body.startedAt.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('El campo startedAt no es una fecha valida');
    }

    // Validar endedAt
    if (req.body.endedAt) {
      const dateEnd = new Date(req.body.endedAt.replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(dateEnd.getTime())) {
        throw new Error('El campo endedAt no es una fecha valida');
      }
      if (dateEnd < date) {
        throw new Error('La fecha de finalización no puede ser anterior a la fecha de inicio');
      }
    }

    // Validar payload
    const insertData = await assignCommissionBodySchema.parseAsync({ ...req.body });
    const result = await db.transaction(async (trx) => {
      // Validar la existencia de la comisión
      const commission = await trx
        .select()
        .from(systemModuleControlCommission)
        .where(eq(systemModuleControlCommission.id, insertData.fkCommissionId))
        .limit(1);
      if (commission.length === 0) {
        throw new Error('Comisión no encontrada');
      }

      const systemModuleCache = SystemModuleCache.getInstance();
      const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_ASIGNACIONES_COMISIONES);
      if (!systemModule) {
        throw new Error('System module CTRL_ASIGNACIONES_COMISIONES not found');
      }
      const newAssignment = await trx.insert(systemModuleAssignmentCommission).values({
        ...insertData,
        fkSystemModuleId: systemModule.id,
      });
      return newAssignment;
    });

    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.COMMISSION} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    // Validar startedAt
    const date = new Date(req.body.startedAt.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('El campo startedAt no es una fecha valida');
    }

    // Validar endedAt
    if (req.body.endedAt) {
      const dateEnd = new Date(req.body.endedAt.replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(dateEnd.getTime())) {
        throw new Error('El campo endedAt no es una fecha valida');
      }
      if (dateEnd < date) {
        throw new Error('La fecha de finalización no puede ser anterior a la fecha de inicio');
      }
    }

    // Validar payload
    const updateData = await assignCommissionBodySchema.parseAsync({ ...req.body });
    const result = await db.transaction(async (trx) => {
      // Obtener registro de asignación
      const assignment = await trx
        .select()
        .from(systemModuleAssignmentCommission)
        .where(eq(systemModuleAssignmentCommission.id, id))
        .limit(1);
      if (assignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar la existencia de la comisión
      const commission = await trx
        .select()
        .from(systemModuleControlCommission)
        .where(eq(systemModuleControlCommission.id, updateData.fkCommissionId))
        .limit(1);
      if (commission.length === 0) {
        throw new Error('Comisión no encontrada');
      }

      // Actualizar la asignación
      const assignUpdate = await trx
        .update(systemModuleAssignmentCommission)
        .set({ ...updateData })
        .where(eq(systemModuleAssignmentCommission.id, id));

      return assignUpdate;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.COMMISSION}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (tx) => {
      // Validar que exista la asignación
      const existingAssignment = await tx
        .select()
        .from(systemModuleAssignmentCommission)
        .where(eq(systemModuleAssignmentCommission.id, id))
        .limit(1);
      if (existingAssignment.length === 0) {
        throw new Error('Asignación no encontrada');
      }

      // Validar que la asignación no este en un despliegue activo
      const activeCommissionAssignment = await tx
        .select()
        .from(systemModuleAssignmentCommission)
        .leftJoin(
          systemModuleControlFatigue,
          eq(systemModuleAssignmentCommission.fkFatigueId, systemModuleControlFatigue.id),
        )
        .where(
          and(
            eq(systemModuleControlFatigue.status, 'active'),
            eq(systemModuleAssignmentCommission.fkFatigueId, existingAssignment[0].fkFatigueId),
            eq(systemModuleAssignmentCommission.id, id),
          ),
        )
        .limit(1);
      if (activeCommissionAssignment.length > 0) {
        throw new Error('No es posible eliminar la asignación si este ya se encuentra en un despliegue activo');
      }

      const deleteResult = await tx
        .update(systemModuleAssignmentCommission)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleAssignmentCommission.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.COMMISSION}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    q: qQS,
    grouped: groupedQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let q = null;
  let grouped = false;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }
  if (groupedQS && typeof groupedQS === 'string') {
    grouped = groupedQS === 'true';
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleAssignmentCommission.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleAssignmentCommission.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(sql`${systemModuleAssignmentCommission.startedAt}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleAssignmentCommission.endedAt}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleAssignmentCommission.comments, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleAssignmentCommission.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleAssignmentCommission.id,
        fkUserId: systemModuleAssignmentCommission.fkUserId,
        fkCommissionId: systemModuleAssignmentCommission.fkCommissionId,
        fkFatigueId: systemModuleAssignmentCommission.fkFatigueId,
        isApproved: systemModuleAssignmentCommission.isApproved,
        startedAt: systemModuleAssignmentCommission.startedAt,
        endedAt: systemModuleAssignmentCommission.endedAt,
        comments: systemModuleAssignmentCommission.comments,
      })
      .from(systemModuleAssignmentCommission)
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleAssignmentCommission.id));
    if (grouped) {
      const groupedUsers = result.reduce((acc, row) => {
        const { fkUserId, ...rest } = row;
        if (!acc[fkUserId]) {
          acc[fkUserId] = {
            fkUserId,
            data: [],
          };
        }
        if (rest) {
          acc[fkUserId].data.push({
            id: rest.id,
            fkCommissionId: rest.fkCommissionId,
            fkFatigueId: rest.fkFatigueId,
            isApproved: rest.isApproved,
            startedAt: rest.startedAt,
            endedAt: rest.endedAt || null,
            comments: rest.comments || null,
          });
        }
        return acc;
      }, {} as CommissionAssignGrouped);
      // Devolver los resultados
      const resultGrouped = Object.values(groupedUsers);
      res.status(HttpStatus.OK).json(resultGrouped);
      return;
    }
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.COMMISSION} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.ASSIGNMENTS,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
