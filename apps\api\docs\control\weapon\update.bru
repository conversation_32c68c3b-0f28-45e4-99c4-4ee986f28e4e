meta {
  name: update
  type: http
  seq: 6
}

put {
  url: {{base}}/control/weapon/:id?locale=es
  body: json
  auth: none
}

params:query {
  locale: es
}

params:path {
  id: 
}

body:json {
  {
    "serialNumber": "N° Serie",
    "nationalRegistryNumber": "N° Registro Nacional", // Opcional
    "fkWeaponTypeId": "ID Tipo de Arma",
    "weaponSize": "Tamaño de Arma", // ['short', 'long']
    "fkAmmunitionTypeId": "ID Tipo de Munición",
    "locId": "ID Licencia Oficial Colectiva", // Opcional
    "fkWeaponStatusId": "ID Estado del Arma",
    "fkOwnershipStatusId": "ID Estado de Propiedad"
  }
}
