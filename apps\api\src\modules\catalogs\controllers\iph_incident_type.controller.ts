import type { Request, Response } from 'express';
import { catIphIncidentType } from '@repo/shared-drizzle/schemas';
import db from '@/db';
import { and, asc, eq, gt, like, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { getLocale } from '@/utils/locale_validator';
import { HttpStatus } from '@/utils/http_status';
import { z } from 'zod';
import { incidentTypePriorityValues, iphSectionValues } from '@repo/types/values';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS } = req.query;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(catIphIncidentType.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(catIphIncidentType.isEnabled, true));
    }
    const result = await db
      .select({
        id: catIphIncidentType.id,
        key: catIphIncidentType.key,
        name: catIphIncidentType[locale],
        icon: catIphIncidentType.icon,
        code: catIphIncidentType.code,
        priority: catIphIncidentType.priority,
        sections: catIphIncidentType.sections,
        caseType: catIphIncidentType.caseType,
        description: catIphIncidentType.description,
      })
      .from(catIphIncidentType);
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'iph_incident_type/all - getAll',
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  try {
    const result = await db
      .select({
        id: catIphIncidentType.id,
        key: catIphIncidentType.key,
        name: catIphIncidentType[locale],
        icon: catIphIncidentType.icon,
        code: catIphIncidentType.code,
        priority: catIphIncidentType.priority,
        sections: catIphIncidentType.sections,
        caseType: catIphIncidentType.caseType,
        description: catIphIncidentType.description,
      })
      .from(catIphIncidentType)
      .where(eq(catIphIncidentType.id, id));
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `iph_incident_type/${id} - getOne`,
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const create = async (req: Request, res: Response) => {
  const bodySchema = z.object({
    key: z.string(),
    name: z.string(),
    description: z.string().optional(),
    icon: z.string(),
    priority: z.enum(incidentTypePriorityValues),
    code: z.number(),
    sections: z.enum(iphSectionValues).array().nullable().optional(),
    caseType: z.enum(['criminal', 'administrative']),
    fkLastUserId: z.string(),
    fkLastDependencyId: z.string(),
  });

  const { locale: localeQS } = req.query;

  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const { name, ...insertData } = await bodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const result = await db.insert(catIphIncidentType).values({ [locale]: name, ...insertData });
    res.status(HttpStatus.CREATED).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'iph_incident_type - create',
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const update = async (req: Request, res: Response) => {
  const bodySchema = z.object({
    key: z.string().optional(),
    name: z.string().optional(),
    icon: z.string().optional(),
    priority: z.enum(incidentTypePriorityValues),
    code: z.number(),
    sections: z.enum(iphSectionValues).array().nullable().optional(),
    caseType: z.enum(['criminal', 'administrative']).optional(),
    description: z.string().optional(),
    fkLastUserId: z.string(),
    fkLastDependencyId: z.string(),
  });

  const { locale: localeQS } = req.query;
  const { id } = req.params;

  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    // TODO: verify what happens if name is not provided
    const { name, ...updateData } = await bodySchema.parseAsync({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const result = await db
      .update(catIphIncidentType)
      .set({ ...updateData, [locale]: name })
      .where(eq(catIphIncidentType.id, id));
    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `iph_incident_type/${id} - update`,
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db
      .update(catIphIncidentType)
      .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
      .where(eq(catIphIncidentType.id, id));

    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `iph_incident_type/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    caseType: caseTypeQS,
    locale: localeQS,
    q: qQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let caseType: 'criminal' | 'administrative' | undefined = undefined;
  let locale = getLocale();
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (caseTypeQS && typeof caseTypeQS === 'string') {
    if (caseTypeQS === 'administrative' || caseTypeQS === 'criminal') {
      caseType = caseTypeQS;
    }
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(catIphIncidentType.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(catIphIncidentType.isEnabled, true));
    }
    if (caseType) {
      filters.push(eq(catIphIncidentType.caseType, caseType));
    }
    if (q) {
      filters.push(like(catIphIncidentType[locale], `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(catIphIncidentType.id, lastId));
    }
    const result = await db
      .select({
        id: catIphIncidentType.id,
        key: catIphIncidentType.key,
        name: catIphIncidentType[locale],
        icon: catIphIncidentType.icon,
        code: catIphIncidentType.code,
        priority: catIphIncidentType.priority,
        sections: catIphIncidentType.sections,
        caseType: catIphIncidentType.caseType,
        description: catIphIncidentType.description,
      })
      .from(catIphIncidentType)
      .where(and(...filters))
      .limit(limit)
      .orderBy(asc(catIphIncidentType.id));

    res.status(HttpStatus.OK).json(result);
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: 'iph_incident_type - searchPaginated',
      source: req.headers['user-agent'],
      module: 'catalogs',
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
  }
};
