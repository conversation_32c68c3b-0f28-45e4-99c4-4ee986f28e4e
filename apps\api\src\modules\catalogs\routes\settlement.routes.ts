import { Router } from 'express';
import {
  getByZipCode,
  getOne,
  create,
  update,
  deleteOne,
  searchByZipPaginated,
  getByStateAndMunicipality,
  searchByStateAndMunicipalityPaginated,
} from '@/modules/catalogs/controllers/settlement.controller';

const catSettlementRouter = Router();

catSettlementRouter.get('/:state/:municipality/search', searchByStateAndMunicipalityPaginated);
catSettlementRouter.get('/:state/:municipality', getByStateAndMunicipality);
catSettlementRouter.get('/:zip/search', searchByZipPaginated);
catSettlementRouter.get('/:zip', getByZipCode);
catSettlementRouter.get('/:id', getOne);
catSettlementRouter.post('/', create);
catSettlementRouter.put('/:id', update);
catSettlementRouter.delete('/:id', deleteOne);

export default catSettlementRouter;
