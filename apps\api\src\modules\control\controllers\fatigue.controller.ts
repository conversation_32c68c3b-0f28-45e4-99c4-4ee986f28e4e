import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, between, eq, gt, gte, inArray, like, lte, not, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';
import {
  type AssignedUser,
  createBaseMetadata,
  createShiftAssignments,
  fatigueBodySchema,
  groupFatigueNameResolvers,
  identifyAssignments,
  parseFatigueGroupByParam,
  replaceFatigueGroupByField,
  startRegisterInKardex,
  validateFatigueConditions,
} from '../helpers/fatigue.helper';
import {
  type ShiftAssign,
  systemModuleAssignmentFatigue,
  systemModuleAssignmentOperationQuadrant,
  systemModuleAssignmentShift,
  systemModuleAssignmentUnit,
  systemModuleControlFatigue,
  systemModuleControlFatiguePlace,
  systemModuleControlOperationQuadrant,
  systemModuleControlShift,
  systemModuleControlVehicleUnit,
  systemModuleUser,
} from '@repo/shared-drizzle/schemas';
import { assignShiftBodySchema } from '@/modules/assignments/helpers/shift.helper';
import type { ShiftAssignMetadata } from '../interfaces/fatigue.interface';
import {
  getKeyFromValue,
  type GroupedItem,
  parseBooleanQueryParam,
  parseDateQueryParam,
  parseNumberQueryParam,
  parseValueQueryParam,
} from '@/utils/default_values';
import type { FatiguePlaceType } from '@repo/types/types';
import moment from 'moment';

export const getAll = async (req: Request, res: Response) => {
  const {
    deleted: deletedQS,
    disabled: disabledQS,
    assigned: assignedQS,
    grouped: groupedQS,
    groupedBy: groupedByQS,
    startDate: startDateQS,
    endDate: endDateQS,
  } = req.query;
  try {
    // Parsear parámetros de consulta
    const deleted = parseBooleanQueryParam(deletedQS, false);
    const disabled = parseBooleanQueryParam(disabledQS, false);
    const assigned = parseBooleanQueryParam(assignedQS, false);
    const grouped = parseBooleanQueryParam(groupedQS, false);
    const groupedBy = parseFatigueGroupByParam(groupedByQS);
    const startDate = parseDateQueryParam(startDateQS);
    const endDate = parseDateQueryParam(endDateQS);

    if (grouped && !groupedBy) {
      throw new Error(
        `El parámetro groupedBy es obligatorio cuando se utiliza el parámetro grouped. Si lo esta enviando, el valor debe pertenecer a la siguiente lista: ${Object.keys(replaceFatigueGroupByField).join(', ')}`,
      );
    }

    if (startDate && endDate && startDate > endDate) {
      throw new Error('La fecha de inicio no puede ser mayor que la fecha de fin');
    }

    // Construir consulta
    const filters: SQL[] = [
      !deleted && eq(systemModuleControlFatigue.isDeleted, false),
      !disabled && eq(systemModuleControlFatigue.isEnabled, true),
    ].filter(Boolean) as SQL[];

    // Filtro por rango de fechas
    if (startDate && endDate) {
      filters.push(between(systemModuleControlFatigue.fatigueDate, startDate, endDate));
    } else if (startDate) {
      filters.push(gte(systemModuleControlFatigue.fatigueDate, startDate));
    } else if (endDate) {
      filters.push(lte(systemModuleControlFatigue.fatigueDate, endDate));
    }

    const fatigueItems = await db
      .select({
        id: systemModuleControlFatigue.id,
        fkUserId: systemModuleControlFatigue.fkUserId,
        aliasUser: systemModuleUser.alias,
        fkShiftId: systemModuleControlFatigue.fkShiftId,
        shiftName: systemModuleControlShift.shiftName,
        fatigueDate: systemModuleControlFatigue.fatigueDate,
        fatigueStartTime: systemModuleControlShift.startTime,
        fatigueEndTime: systemModuleControlShift.endTime,
        //assignedQuantity: sql<number>`CAST(COUNT(${systemModuleAssignmentFatigue.id}) AS INTEGER)`,
        //assignedUsers: assigned ? [] : [],
        status: systemModuleControlFatigue.status,
        isClosed: systemModuleControlFatigue.isClosed,
        closedAt: systemModuleControlFatigue.closedAt,
      })
      .from(systemModuleControlFatigue)
      .leftJoin(systemModuleUser, eq(systemModuleControlFatigue.fkUserId, systemModuleUser.id))
      .leftJoin(systemModuleControlShift, eq(systemModuleControlFatigue.fkShiftId, systemModuleControlShift.id))
      .where(and(...filters))
      .groupBy(
        systemModuleControlFatigue.id,
        systemModuleUser.alias,
        systemModuleControlShift.shiftName,
        systemModuleControlShift.startTime,
        systemModuleControlShift.endTime,
      );

    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    const fatigueMap = new Map<string, any>();
    for (const item of fatigueItems) {
      fatigueMap.set(item.id, { ...item, assignedUsers: [] });
    }

    if (assigned) {
      const assignedUsersByFatigue = await db
        .select({
          fkFatigueId: systemModuleAssignmentShift.fkFatigueId,
          assignedUsers: sql<AssignedUser[]>`jsonb_agg(jsonb_build_object(
          'fkUserId', ${systemModuleAssignmentShift.fkUserId},
          'alias', ${systemModuleUser.alias},
          'checkInTime', ${systemModuleAssignmentShift.checkInTime},
          'isLate', ${systemModuleAssignmentShift.isLate},
          'isDenied', ${systemModuleAssignmentShift.isDenied},
          'denialReason', ${systemModuleAssignmentShift.denialReason},
          'status', ${systemModuleAssignmentShift.status}
        ))`,
        })
        .from(systemModuleAssignmentShift)
        .leftJoin(systemModuleUser, eq(systemModuleAssignmentShift.fkUserId, systemModuleUser.id))
        .groupBy(systemModuleAssignmentShift.fkFatigueId);

      for (const item of assignedUsersByFatigue) {
        const existing = fatigueMap.get(item.fkFatigueId);
        if (existing) {
          existing.assignedUsers = item.assignedUsers;
          existing.assignedQuantity = item.assignedUsers.length;
        }
      }
    }

    const finalItems = Array.from(fatigueMap.values());

    if (grouped && groupedBy) {
      const groupedData: Record<string, GroupedItem> = {};
      for (const item of finalItems) {
        const groupKey = item[groupedBy];
        if (!groupedData[groupKey]) {
          const resolver = groupFatigueNameResolvers[groupedBy];
          const groupName = resolver ? await resolver(groupKey) : null;
          groupedData[groupKey] = {
            groupKey,
            groupName,
            groupType: getKeyFromValue(groupedBy, replaceFatigueGroupByField) ?? groupedBy,
            quantityItems: 0,
            items: [],
          };
        }
        groupedData[groupKey].quantityItems += 1;
        groupedData[groupKey].items.push(item);
      }
      const groupedResult = Object.values(groupedData);
      res.status(HttpStatus.OK).json(groupedResult);
      return;
    }
    res.status(HttpStatus.OK).json(finalItems);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { assigned: assignedQS } = req.query;
  try {
    const assigned = parseBooleanQueryParam(assignedQS, false);
    // Construir consulta
    const result = await db.transaction(async (trx) => {
      const baseResult = await trx
        .select({
          id: systemModuleControlFatigue.id,
          fkUserId: systemModuleControlFatigue.fkUserId,
          aliasUser: systemModuleUser.alias,
          fkShiftId: systemModuleControlFatigue.fkShiftId,
          shiftName: systemModuleControlShift.shiftName,
          fatigueDate: systemModuleControlFatigue.fatigueDate,
          fatigueStartTime: systemModuleControlShift.startTime,
          fatigueEndTime: systemModuleControlShift.endTime,
          assignedQuantity: sql<number>`CAST(COUNT(${systemModuleAssignmentFatigue.id}) AS INTEGER)`,
          //assignedUsers: assigned ? [] : [],
          status: systemModuleControlFatigue.status,
          isClosed: systemModuleControlFatigue.isClosed,
          closedAt: systemModuleControlFatigue.closedAt,
        })
        .from(systemModuleControlFatigue)
        .leftJoin(systemModuleUser, eq(systemModuleControlFatigue.fkUserId, systemModuleUser.id))
        .leftJoin(
          systemModuleAssignmentFatigue,
          eq(systemModuleControlFatigue.id, systemModuleAssignmentFatigue.fkFatigueId),
        )
        .leftJoin(systemModuleControlShift, eq(systemModuleControlFatigue.fkShiftId, systemModuleControlShift.id))
        .where(eq(systemModuleControlFatigue.id, id))
        .groupBy(
          systemModuleControlFatigue.id,
          systemModuleUser.alias,
          systemModuleControlShift.shiftName,
          systemModuleControlShift.startTime,
          systemModuleControlShift.endTime,
        );
      // Manejar asignaciones, si es necesario
      if (assigned) {
        const users = await trx
          .select({
            id: systemModuleAssignmentFatigue.id,
            fkUserId: systemModuleAssignmentFatigue.fkUserId,
            alias: systemModuleUser.alias,
          })
          .from(systemModuleAssignmentFatigue)
          .leftJoin(systemModuleUser, eq(systemModuleAssignmentFatigue.fkUserId, systemModuleUser.id))
          .where(eq(systemModuleAssignmentFatigue.fkFatigueId, id));
        const assignedResult = await Promise.all(
          users.map(async (row) => {
            const unit = await trx
              .select({
                id: systemModuleAssignmentUnit.id,
                fkUnitControlId: systemModuleAssignmentUnit.fkUnitControlId,
                code: systemModuleControlVehicleUnit.code,
                isDriver: systemModuleAssignmentUnit.isDriver,
                isPassenger: systemModuleAssignmentUnit.isPassenger,
                isInCharge: systemModuleAssignmentUnit.isInCharge,
                comments: systemModuleAssignmentUnit.comments,
              })
              .from(systemModuleAssignmentUnit)
              .leftJoin(
                systemModuleControlVehicleUnit,
                eq(systemModuleAssignmentUnit.fkUnitControlId, systemModuleControlVehicleUnit.id),
              )
              .where(
                and(
                  eq(systemModuleAssignmentUnit.fkUserId, row.fkUserId),
                  eq(systemModuleAssignmentUnit.fkFatigueId, id),
                  eq(systemModuleAssignmentUnit.isDeleted, false),
                ),
              );
            const assign = await trx
              .select({
                places: systemModuleAssignmentFatigue.places,
                categories: systemModuleAssignmentFatigue.categories,
              })
              .from(systemModuleAssignmentFatigue)
              .where(
                and(
                  eq(systemModuleAssignmentFatigue.fkUserId, row.fkUserId),
                  eq(systemModuleAssignmentFatigue.fkFatigueId, id),
                ),
              );

            const quadrants = await trx
              .select({
                id: systemModuleAssignmentOperationQuadrant.id,
                fkOperationQuadrantId: systemModuleAssignmentOperationQuadrant.fkOperationQuadrantId,
                name: systemModuleControlOperationQuadrant.quadrantName,
                comments: systemModuleAssignmentOperationQuadrant.comments,
              })
              .from(systemModuleAssignmentOperationQuadrant)
              .leftJoin(
                systemModuleControlOperationQuadrant,
                eq(
                  systemModuleAssignmentOperationQuadrant.fkOperationQuadrantId,
                  systemModuleControlOperationQuadrant.id,
                ),
              )
              .where(
                and(
                  eq(systemModuleAssignmentOperationQuadrant.fkUserId, row.fkUserId),
                  eq(systemModuleAssignmentOperationQuadrant.fkFatigueId, id),
                  eq(systemModuleAssignmentOperationQuadrant.isDeleted, false),
                ),
              );
            let placesMap: { id: string; name: string; type: FatiguePlaceType | null }[] = [];
            if (assign.length > 0 && (assign[0].places || []).length > 0) {
              const placesData = await trx
                .select({
                  id: systemModuleControlFatiguePlace.id,
                  name: systemModuleControlFatiguePlace.name,
                  type: systemModuleControlFatiguePlace.type,
                })
                .from(systemModuleControlFatiguePlace)
                .where(
                  and(
                    inArray(systemModuleControlFatiguePlace.id, assign[0].places),
                    eq(systemModuleControlFatiguePlace.isDeleted, false),
                  ),
                );
              placesMap = placesData.map((place) => {
                return { id: place.id, name: place.name, type: place.type };
              });
            }
            return {
              ...row,
              assignments: {
                places: placesMap,
                categories: (assign.length || 0) > 0 ? assign[0].categories : [],
                unit:
                  (unit.length || 0) > 0
                    ? {
                        id: unit[0].id,
                        fkUnitControlId: unit[0].fkUnitControlId,
                        code: unit[0].code,
                        isDriver: unit[0].isDriver,
                        isPassenger: unit[0].isPassenger,
                        isInCharge: unit[0].isInCharge,
                        comments: unit[0].comments,
                      }
                    : null,
                quadrants,
              },
            };
          }),
        );
        if (baseResult.length === 0) {
          res.status(HttpStatus.OK).json([]);
          return;
        }
        res.status(HttpStatus.OK).json({ ...baseResult[0], users: assignedResult });
        return;
      }
      res.status(HttpStatus.OK).json(baseResult);
      return;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    // Validar fecha
    const date = new Date(req.body.fatigueDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('La fecha "fatigueDate" no es una fecha valida');
    }

    // No se permite cierre al crear un nuevo despliegue
    const { isClosed, closedAt, arrayUsersIds, isTemplate, ...rest } = req.body;

    const insertData = await fatigueBodySchema.parseAsync({
      ...rest,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });

    // Obtener el módulo de fatigas/despliegues
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_FATIGAS);
    if (!systemModule) {
      throw new Error('System module CTRL_FATIGAS not found');
    }

    const result = await db.transaction(async (trx) => {
      const [count, existingSameShift, existingUserAssignment, existingActive] = await Promise.all([
        trx.select({ count: sql<number>`count(*)` }).from(systemModuleControlFatigue),
        trx
          .select()
          .from(systemModuleControlFatigue)
          .where(
            and(
              eq(systemModuleControlFatigue.fkShiftId, insertData.fkShiftId),
              eq(systemModuleControlFatigue.fatigueDate, insertData.fatigueDate),
            ),
          ),
        trx
          .select()
          .from(systemModuleControlFatigue)
          .where(
            and(
              eq(systemModuleControlFatigue.fkUserId, insertData.fkUserId),
              eq(systemModuleControlFatigue.fatigueDate, insertData.fatigueDate),
            ),
          ),
        insertData.status === 'active'
          ? trx.select().from(systemModuleControlFatigue).where(eq(systemModuleControlFatigue.status, 'active'))
          : Promise.resolve([]),
      ]);

      // Validaciones
      validateFatigueConditions(count[0].count, existingSameShift, existingUserAssignment, existingActive);

      // Insertar el nuevo despliegue
      const fatigueResult = await trx
        .insert(systemModuleControlFatigue)
        .values({
          ...insertData,
          isTemplate: count[0].count === 0,
          fkSystemModuleId: systemModule.id,
        })
        .returning({
          fkFatigueId: systemModuleControlFatigue.id,
          fkShiftId: systemModuleControlFatigue.fkShiftId,
        });

      const metadata: ShiftAssignMetadata = createBaseMetadata();

      // Crear asignaciones de usuarios a la fatiga,
      // si ya existen fatigas se creara la nueva fatiga a partir de la plantilla
      const { message, submittedUsers, assignedUsers, unassignedUsers, usersByShift } = await createShiftAssignments(
        trx,
        fatigueResult[0].fkFatigueId,
        fatigueResult[0].fkShiftId,
        arrayUsersIds,
        metadata,
        systemModuleCache,
        req.userId,
        req.dependencyId,
        count[0].count > 0,
      );
      metadata.message = message;
      metadata.submittedUsers = submittedUsers;
      metadata.assignedUsers = assignedUsers;
      metadata.unassignedUsers = unassignedUsers;
      metadata.usersByShift = usersByShift;

      return { fatigueResult, metadata };
    });
    res.status(HttpStatus.CREATED).json({ result: result.fatigueResult, metadata: result.metadata });
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const start = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { force: forceQS } = req.query;
  let force = false;
  if (forceQS && typeof forceQS === 'string') {
    force = forceQS === 'true';
  }
  try {
    const result = await db.transaction(async (trx) => {
      // Validar que exista el despliegue
      const existingFatigue = await trx
        .select()
        .from(systemModuleControlFatigue)
        .where(eq(systemModuleControlFatigue.id, id));
      if (existingFatigue.length === 0) {
        throw new Error('El despliegue no existe');
      }
      // Validar que no exista un despliegue activo
      const existingActiveFatigue = await trx
        .select()
        .from(systemModuleControlFatigue)
        .where(eq(systemModuleControlFatigue.status, 'active'));
      if (existingActiveFatigue.length > 0 && !force) {
        throw new Error('Ya existe un despliegue activo');
      }
      if (existingActiveFatigue.length > 0) {
        if (!force) {
          throw new Error(
            'Ya existe un despliegue activo. Use force=true para completar automaticamente el despliegue',
          );
        }
        await trx
          .update(systemModuleControlFatigue)
          .set({
            status: 'completed',
            fkLastUserId: req.userId,
            isClosed: true,
            closedAt: new Date(),
            fkLastDependencyId: req.dependencyId,
          })
          .where(eq(systemModuleControlFatigue.id, existingActiveFatigue[0].id));
      }
      // Actualizar Kardex de armas, equipamiento táctico (uniformes) y equipo táctico
      startRegisterInKardex(trx, id, req.userId || '', req.dependencyId || '', [
        'weapon',
        'uniform',
        'tactical_equipment',
      ]);

      // Actualizar el despliegue
      const updateResult = await trx
        .update(systemModuleControlFatigue)
        .set({ status: 'active', fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleControlFatigue.id, id));

      return updateResult;
    });
    res.status(HttpStatus.OK).json({ result });
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE}/${id} - start`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const end = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Obtener el despliegue
      const fatigue = await trx
        .select()
        .from(systemModuleControlFatigue)
        .where(eq(systemModuleControlFatigue.id, id))
        .limit(1);
      if (fatigue.length === 0) {
        throw new Error('El despliegue no existe');
      }
      // Validar que no este activo
      if (fatigue[0].status !== 'active') {
        throw new Error('El despliegue debe estar activo para finalizarse');
      }
      // Actualizar el despliegue
      await trx
        .update(systemModuleControlFatigue)
        .set({
          status: 'completed',
          isClosed: true,
          closedAt: new Date(moment().format()),
          fkLastUserId: req.userId,
          fkLastDependencyId: req.dependencyId,
        })
        .where(eq(systemModuleControlFatigue.id, id));
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE}/${id} - end`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    // Validar fecha
    const date = new Date(req.body.fatigueDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('La fecha "fatigueDate" no es una fecha valida');
    }

    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;

    const { arrayUsersIds, ...rest } = req.body;

    const updateData = await fatigueBodySchema.parseAsync({
      ...rest,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });

    const result = await db.transaction(async (trx) => {
      // No se puede actualizar el estatus del despliegue a activo desde el update
      if (updateData.status === 'active') {
        throw new Error('No se puede actualizar el estatus del despliegue a activo desde este proceso');
      }
      // Verificar que el despliegue exista
      const existingFatigue = await trx
        .select()
        .from(systemModuleControlFatigue)
        .where(eq(systemModuleControlFatigue.id, id));
      if (existingFatigue.length === 0) {
        throw new Error('El despliegue no existe');
      }
      if (existingFatigue[0].status > 'completed') {
        throw new Error('El despliegue ya esta completado');
      }
      if (existingFatigue[0].fkShiftId !== updateData.fkShiftId) {
        throw new Error('El despliegue no puede cambiar de turno');
      }
      // Verificar que no exista otro despliegue con el mismo tipo de turno y misma fecha (excluyendo el actual)
      const existingAnotherFatigue = await trx
        .select()
        .from(systemModuleControlFatigue)
        .where(
          and(
            eq(systemModuleControlFatigue.fkShiftId, updateData.fkShiftId),
            eq(systemModuleControlFatigue.fatigueDate, updateData.fatigueDate),
            not(eq(systemModuleControlFatigue.id, id)),
          ),
        );
      if (existingAnotherFatigue.length > 0) {
        throw new Error('Ya existe un despliegue con el mismo turno y fecha');
      }

      // Validar que el encargado del despliegue no esté asignado a otro despliegue en la misma fecha
      const existingFatigueAssignment = await trx
        .select()
        .from(systemModuleControlFatigue)
        .where(
          and(
            eq(systemModuleControlFatigue.fkUserId, updateData.fkUserId),
            eq(systemModuleControlFatigue.fatigueDate, updateData.fatigueDate),
            not(eq(systemModuleControlFatigue.id, id)),
          ),
        );
      if (existingFatigueAssignment.length > 0) {
        throw new Error('El encargado del despliegue ya está asignado a otro despliegue en la misma fecha');
      }

      // Si el despliegue es marcado como completado se actualiza "isClosed" a true y se toma la hora actual
      if (updateData.status === 'completed') {
        updateData.isClosed = true;
        updateData.closedAt = new Date();
      }

      // Validar si se intenta cambiar el template a falso cuando ya es verdadero
      if (existingFatigue[0].isTemplate && updateData.isTemplate === false) {
        throw new Error('No es posible quitar la condición de plantilla a un despliegue marcado como tal');
      }

      // Si se establece el despliegue como plantilla, se maneja el cambio
      if (updateData.isTemplate) {
        // Buscar la plantilla actual (si existe)
        const currentTemplate = await trx
          .select()
          .from(systemModuleControlFatigue)
          .where(and(eq(systemModuleControlFatigue.isTemplate, true), not(eq(systemModuleControlFatigue.id, id))))
          .limit(1);

        // Si existe actualmente un template, actualizarlo a false
        if (currentTemplate.length > 0) {
          await trx
            .update(systemModuleControlFatigue)
            .set({ isTemplate: false })
            .where(eq(systemModuleControlFatigue.id, currentTemplate[0].id));
        }
      }

      // Actualizar el despliegue
      const fatigueResult = await trx
        .update(systemModuleControlFatigue)
        .set({ ...updateData })
        .where(eq(systemModuleControlFatigue.id, id));

      const metadata = {
        message: 'Despliegue actualizado correctamente',
        submittedUsers: arrayUsersIds.length || 0,
        newAssignedUsers: [] as Omit<ShiftAssign, 'fkSystemModuleId'>[],
        updateAssignedUsers: [] as Omit<ShiftAssign, 'fkSystemModuleId'>[],
        deleteAssignedUsers: [] as Omit<ShiftAssign, 'fkSystemModuleId'>[],
      };

      // Actualizar usuarios del despliegue
      if ((arrayUsersIds.length || 0) > 0) {
        // Obtener el módulo de fatigas/despliegues
        const systemModuleCache = SystemModuleCache.getInstance();
        const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_FATIGAS);
        if (!systemModule) {
          throw new Error('System module CTRL_FATIGAS not found');
        }
        const systemModuleShiftAssign = systemModuleCache.getSystemModuleByKey(
          SystemModuleKeys.CTRL_ASIGNACIONES_TURNOS,
        );
        if (systemModuleShiftAssign) {
          const { toCreate, toUpdate, toDelete } = await identifyAssignments(arrayUsersIds || [], id);
          if (toCreate.length > 0) {
            const toCreateMap = toCreate.map((m) => {
              if (!req.userId || !req.dependencyId) {
                throw new Error('userId or dependencyId is undefined');
              }
              return {
                ...m,
                fkFatigueId: id,
                fkSystemModuleId: systemModuleShiftAssign.id,
                fkLastUserId: req.userId,
                fkLastDependencyId: req.dependencyId,
              };
            });
            // Insertar asignaciones
            await trx.insert(systemModuleAssignmentShift).values(toCreateMap);
            metadata.newAssignedUsers = toCreate;
          }
          if (toUpdate.length > 0) {
            const promises = [];
            for (const toUpdateItem of toUpdate) {
              const updateUserFatigueAssign = await assignShiftBodySchema.parseAsync({
                ...toUpdateItem,
                fkLastUserId: req.userId,
                fkLastDependencyId: req.dependencyId,
              });
              promises.push(
                trx
                  .update(systemModuleAssignmentShift)
                  .set({ ...updateUserFatigueAssign })
                  .where(
                    and(
                      eq(systemModuleAssignmentShift.fkUserId, toUpdateItem.fkUserId),
                      eq(systemModuleAssignmentShift.fkFatigueId, id),
                    ),
                  ),
              );
            }
            await Promise.all(promises);
            metadata.updateAssignedUsers = toUpdate;
          }
          if (toDelete.length > 0) {
            for (const toDeleteItem of toDelete) {
              // Eliminar asignaciones
              await trx
                .delete(systemModuleAssignmentShift)
                .where(
                  and(
                    eq(systemModuleAssignmentShift.fkUserId, toDeleteItem.fkUserId),
                    eq(systemModuleAssignmentShift.fkFatigueId, id),
                  ),
                );
            }
            metadata.deleteAssignedUsers = toDelete;
          }
        } else {
          throw new Error('No fue posible asignar usuarios al despliegue');
        }
      }
      return { fatigueResult, metadata };
    });

    res.status(HttpStatus.OK).json({ result: result.fatigueResult, metadata: result.metadata });
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    // TODO: Validar no se puede eliminar un turno que está asignado a una fatiga en activo o programado
    const result = await db.transaction(async (trx) => {
      // Validar que exista el despliegue
      const existingFatigue = await trx
        .select()
        .from(systemModuleControlFatigue)
        .where(eq(systemModuleControlFatigue.id, id));
      if (existingFatigue.length === 0) {
        throw new Error('El despliegue no existe');
      }
      // Validar que el despliegue no este activo
      if (existingFatigue[0].status > 'active') {
        throw new Error('El despliegue está activo');
      }
      await trx
        .update(systemModuleControlFatigue)
        .set({ isDeleted: true, deletedAt: new Date(), fkLastUserId: req.userId, fkLastDependencyId: req.dependencyId })
        .where(eq(systemModuleControlFatigue.id, id));
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    q: qQS,
    assigned: assignedQS,
    grouped: groupedQS,
    groupedBy: groupedByQS,
    startDate: startDateQS,
    endDate: endDateQS,
  } = req.query;
  try {
    // Parsear parámetros de consulta
    const lastId = parseValueQueryParam(lastIdQS, null);
    const limit = parseNumberQueryParam(limitQS, 10);
    const deleted = parseBooleanQueryParam(deletedQS, false);
    const disabled = parseBooleanQueryParam(disabledQS, false);
    const q = parseValueQueryParam(qQS, null);
    const assigned = parseBooleanQueryParam(assignedQS, false);
    const grouped = parseBooleanQueryParam(groupedQS, false);
    const groupedBy = parseFatigueGroupByParam(groupedByQS);
    const startDate = parseDateQueryParam(startDateQS);
    const endDate = parseDateQueryParam(endDateQS);
    if (grouped && !groupedBy) {
      throw new Error(
        `El parámetro groupedBy es obligatorio cuando se utiliza el parámetro grouped. Si lo esta enviando, el valor debe pertenecer a la siguiente lista: ${Object.keys(replaceFatigueGroupByField).join(', ')}`,
      );
    }
    if (startDate && endDate && startDate > endDate) {
      throw new Error('La fecha de inicio no puede ser mayor que la fecha de fin');
    }
    // Filtros básicos
    const filters: SQL[] = [
      !deleted && eq(systemModuleControlFatigue.isDeleted, false),
      !disabled && eq(systemModuleControlFatigue.isEnabled, true),
      lastId && gt(systemModuleControlFatigue.id, lastId),
    ].filter(Boolean) as SQL[];
    // Filtros de búsqueda textual
    const likeFilters: SQL[] = [
      q && like(systemModuleUser.alias, `%${q}%`),
      q && like(systemModuleControlShift.shiftName, `%${q}%`),
      q && like(sql`${systemModuleControlFatigue.fatigueDate}::text`, `%${q}%`),
      q && like(sql`${systemModuleControlShift.startTime}::text`, `%${q}%`),
    ].filter(Boolean) as SQL[];
    // Filtro por rango de fechas
    if (startDate && endDate) {
      filters.push(between(systemModuleControlFatigue.fatigueDate, startDate, endDate));
    } else if (startDate) {
      filters.push(gte(systemModuleControlFatigue.fatigueDate, startDate));
    } else if (endDate) {
      filters.push(lte(systemModuleControlFatigue.fatigueDate, endDate));
    }

    const fatigueItems = await db
      .select({
        id: systemModuleControlFatigue.id,
        fkUserId: systemModuleControlFatigue.fkUserId,
        aliasUser: systemModuleUser.alias,
        fkShiftId: systemModuleControlFatigue.fkShiftId,
        shiftName: systemModuleControlShift.shiftName,
        fatigueDate: systemModuleControlFatigue.fatigueDate,
        fatigueStartTime: systemModuleControlShift.startTime,
        fatigueEndTime: systemModuleControlShift.endTime,
        status: systemModuleControlFatigue.status,
        isClosed: systemModuleControlFatigue.isClosed,
        closedAt: systemModuleControlFatigue.closedAt,
      })
      .from(systemModuleControlFatigue)
      .leftJoin(systemModuleUser, eq(systemModuleControlFatigue.fkUserId, systemModuleUser.id))
      .leftJoin(systemModuleControlShift, eq(systemModuleControlFatigue.fkShiftId, systemModuleControlShift.id))
      .where(and(...filters, or(...likeFilters)))
      .groupBy(
        systemModuleControlFatigue.id,
        systemModuleUser.alias,
        systemModuleControlShift.shiftName,
        systemModuleControlShift.startTime,
        systemModuleControlShift.endTime,
      )
      .limit(limit);

    // biome-ignore lint/suspicious/noExplicitAny: <explanation>
    const fatigueMap = new Map<string, any>();
    for (const item of fatigueItems) {
      fatigueMap.set(item.id, { ...item, assignedUsers: [] });
    }
    if (assigned) {
      const assignedUsersByFatigue = await db
        .select({
          fkFatigueId: systemModuleAssignmentShift.fkFatigueId,
          assignedUsers: sql<AssignedUser[]>`jsonb_agg(jsonb_build_object(
          'fkUserId', ${systemModuleAssignmentShift.fkUserId},
          'alias', ${systemModuleUser.alias},
          'checkInTime', ${systemModuleAssignmentShift.checkInTime},
          'isLate', ${systemModuleAssignmentShift.isLate},
          'isDenied', ${systemModuleAssignmentShift.isDenied},
          'denialReason', ${systemModuleAssignmentShift.denialReason},
          'status', ${systemModuleAssignmentShift.status}
        ))`,
        })
        .from(systemModuleAssignmentShift)
        .leftJoin(systemModuleUser, eq(systemModuleAssignmentShift.fkUserId, systemModuleUser.id))
        .groupBy(systemModuleAssignmentShift.fkFatigueId);

      for (const item of assignedUsersByFatigue) {
        const existing = fatigueMap.get(item.fkFatigueId);
        if (existing) {
          existing.assignedUsers = item.assignedUsers;
          existing.assignedQuantity = item.assignedUsers.length;
        }
      }
    }

    const finalItems = Array.from(fatigueMap.values());

    if (grouped && groupedBy) {
      const groupedData: Record<string, GroupedItem> = {};
      for (const item of finalItems) {
        const groupKey = item[groupedBy];
        if (!groupedData[groupKey]) {
          const resolver = groupFatigueNameResolvers[groupedBy];
          const groupName = resolver ? await resolver(groupKey) : null;
          groupedData[groupKey] = {
            groupKey,
            groupName,
            groupType: getKeyFromValue(groupedBy, replaceFatigueGroupByField) ?? groupedBy,
            quantityItems: 0,
            items: [],
          };
        }
        groupedData[groupKey].quantityItems += 1;
        groupedData[groupKey].items.push(item);
      }
      const groupedResult = Object.values(groupedData);
      res.status(HttpStatus.OK).json(groupedResult);
      return;
    }
    res.status(HttpStatus.OK).json(finalItems);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPlaces = async (req: Request, res: Response) => {
  const { lastId: lastIdQS, limit: limitQS, q: qQS } = req.query;
  let lastId = null;
  let limit = 10;
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filtersE: (SQL | undefined)[] = [];
    const filtersQ: (SQL | undefined)[] = [];
    const filtersOr: (SQL | undefined)[] = [];
    filtersE.push(
      eq(systemModuleControlFatiguePlace.isDeleted, false),
      eq(systemModuleControlFatiguePlace.isEnabled, true),
    );
    filtersQ.push(
      eq(systemModuleControlOperationQuadrant.isDeleted, false),
      eq(systemModuleControlOperationQuadrant.isEnabled, true),
    );
    if (q) {
      filtersE.push(
        like(
          sql`normalize_text(coalesce(${systemModuleControlFatiguePlace.name}))`,
          sql`'%' || normalize_text(${q}) || '%'`,
        ),
      );
      filtersOr.push(
        sql`
            ${systemModuleControlFatiguePlace.properties} != '{}'::jsonb AND 
            EXISTS (
              SELECT 1 FROM jsonb_each_text(${systemModuleControlFatiguePlace.properties})
              WHERE value LIKE '%' || ${q} || '%'
            )`,
      );
      filtersQ.push(
        like(
          sql`normalize_text(coalesce(${systemModuleControlOperationQuadrant.quadrantName}))`,
          sql`'%' || normalize_text(${q}) || '%'`,
        ),
      );
    }

    if (lastId) {
      filtersE.push(gt(systemModuleControlFatiguePlace.id, lastId));
      filtersQ.push(gt(systemModuleControlOperationQuadrant.id, lastId));
    }

    const [fatiguePlaces, operationQuadrants] = await Promise.all([
      db
        .select({
          id: systemModuleControlFatiguePlace.id,
          name: systemModuleControlFatiguePlace.name,
          type: sql`'place'`.as('type'),
          fkFatigueCategoryId: systemModuleControlFatiguePlace.fkFatigueCategoryId,
          properties: systemModuleControlFatiguePlace.properties,
        })
        .from(systemModuleControlFatiguePlace)
        .where(or(...filtersOr, and(...filtersE)))
        .orderBy(asc(systemModuleControlFatiguePlace.id))
        .limit(limit),
      db
        .select({
          id: systemModuleControlOperationQuadrant.id,
          name: systemModuleControlOperationQuadrant.quadrantName,
          type: sql`'quadrant'`.as('type'),
          operationalArea: systemModuleControlOperationQuadrant.operationalArea,
          geometry: systemModuleControlOperationQuadrant.geometry,
          displayColor: systemModuleControlOperationQuadrant.displayColor,
          displayStyle: systemModuleControlOperationQuadrant.displayStyle,
        })
        .from(systemModuleControlOperationQuadrant)
        .where(and(...filtersQ))
        .orderBy(asc(systemModuleControlOperationQuadrant.id))
        .limit(limit),
    ]);
    const allResults = [...fatiguePlaces, ...operationQuadrants];
    allResults.sort((a, b) => {
      const idA = String(a.id);
      const idB = String(b.id);
      return idA.localeCompare(idB);
    });
    const paginatedResults = allResults.slice(0, limit);
    const newLastId = paginatedResults.length > 0 ? paginatedResults[paginatedResults.length - 1].id : null;
    res.status(HttpStatus.OK).json({
      data: paginatedResults,
      lastId: newLastId,
      hasMore: paginatedResults.length === limit && allResults.length > paginatedResults.length,
    });
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.FATIGUE}/places - searchPlaces`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
