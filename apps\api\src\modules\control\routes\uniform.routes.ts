import { Router } from 'express';
import { create, deleteOne, getAll, getOne, searchPaginated, update } from '../controllers/uniform.controller';

const uniformRouter = Router();

uniformRouter.get('/', getAll);
uniformRouter.get('/search', searchPaginated);
uniformRouter.get('/:id', getOne);
uniformRouter.post('/', create);
uniformRouter.put('/:id', update);
uniformRouter.delete('/:id', deleteOne);

export default uniformRouter;
