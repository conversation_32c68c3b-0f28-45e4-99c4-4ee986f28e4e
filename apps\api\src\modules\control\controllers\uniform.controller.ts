import type { Request, Response } from 'express';
import db from '@/db';
import { and, asc, desc, eq, gt, isNull, like, or, sql, type SQL } from 'drizzle-orm';
import { Logger } from '@/utils/logger';
import { getLocale } from '@/utils/locale_validator';
import { HttpStatus } from '@/utils/http_status';
import { SystemModuleKeys } from '@/utils/system_module_keys';
import { ModuleName } from '@/utils/module_names';
import z from 'zod';
import SystemModuleCache from '@/utils/system_module_cache';
import {
  catColor,
  catSize,
  catUniformType,
  systemModuleAssignmentUniform,
  systemModuleControlSupplier,
  systemModuleControlUniform,
  systemModuleUserKardexTacticalEquipment,
} from '@repo/shared-drizzle/schemas';
import { uniformBodySchema } from '../helpers/uniform.helper';

export const getAll = async (req: Request, res: Response) => {
  const { deleted: deletedQS, disabled: disabledQS, locale: localeQS } = req.query;
  let [deleted, disabled, locale] = [false, false, getLocale()];

  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const filters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlUniform.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlUniform.isEnabled, true));
    }
    const result = await db
      .select({
        id: systemModuleControlUniform.id,
        serialNumber: systemModuleControlUniform.serialNumber,
        fkUniformTypeId: systemModuleControlUniform.fkUniformTypeId,
        nameUniformType: catUniformType[locale],
        fkSizeId: systemModuleControlUniform.fkSizeId,
        nameSize: catSize[locale],
        fkColorId: systemModuleControlUniform.fkColorId,
        nameColor: catColor[locale],
        status: systemModuleControlUniform.status,
        expirationDate: systemModuleControlUniform.expirationDate,
        acquisitionDate: systemModuleControlUniform.acquisitionDate,
        cost: systemModuleControlUniform.cost,
        fkSupplierId: systemModuleControlUniform.fkSupplierId,
        nameSupplier: systemModuleControlSupplier.name,
        description: systemModuleControlUniform.description,
      })
      .from(systemModuleControlUniform)
      .leftJoin(catUniformType, eq(systemModuleControlUniform.fkUniformTypeId, catUniformType.id))
      .leftJoin(catSize, eq(systemModuleControlUniform.fkSizeId, catSize.id))
      .leftJoin(catColor, eq(systemModuleControlUniform.fkColorId, catColor.id))
      .leftJoin(
        systemModuleControlSupplier,
        eq(systemModuleControlUniform.fkSupplierId, systemModuleControlSupplier.id),
      )
      .where(and(...filters));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIFORM}/all - getAll`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const getOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  const { locale: localeQS } = req.query;
  let locale = getLocale();

  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }

  try {
    const result = await db
      .select({
        id: systemModuleControlUniform.id,
        serialNumber: systemModuleControlUniform.serialNumber,
        fkUniformTypeId: systemModuleControlUniform.fkUniformTypeId,
        nameUniformType: catUniformType[locale],
        fkSizeId: systemModuleControlUniform.fkSizeId,
        nameSize: catSize[locale],
        fkColorId: systemModuleControlUniform.fkColorId,
        nameColor: catColor[locale],
        status: systemModuleControlUniform.status,
        expirationDate: systemModuleControlUniform.expirationDate,
        acquisitionDate: systemModuleControlUniform.acquisitionDate,
        cost: systemModuleControlUniform.cost,
        fkSupplierId: systemModuleControlUniform.fkSupplierId,
        nameSupplier: systemModuleControlSupplier.name,
        description: systemModuleControlUniform.description,
      })
      .from(systemModuleControlUniform)
      .leftJoin(catUniformType, eq(systemModuleControlUniform.fkUniformTypeId, catUniformType.id))
      .leftJoin(catSize, eq(systemModuleControlUniform.fkSizeId, catSize.id))
      .leftJoin(catColor, eq(systemModuleControlUniform.fkColorId, catColor.id))
      .leftJoin(
        systemModuleControlSupplier,
        eq(systemModuleControlUniform.fkSupplierId, systemModuleControlSupplier.id),
      )
      .where(eq(systemModuleControlUniform.id, id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIFORM}/${id} - getOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const create = async (req: Request, res: Response) => {
  try {
    // Validar assignmentDate
    if (req.body.expirationDate) {
      const date = new Date(req.body.expirationDate.replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(date.getTime())) {
        throw new Error('El campo expirationDate no es una fecha valida');
      }
    }
    // Validar acquisitionDate
    const date = new Date(req.body.acquisitionDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('El campo acquisitionDate no es una fecha valida');
    }
    const insertData = uniformBodySchema.parse({
      ...req.body,
      fkLastUserId: req.userId,
      fkLastDependencyId: req.dependencyId,
    });
    const systemModuleCache = SystemModuleCache.getInstance();
    const systemModule = systemModuleCache.getSystemModuleByKey(SystemModuleKeys.CTRL_UNIFORMES);
    if (!systemModule) {
      throw new Error('System module CTRL_UNIFORMES not found');
    }
    const result = await db.insert(systemModuleControlUniform).values({
      ...insertData,
      fkSystemModuleId: systemModule.id,
    });
    res.status(HttpStatus.CREATED).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIFORM} - create`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const update = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    req.body.fkLastUserId = req.userId;
    req.body.fkLastDependencyId = req.dependencyId;
    // Validar assignmentDate
    if (req.body.expirationDate) {
      const date = new Date(req.body.expirationDate.replace(/\//g, '-')); // Convertir a formato ISO
      if (Number.isNaN(date.getTime())) {
        throw new Error('El campo expirationDate no es una fecha valida');
      }
    }
    // Validar acquisitionDate
    const date = new Date(req.body.acquisitionDate.replace(/\//g, '-')); // Convertir a formato ISO
    if (Number.isNaN(date.getTime())) {
      throw new Error('El campo acquisitionDate no es una fecha valida');
    }
    // Validar payload
    const updateData = await uniformBodySchema.parseAsync({ ...req.body });
    const result = await db.transaction(async (trx) => {
      // Obtener datos de la uniforme
      const uniform = await trx
        .select()
        .from(systemModuleControlUniform)
        .where(eq(systemModuleControlUniform.id, id))
        .limit(1);
      if (uniform.length === 0) {
        throw new Error('Uniforme no encontrado');
      }

      // Obtener la asignación activa del uniforme
      const activeUniformAssignment = await trx
        .select()
        .from(systemModuleAssignmentUniform)
        .where(
          and(
            eq(systemModuleAssignmentUniform.fkUniformId, id),
            isNull(systemModuleAssignmentUniform.assignmentEndDate),
          ),
        )
        .limit(1);

      if (activeUniformAssignment.length > 0) {
        // Definir los campos a verificar como un array de strings literales
        const fieldsToCheck = ['expirationDate', 'fkSizeId', 'status'] as const;

        // Extraer el tipo de las claves
        type FieldsToCheck = (typeof fieldsToCheck)[number];

        // Verificar si alguno de los campos relevantes cambió
        const hasChanged = fieldsToCheck.some(
          (field) => updateData[field as FieldsToCheck] !== uniform[0][field as FieldsToCheck],
        );

        if (hasChanged) {
          // Obtener el último kardex del usuario asignado
          const lastKardex = await trx
            .select()
            .from(systemModuleUserKardexTacticalEquipment)
            .where(
              and(
                eq(systemModuleUserKardexTacticalEquipment.fkUserId, activeUniformAssignment[0].fkUserId),
                eq(systemModuleUserKardexTacticalEquipment.fkUniformId, id),
                isNull(systemModuleUserKardexTacticalEquipment.assignmentEndDate),
              ),
            )
            .orderBy(desc(systemModuleUserKardexTacticalEquipment.createdAt))
            .limit(1);

          if (lastKardex.length > 0) {
            // Actualizar el último kardex del usuario asignado
            await trx
              .update(systemModuleUserKardexTacticalEquipment)
              .set({
                expirationDate: updateData.expirationDate,
                fkSizeId: updateData.fkSizeId,
                tacticalEquipmentStatus: updateData.status,
                fkLastUserId: updateData.fkLastUserId,
                fkLastDependencyId: updateData.fkLastDependencyId,
              })
              .where(eq(systemModuleUserKardexTacticalEquipment.id, lastKardex[0].id));
          }
        }
      }

      // Actualizar el uniforme
      const updateResult = await trx
        .update(systemModuleControlUniform)
        .set({ ...updateData })
        .where(eq(systemModuleControlUniform.id, id));

      return updateResult;
    });
    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIFORM}/${id} - update`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    if (error instanceof z.ZodError) {
      const { errors } = error;
      res.status(HttpStatus.BAD_REQUEST).json({ errors });
      return;
    }
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const deleteOne = async (req: Request, res: Response) => {
  const { id } = req.params;
  try {
    const result = await db.transaction(async (trx) => {
      // Verificar si el uniforme está asignado actualmente a un usuario
      const activeUniformAssignment = await trx
        .select()
        .from(systemModuleAssignmentUniform)
        .where(
          and(
            eq(systemModuleAssignmentUniform.fkUniformId, id),
            isNull(systemModuleAssignmentUniform.assignmentEndDate),
          ),
        )
        .limit(1);

      if (activeUniformAssignment.length > 0) {
        throw new Error('No se puede eliminar el uniforme porque está asignado actualmente a un elemento');
      }

      // Marcar el uniforme como eliminado
      const deleteResult = await trx
        .update(systemModuleControlUniform)
        .set({
          isDeleted: true,
          deletedAt: new Date(),
          fkLastUserId: req.userId,
          fkLastDependencyId: req.dependencyId,
        })
        .where(eq(systemModuleControlUniform.id, id));

      return deleteResult;
    });

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIFORM}/${id} - deleteOne`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};

export const searchPaginated = async (req: Request, res: Response) => {
  const {
    lastId: lastIdQS,
    limit: limitQS,
    deleted: deletedQS,
    disabled: disabledQS,
    locale: localeQS,
    q: qQS,
  } = req.query;

  let lastId = null;
  let limit = 10;
  let deleted = false;
  let disabled = false;
  let locale = getLocale();
  let q = null;

  if (lastIdQS && typeof lastIdQS === 'string') {
    lastId = lastIdQS;
  }
  if (limitQS && typeof limitQS === 'string') {
    limit = limitQS ? Number.parseInt(limitQS) : 10;
  }
  if (deletedQS && typeof deletedQS === 'string') {
    deleted = deletedQS === 'true';
  }
  if (disabledQS && typeof disabledQS === 'string') {
    disabled = disabledQS === 'true';
  }
  if (localeQS && typeof localeQS === 'string') {
    locale = getLocale(localeQS);
  }
  if (qQS && typeof qQS === 'string') {
    q = qQS;
  }

  try {
    const filters: SQL[] = [];
    const likeFilters: SQL[] = [];
    if (!deleted) {
      filters.push(eq(systemModuleControlUniform.isDeleted, false));
    }
    if (!disabled) {
      filters.push(eq(systemModuleControlUniform.isEnabled, true));
    }
    if (q) {
      likeFilters.push(like(systemModuleControlUniform.serialNumber, `%${q}%`));
      likeFilters.push(like(catUniformType[locale], `%${q}%`));
      likeFilters.push(like(catSize[locale], `%${q}%`));
      likeFilters.push(like(catColor[locale], `%${q}%`));
      likeFilters.push(like(systemModuleControlUniform.status, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlUniform.expirationDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlUniform.acquisitionDate}::text`, `%${q}%`));
      likeFilters.push(like(sql`${systemModuleControlUniform.cost}::text`, `%${q}%`));
      likeFilters.push(like(systemModuleControlSupplier.name, `%${q}%`));
      likeFilters.push(like(systemModuleControlUniform.description, `%${q}%`));
    }

    if (lastId) {
      filters.push(gt(systemModuleControlUniform.id, lastId));
    }
    const result = await db
      .select({
        id: systemModuleControlUniform.id,
        serialNumber: systemModuleControlUniform.serialNumber,
        fkUniformTypeId: systemModuleControlUniform.fkUniformTypeId,
        nameUniformType: catUniformType[locale],
        fkSizeId: systemModuleControlUniform.fkSizeId,
        nameSize: catSize[locale],
        fkColorId: systemModuleControlUniform.fkColorId,
        nameColor: catColor[locale],
        status: systemModuleControlUniform.status,
        expirationDate: systemModuleControlUniform.expirationDate,
        acquisitionDate: systemModuleControlUniform.acquisitionDate,
        cost: systemModuleControlUniform.cost,
        fkSupplierId: systemModuleControlUniform.fkSupplierId,
        nameSupplier: systemModuleControlSupplier.name,
        description: systemModuleControlUniform.description,
      })
      .from(systemModuleControlUniform)
      .leftJoin(catUniformType, eq(systemModuleControlUniform.fkUniformTypeId, catUniformType.id))
      .leftJoin(catSize, eq(systemModuleControlUniform.fkSizeId, catSize.id))
      .leftJoin(catColor, eq(systemModuleControlUniform.fkColorId, catColor.id))
      .leftJoin(
        systemModuleControlSupplier,
        eq(systemModuleControlUniform.fkSupplierId, systemModuleControlSupplier.id),
      )
      .where(and(...filters, or(...likeFilters)))
      .limit(limit)
      .orderBy(asc(systemModuleControlUniform.id));

    res.status(HttpStatus.OK).json(result);
    return;
  } catch (error) {
    const { message, stack } = error as Error;
    Logger.getInstance().error(message, {
      stack,
      method: `${ModuleName.UNIFORM} - searchPaginated`,
      source: req.headers['user-agent'],
      module: ModuleName.CONTROL,
    });
    res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({ message });
    return;
  }
};
